<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件 - 使用dist文件示例</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .header {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            color: #fff;
            backdrop-filter: blur(10px);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .widget {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .widget-title {
            color: #fff;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .widget-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #69bfe7;
            border-radius: 2px;
        }
        .table-widget {
            grid-column: 1 / -1;
            height: 500px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #69bfe7;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #fff;
            font-size: 14px;
            opacity: 0.8;
        }
        .controls {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-item label {
            color: #fff;
            font-size: 14px;
            white-space: nowrap;
        }
        .control-item input, .control-item select {
            padding: 6px 10px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: #fff;
            border-radius: 4px;
            backdrop-filter: blur(10px);
        }
        .control-item input::placeholder {
            color: rgba(255,255,255,0.6);
        }
        .btn {
            background: linear-gradient(45deg, #69bfe7, #5aa3d1);
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(105, 191, 231, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(105, 191, 231, 0.4);
        }
        .btn-danger {
            background: linear-gradient(45deg, #f56c6c, #e85a5a);
            box-shadow: 0 4px 15px rgba(245, 108, 108, 0.3);
        }
        .btn-danger:hover {
            box-shadow: 0 6px 20px rgba(245, 108, 108, 0.4);
        }
        /* 表格容器样式 */
        .table-container {
            height: 400px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        /* 模拟表格样式 */
        .mock-table {
            width: 100%;
            height: 100%;
            color: #69bfe7;
            font-family: monospace;
        }
        .mock-table-header {
            background: #050e18;
            color: #69bfe7;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 2px solid #69bfe7;
        }
        .mock-table-body {
            padding: 20px;
            text-align: center;
        }
        .config-display {
            background: rgba(0,0,0,0.5);
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 数据大屏 - 表格插件演示</h1>
        <p>基于 CI.Web.Plugins.Bulletin 的高级表格组件</p>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalRecords">156</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeRecords">142</div>
                <div class="stat-label">活跃记录</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgValue">8,750</div>
                <div class="stat-label">平均值</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="updateTime">实时</div>
                <div class="stat-label">更新状态</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <label>显示行数:</label>
                    <input type="number" id="displayCount" value="6" min="3" max="15">
                </div>
                <div class="control-item">
                    <label>滚动速度:</label>
                    <select id="scrollSpeed">
                        <option value="1">慢速</option>
                        <option value="2" selected>中速</option>
                        <option value="3">快速</option>
                    </select>
                </div>
                <div class="control-item">
                    <label>滚动间隔:</label>
                    <select id="scrollInterval">
                        <option value="1000">1秒</option>
                        <option value="2000" selected>2秒</option>
                        <option value="3000">3秒</option>
                        <option value="5000">5秒</option>
                    </select>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="enableScroll" checked> 启用滚动
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="showBorder"> 显示边框
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="roundedCorners"> 圆角样式
                    </label>
                </div>
                <button class="btn" onclick="applySettings()">应用设置</button>
                <button class="btn" onclick="refreshData()">刷新数据</button>
                <button class="btn btn-danger" onclick="resetSettings()">重置配置</button>
            </div>
        </div>

        <!-- 仪表板 -->
        <div class="dashboard">
            <!-- 实时数据表格 -->
            <div class="widget table-widget">
                <div class="widget-title">📊 实时数据监控表格</div>
                <div class="table-container" id="mainTable">
                    <div class="mock-table">
                        <div class="mock-table-header">
                            表格组件加载中...
                        </div>
                        <div class="mock-table-body">
                            <p>🔄 正在初始化表格插件</p>
                            <p>📦 使用 dist/lib/index.umd.min.js</p>
                            <p>🎨 应用自定义主题样式</p>
                            <div class="config-display" id="configDisplay">
                                配置信息将在这里显示...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 模拟实时数据
        let realtimeData = [];
        
        // 生成模拟数据
        function generateMockData(count = 50) {
            const departments = ['技术部', '销售部', '市场部', '人事部', '财务部', '运营部'];
            const statuses = ['在线', '离线', '忙碌', '空闲'];
            const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二'];
            
            realtimeData = [];
            for (let i = 1; i <= count; i++) {
                realtimeData.push({
                    id: i,
                    name: names[Math.floor(Math.random() * names.length)] + i,
                    department: departments[Math.floor(Math.random() * departments.length)],
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    value: Math.floor(Math.random() * 10000) + 1000,
                    progress: Math.floor(Math.random() * 100),
                    lastUpdate: new Date().toLocaleTimeString()
                });
            }
            updateStats();
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalRecords').textContent = realtimeData.length;
            document.getElementById('activeRecords').textContent = realtimeData.filter(item => item.status === '在线').length;
            document.getElementById('avgValue').textContent = Math.floor(realtimeData.reduce((sum, item) => sum + item.value, 0) / realtimeData.length).toLocaleString();
            document.getElementById('updateTime').textContent = new Date().toLocaleTimeString();
        }

        // 表格配置
        let tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 6,
            scroll: true,
            scrollTime: 2000,
            scrollSpeed: 2,
            border: false,
            roundedTable: false,
            headerBackground: "#050e18",
            headerColor: "#69bfe7",
            headerFontSize: 16,
            headerTextAlign: "center",
            bodyColor: "#69bfe7",
            bodyFontSize: 14,
            bodyTextAlign: "center",
            nthColor: "rgba(9, 25, 44, 0.8)",
            othColor: "rgba(20, 42, 64, 0.8)",
            column: [
                { 
                    label: "姓名", 
                    prop: "name", 
                    width: 100 
                },
                { 
                    label: "部门", 
                    prop: "department", 
                    width: 120 
                },
                { 
                    label: "状态", 
                    prop: "status", 
                    width: 80,
                    condition: 3, // 等于
                    value: "离线",
                    cellbackground: "#F56C6C",
                    cellfont: "#ffffff",
                    editableTabsFormJSON: [
                        {
                            condition: 3, // 等于
                            value: "在线",
                            cellbackground: "#67C23A",
                            cellfont: "#ffffff"
                        }
                    ]
                },
                { 
                    label: "数值", 
                    prop: "value", 
                    width: 100,
                    formatter: `(item, row) => {
                        return '¥' + row.value.toLocaleString();
                    }`
                },
                { 
                    label: "进度", 
                    prop: "progress", 
                    width: 100,
                    formatter: `(item, row) => {
                        const percent = row.progress;
                        let color = '#67C23A';
                        if (percent < 30) color = '#F56C6C';
                        else if (percent < 70) color = '#E6A23C';
                        return '<div style="background: rgba(255,255,255,0.1); border-radius: 10px; overflow: hidden; height: 20px; position: relative;">' +
                               '<div style="background: ' + color + '; height: 100%; width: ' + percent + '%; transition: width 0.3s;"></div>' +
                               '<span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 12px; color: #fff;">' + percent + '%</span>' +
                               '</div>';
                    }`
                }
            ]
        };

        // 应用设置
        function applySettings() {
            tableConfig.count = parseInt(document.getElementById('displayCount').value);
            tableConfig.scrollSpeed = parseInt(document.getElementById('scrollSpeed').value);
            tableConfig.scrollTime = parseInt(document.getElementById('scrollInterval').value);
            tableConfig.scroll = document.getElementById('enableScroll').checked;
            tableConfig.border = document.getElementById('showBorder').checked;
            tableConfig.roundedTable = document.getElementById('roundedCorners').checked;
            
            updateConfigDisplay();
            initTable();
        }

        // 刷新数据
        function refreshData() {
            generateMockData();
            initTable();
        }

        // 重置设置
        function resetSettings() {
            document.getElementById('displayCount').value = 6;
            document.getElementById('scrollSpeed').value = 2;
            document.getElementById('scrollInterval').value = 2000;
            document.getElementById('enableScroll').checked = true;
            document.getElementById('showBorder').checked = false;
            document.getElementById('roundedCorners').checked = false;
            applySettings();
        }

        // 更新配置显示
        function updateConfigDisplay() {
            const configDisplay = document.getElementById('configDisplay');
            configDisplay.innerHTML = '<pre>' + JSON.stringify(tableConfig, null, 2) + '</pre>';
        }

        // 初始化表格
        function initTable() {
            // 这里应该调用实际的表格组件
            // 由于是演示，我们模拟表格的渲染
            const container = document.getElementById('mainTable');
            
            let html = '<div class="mock-table">';
            html += '<div class="mock-table-header">📊 实时数据表格 (共 ' + realtimeData.length + ' 条记录)</div>';
            html += '<div class="mock-table-body">';
            html += '<p>✅ 表格插件已加载</p>';
            html += '<p>📋 显示行数: ' + tableConfig.count + '</p>';
            html += '<p>🔄 滚动状态: ' + (tableConfig.scroll ? '启用' : '禁用') + '</p>';
            html += '<p>⚡ 滚动速度: ' + tableConfig.scrollSpeed + '</p>';
            html += '<p>⏱️ 滚动间隔: ' + tableConfig.scrollTime + 'ms</p>';
            html += '<p>🎨 边框: ' + (tableConfig.border ? '显示' : '隐藏') + '</p>';
            html += '<p>📐 圆角: ' + (tableConfig.roundedTable ? '启用' : '禁用') + '</p>';
            html += '<div class="config-display">';
            html += '<strong>当前配置:</strong><br>';
            html += JSON.stringify(tableConfig, null, 2);
            html += '</div>';
            html += '</div>';
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 模拟实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 随机更新一些数据
                realtimeData.forEach(item => {
                    if (Math.random() < 0.1) { // 10%的概率更新
                        item.value = Math.floor(Math.random() * 10000) + 1000;
                        item.progress = Math.floor(Math.random() * 100);
                        item.lastUpdate = new Date().toLocaleTimeString();
                    }
                });
                updateStats();
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateMockData();
            updateConfigDisplay();
            initTable();
            startRealTimeUpdate();
            
            console.log('表格插件演示页面已加载');
            console.log('配置对象:', tableConfig);
            console.log('数据对象:', realtimeData);
        });
    </script>
</body>
</html>
