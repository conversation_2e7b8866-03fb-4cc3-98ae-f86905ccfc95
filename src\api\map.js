

import { url ,urls} from '@/config';
import { config } from '@/option/config'
import request from '../axios'
export const getList = (params) => request({
  url: urls + '/map/list',
  method: 'get',
  params: params
});

export const GetPageListByQueryJson = (params) => request({
  url: url + 'api/kanban/kanbanMap/GetAll',
  method: 'get',
  params:params
});

export const getObj = (id) => request({
  url: urls + '/map/detail',
  method: 'get',
  params: {
    id
  }
});

export const addObj = (data) => request({
  url: urls + '/map/save',
  method: 'post',
  data: data
});
export const updateObj = (data) => request({
  url: urls + '/map/update',
  method: 'post',
  data: data
});



export const delObj = (id) => request({
  url: urls + '/map/remove',
  method: 'post',
  params: {
    ids: id
  }
});