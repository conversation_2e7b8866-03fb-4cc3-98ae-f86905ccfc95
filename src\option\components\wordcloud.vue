<!-- 字符云配置 -->
<template>
  <div>
    <el-form-item label="最小字体" :label="`${$t('components.wordCloud.MinimumFont')}`">
      <avue-input-number v-model="main.activeOption.minFontSize"></avue-input-number>
    </el-form-item>
    <el-form-item label="最大字体" :label="`${$t('components.wordCloud.MaximumFont')}`">
      <avue-input-number v-model="main.activeOption.maxFontSize"></avue-input-number>
    </el-form-item>
    <el-form-item label="间距" :label="`${$t('components.wordCloud.Spacing')}`">
      <avue-input-number v-model="main.activeOption.split"></avue-input-number>
    </el-form-item>
    <el-form-item label="旋转" :label="`${$t('components.wordCloud.Rotation')}`">
      <avue-switch v-model="main.activeOption.rotate"></avue-switch>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'wordcloud',
  inject: ["main"]
}
</script>

<style>
</style>