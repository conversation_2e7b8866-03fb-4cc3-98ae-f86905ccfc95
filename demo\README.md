# 表格插件演示案例

本目录包含了表格插件的各种使用示例，展示了如何在实际项目中集成和使用表格插件。

## 文件说明

### 1. table-basic-example.html
**基础功能演示**
- 展示表格插件的基本配置和使用方法
- 包含动态配置更新功能
- 演示数据的增删操作
- 适合初学者了解插件基本功能

**主要特性:**
- 基础表格渲染
- 滚动功能控制
- 样式配置
- 数据操作

### 2. table-dist-example.html
**生产环境使用示例**
- 使用打包后的 dist 文件
- 模拟真实的数据大屏场景
- 包含完整的UI界面和交互
- 展示高级配置和实时数据更新

**主要特性:**
- 使用 dist/lib/index.umd.min.js
- 数据大屏风格的UI设计
- 实时数据模拟
- 条件格式化演示
- 自定义格式化函数
- 统计信息展示

### 3. table-merge-column-example.html
**🆕 合并列功能演示**
- 展示智能单元格合并功能
- 模拟生产线工序数据
- 多列合并配置演示
- 实时配置切换效果

**主要特性:**
- 合并列功能开关控制
- 工序和工站列合并演示
- 生产数据模拟
- 配置实时预览
- 边框和样式控制

## 如何运行示例

### 方法一：直接打开HTML文件
1. 确保项目已经构建完成，dist 目录存在
2. 直接在浏览器中打开 HTML 文件
3. 查看控制台输出了解配置信息

### 方法二：使用本地服务器
```bash
# 在项目根目录启动简单的HTTP服务器
python -m http.server 8080
# 或者使用Node.js
npx http-server

# 然后访问
http://localhost:8080/demo/table-basic-example.html
http://localhost:8080/demo/table-dist-example.html
http://localhost:8080/demo/table-merge-column-example.html
```

## 集成到实际项目

### 1. 引入必要文件
```html
<!-- CSS文件 -->
<link rel="stylesheet" href="path/to/dist/lib/index.css">

<!-- JavaScript文件 -->
<script src="path/to/dist/lib/index.umd.min.js"></script>
```

### 2. 基本使用
```javascript
// 配置表格选项
const tableConfig = {
  showHeader: true,
  index: true,
  count: 5,
  scroll: true,
  scrollTime: 2000,
  scrollSpeed: 2,
  column: [
    { label: "姓名", prop: "name", width: 100 },
    { label: "年龄", prop: "age", width: 80 }
  ]
};

// 准备数据
const tableData = [
  { name: "张三", age: 25 },
  { name: "李四", age: 30 }
];

// 初始化表格组件
// 具体的初始化方法取决于组件的实际API
```

### 3. 高级配置
```javascript
// 条件格式化配置
const advancedConfig = {
  ...tableConfig,
  column: [
    {
      label: "状态",
      prop: "status",
      condition: 3, // 等于
      value: "异常",
      cellbackground: "#F56C6C",
      cellfont: "#ffffff"
    }
  ]
};

// 自定义格式化函数
const formatterConfig = {
  ...tableConfig,
  column: [
    {
      label: "金额",
      prop: "amount",
      formatter: `(item, row) => {
        return '¥' + row.amount.toLocaleString();
      }`
    }
  ]
};
```

## 配置参数说明

### 基础配置
- `showHeader`: 是否显示表头
- `index`: 是否显示序号列
- `indexWidth`: 序号列宽度
- `count`: 显示行数
- `border`: 是否显示边框
- `roundedTable`: 是否使用圆角

### 滚动配置
- `scroll`: 是否启用滚动
- `scrollTime`: 滚动间隔时间(毫秒)
- `scrollSpeed`: 滚动速度(像素/次)

### 样式配置
- `headerBackground`: 表头背景色
- `headerColor`: 表头文字颜色
- `headerFontSize`: 表头字体大小
- `bodyColor`: 表体文字颜色
- `bodyFontSize`: 表体字体大小
- `nthColor`: 奇数行背景色
- `othColor`: 偶数行背景色

### 列配置
- `label`: 列标题
- `prop`: 数据字段名
- `width`: 列宽度
- `hide`: 是否隐藏
- `wordBreak`: 是否自动换行
- `formatter`: 格式化函数

### 条件格式化
- `condition`: 条件类型 (1-8)
- `value`: 比较值
- `cellbackground`: 单元格背景色
- `cellfont`: 单元格文字颜色
- `rowbackground`: 行背景色
- `rowfont`: 行文字颜色

## 常见问题

### Q: 表格不显示怎么办？
A: 检查以下几点：
1. 确保正确引入了CSS和JS文件
2. 检查数据格式是否正确
3. 查看浏览器控制台是否有错误信息

### Q: 滚动不工作怎么办？
A: 检查以下配置：
1. `scroll` 是否设置为 `true`
2. `scrollTime` 和 `scrollSpeed` 是否设置合理
3. 数据行数是否大于 `count` 设置

### Q: 条件格式化不生效？
A: 确认以下配置：
1. `condition` 值是否在 1-8 范围内
2. `value` 类型是否与数据字段类型匹配
3. 颜色值格式是否正确

### Q: 如何自定义样式？
A: 可以通过以下方式：
1. 修改配置中的颜色和字体设置
2. 使用CSS覆盖默认样式
3. 使用格式化函数返回自定义HTML

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看主项目的 README.md 文档
2. 检查浏览器控制台的错误信息
3. 参考源码中的注释和示例

## 更新日志

- v1.0.0: 初始版本，包含基础功能演示
- v1.1.0: 添加生产环境使用示例
- v1.2.0: 完善文档和配置说明
