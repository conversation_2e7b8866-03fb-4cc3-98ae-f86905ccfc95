<!-- 通用形配置 -->
<template>
  <div>
    <el-form-item label1="在线文档" :label="`${$t('page.build.DOCOnLine')}`">
      <a href="https://echarts.apache.org/examples/zh/index.html"
         target="_blank">
         <!-- 点击查看 -->
         {{ $t('page.build.ClickToView') }}
        </a>
    </el-form-item>
    <el-form-item label1="配置列表" :label="`${$t('page.build.ConfigurationList')}`">
      <el-button size="small"
                 type="primary"
                 @click="openCode">
                 <!-- 编辑 -->
                 {{ $t('page.build.Edit') }}
                </el-button>
       <el-button type="primary"
                 size="small"
                 @click="api.box=true"
                 icon="el-icon-search"></el-button>
            
    </el-form-item>
    
    <el-form-item label1="组件库" :label="`${$t('components.common.ComponentLibrary')}`">
    
        <el-button size="small"
                 type="primary"
                 @click="AddNewComponent">
                 <!-- 加入组件库 -->
                 {{ $t('components.common.ComponentLibrary') }}
                </el-button>         
    </el-form-item>
    <el-form-item label1="动态参数" :label="`${$t('components.common.DynamicParameter')}`">
    {{ configFormInfo.length==0?`${$t('components.common.NoData')}`:''}}
</el-form-item>
    <!-- 动态参数渲染 从组件列表模板中读取，具体查看控件说明 -->
   <configInfoFormRender @change="dataChangeFn" :defaultSelectList="main.DIC.model"   :dataModelOthers="main.activeObj.dataModelOthers" :formList="configFormInfo"></configInfoFormRender>
    <el-input
        v-show="false"
        type="textarea"
        :rows="10"
        disabled
        placeholder=""
        v-model="defaultCode">
      </el-input>
    <codeedit @submit="codeClose"
              title1="配置列表"
              :title="`${$t('page.build.ConfigurationList')}`"
              v-model="code.obj"
              v-if="code.box"
              :type="code.type"
              :visible.sync="code.box"></codeedit>
  <el-dialog title1="组件库"
              :title="`${$t('components.common.ComponentLibrary')}`"
               class="avue-dialog"
               :visible.sync="api.box"
               width="70%">
      <component-list v-if="api.box"
                      :activeName="1"
                      @change="handleApiChange"
                      :menu="false"></component-list>
    </el-dialog>          
  </div>
</template>

<script>
import configInfoFormRender from './configInfoFormRender.vue'
import codeedit from '../../page/group/code';
import componentList from '@/page/list/components'
import { UploadFile,getList, getObj, addObj, delObj, updateObj } from '@/api/components'
export default {
  name: 'common',
  inject: ["main"],
  data () {
    return {
      configFormInfo:[],//动态读取配置的信息
      defaultCode:'',
      api: {
        box: false,
        item: {}
      },
      code: {
        type: 'echartFormatter',
        box: false,
        obj: {},
      }
    }
  },
  components: {
    codeedit,
    componentList,
    configInfoFormRender,
  },
  watch: {
    'main.activeObj': {
      handler (val) {
        this.defaultCode = val["echartFormatter"]
        this.renderCode()
      },
      deep: true
    }
  },
  mounted(){
    //debugger
    this.defaultCode = this.main.activeObj["echartFormatter"]
    this.renderCode()
  },
  methods: {
    dataChangeFn(val){
      //debugger
      this.main.activeObj.dataModelOthers = val
    },
    // 正则去获取代码中的配置信息，固定格式
     /*配置信息不可删除#
  [
     { "field": "name", "title": "名称","itemRender": { "name": "Input", "props": { "placeholder": "请输入名称" } } },
     { "field": "sex", "title": "性别", "itemRender":  { "name": "Select","props": { "placeholder": "请选择性别" } } }
  ]
 #配置信息不可删除*/
    renderCode(){
      this.configFormInfo = []
      const regex = /\/\*配置信息不可删除#([\s\S]*?)#配置信息不可删除\*\//;
      const match = this.defaultCode.match(regex);
      if (match && match[1]) {
        let _configInfo = match[1].trim()
        //debugger
        if(_configInfo){
          try {
           // debugger
            this.configFormInfo = JSON.parse(_configInfo)
            if(this.configFormInfo &&  this.configFormInfo.length > 0){
               if(!this.main.activeObj.hasOwnProperty("dataModelOthers")){
                  this.main.activeObj["dataModelOthers"] ={}
               }
                let newAllKeys = [] // 新的KEY
               this.configFormInfo.forEach(item=>{
                  newAllKeys.push(item.field)
                  if(this.main.activeObj.dataModelOthers.hasOwnProperty(item.field)){
                    // 不处理，赋值
                  }else{
                    this.$set(this.main.activeObj.dataModelOthers,item.field,"")
                  }
               })
                 // 删除多余的KEY
                 let oldFields = Object.keys(this.main.activeObj.dataModelOthers)
                 oldFields.forEach(oldField=>{
                    if(!newAllKeys.includes(oldField)){
                      delete this.main.activeObj.dataModelOthers[oldField]
                    }

                 })
            }
          } catch (error) {
            this.configFormInfo =[]
            console.error("common>>renderCode>>配置信息转换JSON错误，请检查配置信息格式，参考：configInfoFormRender.vue");
          }
        }
         // console.log(match[1].trim());
      } else {
         // console.warn('common>>renderCode>>未找到配置信息');
      }
    },
    handleApiChange (val) {
      //debugger
      let _self = this
      this.api.item = val
      getObj(this.api.item.CID).then(res => {
        const data = res.data.data;
        _self.main.activeObj[_self.code.type] = data.Datas.CCONTEXT
        _self.api.box = false;
        _self.api.item = {}
      })
    },
    codeClose (value) {
      this.main.activeObj[this.code.type] = value;
    },
    openCode () {
      this.code.type = 'echartFormatter';
      this.code.obj = this.main.activeObj[this.code.type];
      this.code.box = true;
    },
    AddNewComponent(){
     let codeType = 'echartFormatter';
      //debugger
      let codeObj = this.main.activeObj[codeType];
      let _title = this.main.config.name
      let randomName = Math.floor(Math.random()*1000)
      let params ={
          "IS_UPDATE": false,
          "CNAME":_title+'_'+randomName,
          "CCONTEXT": codeObj,
          "CTYPE": 1,
          "CREMARK": "新组件",
          "CIMG_URL": "/img/components-default.png"
       }
       addObj(params).then(res=>{
         this.$message.success(`${this.$t('message.AddedSuccessfully')}`)//"添加成功"
       })
    }
  }
}
</script>
 
<style>
</style>