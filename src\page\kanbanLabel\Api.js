
import { url ,urls} from '@/config';

import request from '../../axios'
// 看板分类列表 
export const GetList = (params) => {
      return request({
        url:url + 'api/kanban/kanbanGroup/GetList',
        method: 'get',
        params
      })
}
//新增看板分类
export const AddOrgData = (data) => {
      return request({
        url:url + 'api/kanban/kanbanGroup/Add',
        method: 'post',
        data
      })
}
//保存看板分类
export const UpdateOrgData = (data) => {
      return request({
        url: url +'api/kanban/kanbanGroup/Update',
        method: 'post',
        data
      })
}
//删除看板分类
export const Delete = (data) => {
      return request({
        url:url + 'api/kanban/kanbanGroup/DeleteByID',
        method: 'post',
        data
      })
}

// 根据关键字查询看板分类列表 
export const GetListByQueryJson = (params) => {
      return request({
        url:url + 'api/kanban/kanbanGroup/GetListByQueryJson',
        method: 'get',
        params
      })
}

// 看板标签列表 

export const getall = (params) => {
      return request({
        url:url + 'api/kanban/kanbanLabel/getall',
        method: 'get',
        params
      })
}
//添加标签列表 
export const kanbanLabelAdd = (data) => {
      return request({
        url:url +'api/kanban/kanbanLabel/Add',
        method: 'post',
        data
      })
}
//保存标签列表 
export const kanbanLabelUpdate = (data) => {
      return request({
        url:url + 'api/kanban/kanbanLabel/Update',
        method: 'post',
        data
      })
}

//删除看板标签
export const kanbanLabelDelete = (data) => {
      return request({
        url: url +'api/kanban/kanbanLabel/Delete',
        method: 'post',
        data
      })
}

// 数据源类型列表行拖拽 
export const Change = (params) => {
      return request({
        url:url + 'api/kanban/kanbanLabel/Change',
        method: 'get',
        params
      })
}
//请求数据源
export const GetDataAll = (params) => {
      return request({
        url:url + 'api/MD/DataSource/GetList',
        method: 'get',
        params
      })
}
//请求数据集
export const GetSoureAll = (params) => {
      return request({
        url:url + 'api/MD/DataSet/GetAll',
        method: 'get',
        params
      })
}
//请求模型
export const GetModelAll = (params) => {
      return request({
        url:url + 'api/MD/DataSetModel/GetList',
        method: 'get',
        params
      })
}
//请求参数
export const GetInputParameterByDataSetId = (data) => {
      return request({
        url:url + 'api/MD/DataSet/GetInputParameterByDataSetId',
         method: 'post',
        data
      })
}
//请求数据
export const GetListByDataSetId = (data) => {
      return request({
        url:url + 'api/MD/DataSet/GetListByDataSetId',
         method: 'post',
        data
      })
}
export const Import1 = (data) => {
    return request({
      url:url + 'api/kanban/KanbanLabel/Import',
      method: 'post',
      data
    })
  }
  export const Export1 = (data) => {
    return request({
      url:url + 'api/kanban/KanbanLabel/Export',
      method: 'post',
      data
    })
  }
  export const PushData = (data) => {
    return request({
      url:url + 'api/kanban/KanbanLabel/PushData',
      method: 'post',
      data
    })
  }




