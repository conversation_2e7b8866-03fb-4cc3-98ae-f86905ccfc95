<template>
  <div class="datasetConfiguration">
    <div class="title"></div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="配置信息" name="first" style="padding: 0 10px">
        <vxe-toolbar>
          <template #buttons>
            <el-button
              @click="runhandlick"
              icon="el-icon-video-play"
              type="primary"
              size="mini"
              >运行</el-button
            >
            <el-button
              id="baocun"
              icon="el-icon-document"
              type="primary"
              size="mini"
             @click="submitForm()" 
              >保存</el-button
            >
          </template>
          <template #tools>
            <span style="color: #333; font-size: 12px">数据源模式</span>
            <el-select
              style="width: 180px; margin: 0 10px"
              v-model="value"
              placeholder="请选择"
              size="small"
            >
              <el-option
                v-for="item in option3"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </vxe-toolbar>
        <div class="dataSELECT" v-if="value == 0">
          <el-input
            style="width: 98.5%; margin-left: 85px; margin: 10px 10px 0px 10px"
            type="textarea"
            autosize
            :autosize="{ minRows: 4 }"
            placeholder="请输入内容"
            v-model="textarea"
          >
          </el-input>

          <vxe-table
            v-if="tableData.length > 0"
            style="margin: 10px 10px 0px 10px"
            ref="xTable"
            border
            align="center"
            show-overflow
            keep-source
            :data="tableData"
          >
            <vxe-column type="seq" width="60" title="序号"></vxe-column>
            <vxe-column
              v-for="(item,tableIndex) in Object.keys(tableData[0])"
              :field="item"
              width="150"
              :key="tableIndex"
              :title="item"
            ></vxe-column>
          </vxe-table>
        </div>
        <div class="dataSELECTgui" v-if="value == 1">
          <el-form
            size="small"
            :model="dynamicValidateForm"
            ref="dynamicValidateForm"
            label-width="100px"
            class="demo-dynamic"
          >
            <br />
            <el-form-item label="表名">
              <el-select
                filterable
                @change="nameChange"
                style="width: 96.7%"
                v-model="dynamicValidateForm.name"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in option1"
                  :key="item.CNAME"
                  :label="item.CNAME"
                  :value="item.CNAME"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="操作">
              <el-select
                style="width: 96.7%"
                v-model="dynamicValidateForm.region"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in option2"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="插入列表">
              <el-select
                style="width: 90.5%"
                v-model="dynamicValidateForm.adress"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in option4"
                  :key="item.CCODE"
                  :label="item.CCODE"
                  :value="item.CCODE"
                >
                </el-option>
              </el-select>

              &nbsp;&nbsp;<el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="addDomain"
                >添加</el-button
              >
            </el-form-item>
            <el-form-item
              v-for="(domain, index) in dynamicValidateForm.domains"
              label=" "
              :key="domain.key"
            >
              <el-input
                placeholder="key"
                v-model="domain.key"
                style="width: 44.76%"
              ></el-input
              >&nbsp;&nbsp;
              <el-input
                placeholder="value"
                v-model="domain.value"
                style="width: 44.76%"
              ></el-input
              >&nbsp;&nbsp;<el-button
                type="danger"
                icon="el-icon-delete"
                @click.prevent="removeDomain(index)"
                >删除</el-button
              >
            </el-form-item>
            <br />
            <!-- <el-form-item>
              <el-button
                type="primary"
                @click="submitForm('dynamicValidateForm')"
                >提交</el-button
              >
              <el-button @click="addDomain">新增域名</el-button>
              <el-button @click="resetForm('dynamicValidateForm')"
                >重置</el-button
              >
            </el-form-item> -->
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="预览信息" name="second">配置管理</el-tab-pane>
      <el-tab-pane label="数据源关联功能" name="third">角色管理</el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { GetTabels, QueryTableDatas, } from "@/api/visual";
import { mapGetters } from "vuex";
export default {
  name: "datasetConfiguration",
  props: ["Configuration"],
  data() {
    return {
      value: 0,
   

      textarea: "SELECT * FROM TBL_MD_DATASOURCE_TYPE",
      activeName: "first",
      projectoption: [],
      option3: [
        {
          value: 0,
          name: "SQL模式",
        },
        {
          value: 1,
          name: "GUI模式",
        },
      ],
      option2: [
        {
          value: "insert",
          name: "插入",
        },
        {
          value: "update",
          name: "更新",
        },
        {
          value: "select",
          name: "查询",
        },
      ],
      option1: [],
      option4: [],
      dynamicValidateForm: {
        domains: [],
        name: "",
        region: "",
        adress: "",
      },
      tableData: [],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.getDataList();
  },

  methods: {
    //运行脚本
    runhandlick() {
      this.tableData = [];
      const data = {
        Condition: this.textarea,
      };
      QueryTableDatas(data).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.tableData = res.data.Datas;
        } else {
          this.$message({
            message: res.data.Content,
            type: "error",
          });
        }
      });
    },
    //切换表明
    nameChange(value) {
      console.log(value);
      var arr = this.option1.filter((item) => item.CNAME == value);
      if (arr && arr.length > 0) {
        this.dynamicValidateForm.adress = "";
        this.option4 = arr[0].CCOLUMNS;
      } else {
        this.option4 = [];
      }
    },
    //请求数据源表接口
    getDataList() {
      const data = {
        Condition: "",
      };
      GetTabels(data).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.option1 = res.data.Datas;
        }
      });
    },
    currentRow(row) {
      console.log(row);
    },
    submitForm() {
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     alert("submit!");
      //   } else {
      //     console.log("error submit!!");
      //     return false;
      //   }
      // });
       if(this.value==0){
        console.log(this.textarea);
       }else{
        console.log(this.dynamicValidateForm);
       }
    
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeDomain(index) {
      this.dynamicValidateForm.domains.splice(index, 1);
    },
    addDomain() {
      var arr = this.option4.filter(
        (item) => item.CCODE == this.dynamicValidateForm.adress
      );
      if (arr && arr.length > 0) {
        this.dynamicValidateForm.domains.push({
          value: arr[0].CDEFAULT_VALUE,
          key: arr[0].CCODE,
        });
      } else {
        this.dynamicValidateForm.domains.push({
          value: "",
          key: "",
        });
      }
    },
  },
};
</script>
<style lang="scss">
.datasetConfiguration {
  .vxe-table--body-wrapper {
    max-height: calc(100vh - 474px);
  }
  padding-bottom: 10px;
  position: relative;
  margin-top: 5px;
  background: #fff;
  min-height: 194px;

  .el-tabs--card > .el-tabs__header {
    border: 0;
  }
  .el-tabs__item {
    border: 0;
  }
  .is-top {
    border: 0 !important;
    margin: 0;
  }
  .title {
    position: absolute;
    background: #409eff;
    height: 15px;
    width: 5px;
    top: 13px;
    margin-left: 10px;
  }
  .dataSELECT {
    background: #ebeef5;
    padding-bottom: 10px;
  }
  .dataSELECTgui {
    background: #ebeef5;
  }
  .vxe-toolbar {
    padding-top: 0;
  }
  .el-button {
    height: 32px;
  }
}
</style>
