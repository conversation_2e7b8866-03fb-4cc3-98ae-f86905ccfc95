// module.exports = {
//   publicPath: process.env.VUE_APP_PATH,
//   lintOnSave: false,
//   css: {
//     loaderOptions: {
//       sass: {
//         implementation: require('sass'), // This line must in sass option
//       }
//     }
//   },
//   transpileDependencies: [
//     'monaco-editor',
//     '@jiaminghi/data-view'
//   ],
//   chainWebpack: (config) => {
//     //忽略的打包文件
//     config.externals({
//       'vue': 'Vue',
//       'vue-router': 'VueRouter',
//       'vuex': 'Vuex',
//       'axios': 'axios',
//       'element-ui': 'ELEMENT',
//     })
//   }
// }


// 开启gzip压缩
const CompressionPlugin = require('compression-webpack-plugin');
const productionGzipExtensions = ['js', 'css']
const Timestamp = new Date().getTime()
var BASE_URL= process.env.NODE_ENV === 'development' ? '/' : '/subapp/bulletin'
module.exports = {
  publicPath: BASE_URL ,
  productionSourceMap: false,
  lintOnSave: false,
  css: {
    loaderOptions: {
      sass: {
        implementation: require('sass'), // This line must in sass option
      }
    }
  },

  transpileDependencies: [
    'monaco-editor',
    '@jiaminghi/data-view'
  ],
  devServer:{ port: 10006,

    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "*",
      "Access-Control-Allow-Methods": "*",
    },
  
 
  
    },
    // 加上 微前端改造代码
    configureWebpack: {
        output: {
            library: 'bulletin', // admin这个应用名称（例如：存储命名是 obj-storage）
            libraryTarget: 'umd',
            filename: `js/[name].${process.env.VUE_APP_Version}.${Timestamp}.js`,
            chunkFilename: `js/[name].${process.env.VUE_APP_Version}.${Timestamp}.js`
        },
      
        plugins: process.env.NODE_ENV === 'production' ? [
          new CompressionPlugin({ 
            filename: '[path].gz[query]', //  使得多个.gz文件合并成一个文件，这种方式压缩后的文件少，建议使用
            algorithm: 'gzip', // 官方默认压缩算法也是gzip
            test: /\.js$|\.css$|\.html$|\.ttf$|\.eot$|\.woff$/, // 使用正则给匹配到的文件做压缩，这里是给html、css、js以及字体（.ttf和.woff和.eot）做压缩
            threshold: 10240, //以字节为单位压缩超过此大小的文件，使用默认值10240吧
            minRatio: 0.8, // 最小压缩比率，官方默认0.8
            //是否删除原有静态资源文件，即只保留压缩后的.gz文件，建议这个置为false，还保留源文件。以防：
            deleteOriginalAssets: false
        })
        ] : []


    },
 



  // 打包时不生成.map文件
 
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      'vue': 'Vue',
      'vue-router': 'VueRouter',
      'vuex': 'Vuex',
      'axios': 'axios',
      'element-ui': 'ELEMENT',
    })
  }
}

