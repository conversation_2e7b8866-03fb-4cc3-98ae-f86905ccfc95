import { url ,urls} from '@/config';
import { config } from '@/option/config'
import request from '../../axios'



//分页获取看板图片
export const GetPageListByQueryJson = (params) => request({
    url: url + 'api/kanban/kanbanImage/GetAll',
    method: 'get',
    params:params
  });

//添加看板地图
  export const UpdateImage = (data) => request({
    url: url + 'api/kanban/kanbanImage/UpdateImage',
    method: 'post',
    data:data
  });
  

  //删除看板地图
  export const Delete = (data) => request({
    url: url + 'api/kanban/kanbanImage/Delete',
    method: 'post',
    data:data
  });
  
  export const Import1 = (data) => {
    return request({
      url:url + '/api/kanban/KanbanImage/Import',
      method: 'post',
      data
    })
  }
  export const Export1 = (data) => {
    return request({
      url:url + '/api/kanban/KanbanImage/Export',
      method: 'post',
      data
    })
  }
  export const PushData = (data) => {
    return request({
      url:url + '/api/kanban/KanbanImage/PushData',
      method: 'post',
      data
    })
  }

  export const TestUpload = (data) => {
    return request({
      url:url + '/api/Upload/TestUpload',
      method: 'post',
      data
    })
  }




