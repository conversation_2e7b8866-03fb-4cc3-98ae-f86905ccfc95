<!-- 饼图的配置 -->
<template>
  <div>
    <el-collapse accordion>
      <el-collapse-item title1="饼图设置" :title="`${$t('components.pie.PieChartSettings')}`">
        <el-form-item label="设置为环形" :label="`${$t('components.pie.SetAsRing')}`">
          <avue-switch v-model="main.activeOption.radius"></avue-switch>
        </el-form-item>
        <el-form-item label1="南丁格尔玫瑰" :label="`${$t('components.pie.NightingaleRose')}`">
          <avue-switch v-model="main.activeOption.roseType"></avue-switch>
        </el-form-item>
        <el-form-item label1="自动排序" :label="`${$t('components.pie.AutoSort')}`">
          <avue-switch v-model="main.activeOption.sort"></avue-switch>
        </el-form-item>
        <el-form-item label1="不展示零" :label="`${$t('components.pie.DoNotDisplayZero')}`">
          <avue-switch v-model="main.activeOption.notCount"></avue-switch>
        </el-form-item>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'pie',
  inject: ["main"]
}
</script>

<style>
</style>