<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件 - 多条件修复测试</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .title {
            color: #fff;
            text-align: center;
            font-size: 32px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .subtitle {
            color: rgba(255,255,255,0.9);
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section-title {
            color: #fff;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .section-title::before {
            content: '';
            width: 5px;
            height: 25px;
            background: #ff6b6b;
            border-radius: 3px;
        }
        .problem-description {
            background: rgba(255,107,107,0.2);
            border: 1px solid rgba(255,107,107,0.5);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #fff;
        }
        .problem-description h4 {
            color: #ff6b6b;
            margin: 0 0 10px 0;
        }
        .solution-description {
            background: rgba(46,204,113,0.2);
            border: 1px solid rgba(46,204,113,0.5);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #fff;
        }
        .solution-description h4 {
            color: #2ecc71;
            margin: 0 0 10px 0;
        }
        .test-data {
            background: rgba(0,0,0,0.5);
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            color: #ccc;
            font-size: 14px;
        }
        .test-data h4 {
            color: #ff6b6b;
            margin: 0 0 15px 0;
        }
        .condition-item {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            padding: 10px;
            margin: 8px 0;
            border-left: 4px solid #ff6b6b;
        }
        .condition-item.primary {
            border-left-color: #3498db;
        }
        .condition-item.secondary {
            border-left-color: #e74c3c;
        }
        .controls {
            text-align: center;
            margin: 25px 0;
        }
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: #fff;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        .btn-success {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        .btn-success:hover {
            box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
        }
        .table-wrapper {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
        }
        .test-result {
            background: rgba(0,0,0,0.6);
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 13px;
            color: #ccc;
            max-height: 300px;
            overflow-y: auto;
        }
        .highlight {
            color: #ff6b6b;
            font-weight: bold;
        }
        .success {
            color: #2ecc71;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 多条件修复测试</h1>
        <p class="subtitle">验证"条件1：大于111 + 条件2：大于等于222"的修复效果</p>
        
        <div class="controls">
            <button class="btn" onclick="runFixTest()">🧪 运行修复测试</button>
            <button class="btn btn-success" onclick="showTestConfig()">⚙️ 查看配置</button>
            <button class="btn" onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <!-- 问题描述 -->
        <div class="test-section">
            <div class="section-title">🐛 问题描述</div>
            <div class="problem-description">
                <h4>原问题：</h4>
                <p>当配置了条件1（大于111）和条件2（大于等于222）时，条件2无效。</p>
                <p><strong>原因：</strong>原逻辑只有主条件不满足时才检查多条件，导致条件2被忽略。</p>
            </div>
        </div>

        <!-- 解决方案 -->
        <div class="test-section">
            <div class="section-title">✅ 解决方案</div>
            <div class="solution-description">
                <h4>修复方案：</h4>
                <p>1. 创建统一的条件处理方法 <code>processAllConditions</code></p>
                <p>2. 收集所有条件（主条件 + 扩展条件）</p>
                <p>3. 按顺序检查所有条件，后面的条件优先级更高</p>
                <p>4. 应用最终确定的样式</p>
            </div>
        </div>

        <!-- 测试配置 -->
        <div class="test-section">
            <div class="section-title">⚙️ 测试配置</div>
            <div class="test-data">
                <h4>列配置：</h4>
                <div class="condition-item primary">
                    <strong>主条件：</strong> 数值 > 111 → 蓝色背景
                </div>
                <div class="condition-item secondary">
                    <strong>扩展条件：</strong> 数值 >= 222 → 红色背景（优先级更高）
                </div>
                
                <h4>测试数据：</h4>
                <p>• 值 = 50：不满足任何条件 → 默认样式</p>
                <p>• 值 = 150：满足条件1 → 蓝色背景</p>
                <p>• 值 = 250：满足条件1和条件2 → 红色背景（条件2优先）</p>
                <p>• 值 = 300：满足条件1和条件2 → 红色背景（条件2优先）</p>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-section">
            <div class="section-title">📊 测试结果</div>
            <div class="table-wrapper" id="testResults">
                <div style="color: #ff6b6b; text-align: center; padding: 40px;">
                    点击"运行修复测试"开始测试
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 测试数据
        const testData = [
            { id: 1, name: '测试1', value: 50, description: '不满足任何条件' },
            { id: 2, name: '测试2', value: 150, description: '满足条件1（大于111）' },
            { id: 3, name: '测试3', value: 250, description: '满足条件1和条件2' },
            { id: 4, name: '测试4', value: 300, description: '满足条件1和条件2' },
            { id: 5, name: '测试5', value: 111, description: '边界值：等于111' },
            { id: 6, name: '测试6', value: 222, description: '边界值：等于222' }
        ];

        // 表格配置
        const tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 6,
            scroll: false,
            headerBackground: "#2c3e50",
            headerColor: "#ecf0f1",
            headerFontSize: 16,
            headerTextAlign: "center",
            bodyColor: "#2c3e50",
            bodyFontSize: 14,
            bodyTextAlign: "center",
            nthColor: "rgba(236, 240, 241, 0.1)",
            othColor: "rgba(189, 195, 199, 0.1)",
            column: [
                { 
                    label: "名称", 
                    prop: "name", 
                    width: 100
                },
                { 
                    label: "数值", 
                    prop: "value", 
                    width: 100,
                    // 主条件：大于111显示蓝色
                    condition: 1, // 大于
                    value: 111,
                    cellbackground: "#3498db",
                    cellfont: "#ffffff",
                    // 扩展条件：大于等于222显示红色（优先级更高）
                    editableTabsFormJSON: [
                        {
                            condition: 5, // 大于等于
                            value: 222,
                            cellbackground: "#e74c3c",
                            cellfont: "#ffffff"
                        }
                    ]
                },
                { 
                    label: "说明", 
                    prop: "description", 
                    width: 200
                }
            ]
        };

        // 运行修复测试
        function runFixTest() {
            console.log('🧪 开始运行多条件修复测试...');
            
            let html = '<div style="color: #fff;">';
            html += '<h3 style="color: #ff6b6b; margin-bottom: 20px;">🔧 多条件修复测试结果</h3>';
            
            // 模拟条件处理逻辑
            testData.forEach((row, index) => {
                const result = simulateConditionProcessing(row);
                
                html += `<div class="test-result">`;
                html += `<div class="highlight">测试 ${index + 1}: ${row.name} (值: ${row.value})</div>`;
                html += `<div>描述: ${row.description}</div>`;
                html += `<div>条件1 (>111): ${result.condition1 ? '<span class="success">✅ 满足</span>' : '<span class="warning">❌ 不满足</span>'}</div>`;
                html += `<div>条件2 (>=222): ${result.condition2 ? '<span class="success">✅ 满足</span>' : '<span class="warning">❌ 不满足</span>'}</div>`;
                html += `<div>最终样式: <span class="highlight">${result.finalStyle}</span></div>`;
                html += `<div>优先级说明: ${result.explanation}</div>`;
                html += `</div>`;
            });
            
            html += '</div>';
            document.getElementById('testResults').innerHTML = html;
            
            console.log('✅ 多条件修复测试完成');
        }

        // 模拟条件处理逻辑
        function simulateConditionProcessing(row) {
            const value = row.value;
            const condition1 = value > 111;  // 主条件
            const condition2 = value >= 222; // 扩展条件
            
            let finalStyle = '默认样式';
            let explanation = '无条件满足';
            
            // 按新的逻辑处理：所有条件都检查，后面的优先级更高
            if (condition1) {
                finalStyle = '蓝色背景（条件1）';
                explanation = '满足主条件：大于111';
            }
            
            if (condition2) {
                finalStyle = '红色背景（条件2）';
                explanation = '满足扩展条件：大于等于222，覆盖条件1';
            }
            
            return {
                condition1,
                condition2,
                finalStyle,
                explanation
            };
        }

        // 显示测试配置
        function showTestConfig() {
            console.log('⚙️ 表格配置:', tableConfig);
            console.log('📊 测试数据:', testData);
            alert('配置信息已输出到控制台，请按F12查看详细信息');
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = 
                '<div style="color: #ff6b6b; text-align: center; padding: 40px;">测试结果已清空</div>';
            console.clear();
            console.log('🔧 多条件修复测试');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 多条件修复测试页面已加载');
            console.log('🎯 测试目标：验证条件1（大于111）和条件2（大于等于222）都能正确生效');
            console.log('📊 测试数据已准备就绪，共', testData.length, '条记录');
        });
    </script>
</body>
</html>
