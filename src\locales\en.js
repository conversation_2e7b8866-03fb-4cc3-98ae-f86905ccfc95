// en.js
export default {
	other:{
	      'ConfirmBtn':'Confirm',
		'CancelBtn':'Cancel'
	},
	message:{
		"PleaseEnterURL":"Please enter the URL",
		"PleaseEnterUserName":"Please enter the user name",
		"PleaseEnterDatabaseName":"Please enter the database name",
		"PleaseEnterPortNumber":"Please enter the port number",
		"PleaseEnterHostName":"Please enter the host name",
		"PleaseEnterDataSourceName":"Please enter the data source name",
		"PleaseSelectDataSourceType":"Please select the data source type",
		"PleaseSelect":"Please make a selection",
		"PleaseEnterTemplateName":"Please enter the template name",
		"PleaseSelectType":"Please select the type",
		"PleaseEnterMapName":"Please enter the map name",
		"PleaseEnterConnectionAddress":"Please enter the connection address",
		"PleaseEnterDriverClass":"Please enter the driver class",
		"PleaseSelectComponentType":"Please select the component type",
		"ComponentPreview":"Preview the component",
		"ComponentData":"Component data",
		"PleaseEnterComponentName":"Please enter the component name",
		"PleaseEnterNumber":"Please enter the number",
		"PleaseEnterName":"Please enter the name",
		"PleaseEnterBigScreenName":"Please enter the big screen name",
		"PleaseSelectTemplate":"Please select the template",
		'OperationSuccessful':'Operation Successful',
		'SavedSuccessfully':'Saved Successfully',
		'confirmButtonText': "Confirm",
        'cancelButtonText': "Cancel",
		'AddedSuccessfully':'Added Successfully',
		'PleaseDeleteTheLastTabFirst':'Please Delete The LastTab First',
		'TheTimeCannotBeLessThan1000':'The Time Cannot Be Less Than 1000!!',
		'CopiedSuccessfully':'Copied Successfully',
		'CopyFailed':'Copy Failed',
		'ImportedSuccessfully':'Imported Successfully',
		'RequestForDataWasSuccessful':'Request Data Successful',
		'SuccessfullyCopiedTheComponent':'Copied Component Successfully',
		'SuccessfullyPastedTheComponent':'Pasted Component Successfully',
		'ConfigSavedSuccessfully':'Config Saved Successfully',
		'ConfigSavingFailed':'Config Saving Failed',
		'PictureExportedSuccessfully':'Picture Exported Successfully',
		'DataFormatIsIncorrect':'Data Format Incorrect',
		'DataImportedSuccessfully':'Data Imported Successfully',
		'ErrorInImportingData':'Error In Importing Data',
		'LinkCopiedSuccessfully':'Link Copied Successfully',
		'DashboardSharedSuccessfully':'Dashboard Shared Successfully',
		'DeletedSuccessfully':'Deleted Successfully',
		'PermanentDeletion':'Permanent Deletion？',
		'Prompt':'Tip',
		'ModifiedSuccessfully':'Modified Successfully',
		'NewlyAddedSuccessfully':'Added Successfully',
		'ConnectedSuccessfully':'Connected Successfully',
		'ConnectionFailed':'Connection Failed',
		'SharedSuccessfully':'Shared uccessfully',
		'DataRefreshedSuccessfully':'Data Refreshed Successfully',
		'VerificationCodeCannotBeEmpty':'Verification Code Cannot Be Empty',
		'VerificationCodeIsIncorrect':'Verification Code Is Incorrect',
		'CombineTheSelectedLayers': 'Combine The Selected Layers',
		'BatchDeleteTheSelectedLayers':'Batch Delete The Selected Layers',
		'ExportTheLargeScreenPicture':'Export The Large Screen Picture',
		'DeleteTheSelectedLayers':'Delete The Selected Layers',
		'ConfirmToDeleteTheSelectedSata':'Confirm To Delete The Selected Data',
		'DisassembleTheLayers':'Disassemble The Layers'
	},
	rules:{
		"PleaseEnterBigScreenName":"Please enter the big screen name",
		"PleaseSelectTemplate":"Please select the template",
		"PleaseSelectDataSource":"Please select the data source",
		"PleaseEnterDataSourceName":"Please enter the data source name",
		"PleaseEnterNumber":"Please enter the number",
		"PleaseEnterName":"Please enter the name",
		"PleaseEnterDriverClass":"Please enter the driver class",
		"PleaseEnterUserName":"Please enter the user name",
		"PleaseEnterPassword":"Please enter the password",
		"PleaseEnterConnectionAddress":"Please enter the connection address",
		"PleaseSelect":"Please make a selection",
		"PleaseSelectType":"Please select the type",
		"PleaseEnterTemplateName":"Please enter the template name",
		"PleaseEnterMapName":"Please enter the map name",
		"PleaseSelectDataSourceType":"Please select the data source type",
		"PleaseEnterDataSourceName":"Please enter the data source name",
		"PleaseEnterHostName":"Please enter the host name",
		"PleaseEnterPortNumber":"Please enter the port number",
		"PleaseEnterDatabaseName":"Please enter the database name",
		"PleaseEnterUserName":"Please enter the user name",
		"PleaseEnterPassword":"Please enter the password",
		"PleaseSelectDataSourceType":"Please select the data source type",
		"PleaseEnterDataSourceName":"Please enter the data source name",
		"PleaseEnterURL":"Please enter the URL"
	},
	page:{
		largescreentemplate:{
			"Add":"Add",
			"Import":"Import",
			"Export":"Export",
			"Synchronize":"Synchronize",
			"Keyword":"Keyword",
			"Query":"Query",
			"Reset":"Reset",
			"Edit":"Edit",
			"Delete":"Delete",
			"SerialNumber":"NO.",
			"Operation":"Operation",
			"PleaseSelectLabel":"Please select the label",
			"TemplateName":"Name",
			"PleaseSearchforTemplateName":"Please search for the template name",
			"CreateNewTemplate":"Create a new template",
			"Design":"Design",
			"Preview":"Preview",
			"Copy":"Copy",
			"PleaseEnterTemplateName":"Please enter the template name",
			"TemplateTag":"Tag",
			"BigScreenWidth":"Screen width",
			'BigScreenHeight':'Screen height',
			"PleaseEnterBigScreenWidth":"Please enter the screen width",
			"PleaseEnterBigScreenHeight":"Please enter the screen height",
			"Cancel":"Cancel",
			"Confirm":"Confirm",
			'TagCategory':'TagCategory',
			
		},
		create:{
			"BigScreenDesign":"Design",
			"Cancel":"Cancel",
			"Confirm":"Confirm",
			"BigScreenName":"ScreenName",
			"BelongingGroup":"Group",
			"PleaseSelectBelongingGroup":"Please Select Belonging Group",
			"BigScreenSize":"ScreenSize",
			"WidthHeight":" Width*Height",
			"BigScreenPassword":"Password",
			"SelecttheFollowingMethodsforCreation":"Select the Following Methods for Creation",
			"TemplateName":"TemplateName",
			"PleaseSearchforTemplateName":"Please Search for Template Name",
			"Query":"Query",
			"Reset":"Reset",
			"BlankTemplate":"BlankTemplate",
			"UpdateKanbanList":"Update Kanban List"
		},
		mapManagement:{
			"Add":"Add",
			"Import":"Import",
			"Export":"Export",
			"Synchronize":"Synchronize",
			"Keyword":"Keyword",
			"Query":"Query",
			"Reset":"Reset",
			"Edit":"Edit",
			"Delete":"Delete",
			"SerialNumber":"NO.",
			"Operation":"Operation",
			"MapName":"MapName",
			"PleaseEnterMapName":"Please Enter Map Name",
			"AddMoreMaps":"Add More Maps",
			"MapData":"MapData",
			"PleaseEnterMapData":"Please Enter Map Data",
			"Cancel":"Cancel",
			"Confirm":"Confirm",
			"MapName":"Name",
			"PleaseEnterMapName":"Please enter the map name",
			"MapData":"Data",
			"PleaseEnterMapData":"Please enter the map data",
			'AddMoreMaps':'Add More Maps',
			'CreateMap':'Create Map',
			'EditMap':'Edit Map'
		},
		filemanagement:{
			"Add":"Add",
			"Import":"Import",
			"Export":"Export",
			"Synchronize":"Synchronize",
			"Keyword":"Keyword",
			"Query":"Query",
			"Reset":"Reset",
			"Edit":"Edit",
			"Delete":"Delete",
			"SerialNumber":"NO.",
			"Operation":"Operation",
			"PictureName":"PictureName",
			"PictureType":"PictureType",
			"PicturePath":"PicturePath",
			"PicturePreview":"Preview",
			"Loading":"Loading",
			"DragTheFileHere":"DragTheFileHere",
			"ClickToUpload":"ClickToUpload",
			'Create':'Create',
		},
		components:{
			"Add":"Add",
			"PleaseEnterName":"Please Enter Name",
			"Preview":"Preview",
			"Select":"Select",
			"Edit":"Edit",
			"Delete":"Delete",
			"NoDataAvailable":"No Data",
			"EditComponent":"Edit Component",
			"EnlargeEditor":"Enlarge Editor",
			"OfficialComponentLibrary":"OfficialComponentLibrary",
			"ThirdPartyComponentLibrary":"Third-PartyComponentLibrary",
			"ComponentName":"Name",
			"PleaseEnterComponentName":"Please Enter Component Name",
			"ComponentType":"Type",
			"PleaseSelectComponentType":"Please Select Component Type",
			"ComponentData":"Data",
			"ComponentPreview":"Preview"
		},
		classification:{
			"Add":"Add",
			"Import":"Import",
			"Export":"Export",
			"Synchronize":"Synchronize",
			"Keyword":"Keyword",
			"Query":"Query",
			"Reset":"Reset",
			"Edit":"Edit",
			"Delete":"Delete",
			"SerialNumber":"NO.",
			"Operation":"Operation",
			"GroupingName":"GroupingName",
			"GroupingDescription":"GroupingDescription",
			"EditSave":"Edit & Save",
			"AddSave":"Add & Save",
			"ParentGrouping":"ParentGrouping",
			"PleaseEnterGroupingName":"Please Enter Grouping Name",
			"PleaseEnterGroupingDescription":"Please Enter Grouping Description",
			"Cancel":"Cancel",
			"Confirm":"Confirm"
		},
		kanbanLabel:{
			"Add":"Add",
			"Import":"Import",
			"Export":"Export",
			"Synchronize":"Synchronize",
			"Keyword":"Keyword",
			"Query":"Query",
			"Reset":"Reset",
			"Edit":"Edit",
			"Delete":"Delete",
			"AddTemplateTag":"Add Template Tag",
			"EditTemplateTag":"Edit Template Tag",
			"SerialNumber":"NO.",
			"LabelCode":"Code",
			"LabelName":"Name",
			"LabelRemarks":"Remarks",
			"Operation":"Operation",
			"PleaseEnterLabelNumber":"Please enter the label number",
			"PleaseEnterLabelName":"Please enter the label name",
			"PleaseEnterLabelName":"Please enter the label name",
			"Cancel":"Cancel",
			"Confirm":"Confirm"
		},
		largescreendesign:{
			"ShareBigScreen":"Share Big Screen",
			"PleaseSelecttheBelongingTemplate":"Please Select the Belonging Template",
			"ShareLink":"Share Link",
			"CopyLink":"Copy Link",
			"SharePassword":"Share Password",
			KanbanGrouping:'Kanban Grouping',
			Selectthefollowingmethodstocreate:'Select methods to create',
			Import:'Import',
			Export:'Export',
			Synchronize:'Synchronize',
			KanbanName:'Kanban Name',
			'SearchTemplateName':'Search Template Name',
			'Search':'Search',
			'Reset':'Reset',
			"CreateNewBigScreen":"Create a new big screen",
			"Preview":"Preview",
			"Share":"Share",
			"Design":"Design",
			"Delete":"Delete",
			"Edit":"Edit",
			"Copy":"Copy",
			"AddTemplate":"Add a template",
			"BigScreenName":"Name",
			"PleaseEnterBigScreenName":"Please enter the big screen name",
			"BelongingGroup":"Group",
			"PleaseSelectBelongingGroup":"Please select the belonging group",
			"BigScreenWidth":"Width",
			"PleaseEnterBigScreenWidth":"Please enter the big screen width",
			"BigScreenHeight":"Height",
			"PleaseEnterBigScreenHeight":"Please enter the big screen height",
			"Cancel":"Cancel",
			"Confirm":"Confirm",
			"BelongingLabel":"Label",
			"PleaseSelectLabel":"Please select the label"
		},
		top:{
			'search':'search components'
		},
		header:{
			LargeScreenName:'Large screen name',
			TemplateName:'Template name',
			Preview:'Preview',
			SaveCover:'SaveCover',
			SaveConfiguration:'SaveConfig',
			Import:'Import',
			Export:'Export',
			Undo:'Undo',
			'Tip':'Tip',
			'WhetherToExportTheLargeScreenPicture': 'Whether to export the large screen picture',
			'Prompt': 'Prompt',
			'Confirm': 'Confirm',
			'Cancel': 'Cancel',
			'PictureExportSuccessful': 'Picture export successful',
			'PleaseCheckTheServerConfiguration': 'The large screen configuration failed to be saved. Please check the server configuration.',
			'LargeScreenConfigurationSavedSuccessfully': 'Large screen configuration saved successfully',
			'UpdateDashboardList': 'Update dashboard list',
			'SavingConfiguration': 'Saving configuration. Please wait a moment.',
			'LargeScreenConfigurationCoverSavedSuccessfully': 'Large screen configuration cover saved successfully',
			'UpdateDashboardList': 'Update dashboard list',
			'LargeScreenConfigurationCoverFailedToBeSaved': 'Large screen configuration cover failed to be saved. Please check the server configuration.',
			'SavingTheConfigurationCover': 'Saving the configuration cover. Please wait a moment.'
		},
		build:{
			'PromptEvent':'Prompt Event',
			'TitleEvent':'Title Event',
			'DataSourceEdit':'DataSource Edit',
			'SystemColor':'System Color',
			'RequestHeader':'Request Header',
			'RequestConfiguration':'Request Config',
			'Grouping':'Grouping',
			'Delete':'Delete',
			'Filter':'Filter',
			'ResponseData':'Response',
			'EditFilter':'Edit Filter',
			'DOCOnLine': 'Doc On Line',
			'ClickToView':'Click To View',
			'ModuleName':'Module Name',
			'ConfigurationList':'Config List',
			'Subclass':'Sub class',
			'ParameterName':'Params name',
			'MappingFields':'Mapping fields',
			'Defaultvalue':'Default value',
			'ClickEvent':'Click Event',
			'Event':'Event',
			'basis':'basis',
			'requestData':'Request Data',
			'Component': 'Cmp.',
			'Layer': 'Layer',
			'ComponentConfiguration': 'Config',
			'Data': 'Data',
			'DataType': 'Data Type',
			'SQLStatement': 'SQL Statement',
			'InterfaceAddress': 'Interface Address',
			'RequestMethod': 'Request Method',
			'WSAddress': 'WS Address',
			'DatasetSelection': 'Dataset',
			'DataSourceSelection': 'Data Source',
			'RefreshTime': 'Refresh Time',
			'ParameterConfiguration': 'Params Config',
			'GlobalDataset': 'Global Dataset',
			'MoreSettings': 'More Settings',
			'LargeScreenConfiguration': 'Setting',
			'Basic': 'Basic',
			'HorizontalAlignment': 'Horizontal Alignment',
			'LeftAlignment': 'Left Alignment',
			'CenterAlignment': 'Center Alignment',
			'RightAlignment': 'Right Alignment',
			'VerticalAlignment': 'Vertical Alignment',
			'TopAlignment': 'Top Alignment',
			'MiddleAlignment': 'Middle Alignment',
			'BottomAlignment': 'Bottom Alignment',
			'Group': 'Group',
			'Delete': 'Delete',
			'XAxisModel': 'X-axis Model',
			'YAxisModel': 'Y-axis Model',
			'MultiYAxisModel': 'Multi-Y-axis Model',
			'LayerName': 'Layer Name',
			'Hide': 'Hide',
			'Lock': 'Lock',
			'SystemColorScheme': 'System Color Scheme',
			'LargeScreenName': 'Name',
			'LargeScreenWidth': 'Width',
			'LargeScreenHeight': 'Height',
			'LargeScreenIntroduction': 'Description',
			'PleaseEnterTheLargeScreenIntroduction': 'Please Enter the Large Screen Introduction',
			'BackgroundColor': 'BgColor',
			'BackgroundPicture': 'BgPicture',
			'RefreshPageTime': 'Refresh Page Time',
			'GlobalData':'Global Data',
			'DataSource':'DataSource',
			'Dataset':'Dataset',
			'FirstLoad':'FirstLoad',
			'SelectionTime': 'Selection Time',
			'PreviewMode': 'Preview Mode',
			'XAxisFilling': 'X-axis Filling',
			'YAxisFilling': 'Y-axis Filling',
			'ForceStretchingThePicture': 'Stretching Picture',
			'MoreSettings': 'More Settings',
			'Interaction': 'Interaction',
			'Parameter': 'Parameter',
			'SerialNumber': 'Serial Number',
			'Configuration': 'Configuration',
			'EntryAnimation': 'Entry Animation',
			'ClickToViewAnimationType': 'Click to View Animation Type',
			'Position': 'Position',
			'Size': 'Size',
			'Font': 'Font',
			'Perspective': 'Perspective',
			'Scale': 'Scale',
			'Opacity': 'Opacity',
			'XRotationDegree': 'XRotation',
			'YRotationDegree': 'YRotation',
			'ZRotationDegree': 'ZRotation',
			'MoreSettings': 'More Settings',
			'GlobalRequestAddress': 'Global Request Address',
			'GlobalRequestParameters': 'Global Request Parameters',
			'GlobalRequestHeader': 'Global Request Header',
			'LargeScreenWatermark': 'Watermark',
			'Content': 'Content',
			'Size': 'Size',
			'Color': 'Color',
			'Angle': 'Angle',
			'DataValue': 'Data Value',
			'EditDataValue': 'Edit Data Value',
			'ImportData': 'Import Data (Excel)',
			'JustWriteItAsAFunctionToReturnSQL': 'Just write it as a function to return SQL statements.',
			'RequestParameters_BODY': 'Request Parameters (Body)',
			'RequestParameters':'Request Parameters',
			'Filter': 'Filter',
			'Edit': 'Edit',
			'Close': 'Close',
			'SaveAndRequest': 'Save and Request',
			'OriginalRequest': 'Original Request',
			'DataSourceModification': 'Data Source Modification'
		}
	},
	components:{
		imgList:{
			'HoverStop': 'Hover Stop',
			'ScrollingTime':'Scrolling Time',
			'Direction':'Direction',
			'TabTime': 'Tab Time',
			'Autoplay':'Autoplay',
			'CarouselTime':'Time',
			'CarouselDirection':'Direction',
			'Horizontal':'Horizontal',
			'Vertical':'Vertical'
		},
		bar:{
			"VerticalDisplay":"Vertical Display",
			"BarSettings":"Bar Settings",
			"MaxWidth":	"Max Width",
			"RoundCorner":"Round Corner",
			"MinHeight":"Min Height",
		},
		main:{
			"TitleSettings":"Title Settings",
			"Title":"Title",
			"TitleDisplay":"Title Display",
			"FontColor":"Font Color",
			"FontSize":"Font Size",
			"FontPosition":"Font Position",
			"Subtitle":"Subtitle",
			"X-axisSettings":"X-axis Settings",
			"Name":"Name",
			"Display":"Display",
			"DisplayGridLine":"Display Grid Line",
			"LabelSpacing":"Label Spacing",
			"TextAngle":"Text Angle",
			"AxisInversion":"Axis Inversion",
			"AxisColor":"Axis Color",
			"Y-axisSettings":"Y-axis Settings",
			"AxisGridLines":"Axis Grid Lines",
			"Inversion":"Inversion",
			"ValueSettings":"Value Settings",
			"DisplayFormat":"Display Format",
			"DisplayPosition":"Display Position",
			"FontWeight":"Font Weight",
			"PromptSettings":"Prompt Settings",
			"BackgroundColor":"Bg Color",
			"AxisMarginSettings":"Axis Margin Settings",
			"LeftMargin":"Left Margin (Pixels)",
			"TopMargin":"Top Margin (Pixels)",
			"RightMargin":"Right Margin (Pixels)",
			"BottomMargin":"Bottom Margin (Pixels)",
			"LegendOperation":"Legend Operation",
			"Legend":"Legend",
			"IndependentColor":"Independent Color",
			"Position":"Position",
			"RelativePosition":"Relative Position",
			"LayoutOrientation":"Layout Orientation",
			"CustomColor":"Custom Color"
		},
		borderBox:{
			'DecorationType':'Decoration Type',
			'MainColor':'Main Color',
			'SecondaryColor':'Secondary Color',
			'BackgroundColor':'Background Color',
			'AnimationDuration':'Animation Duration'
		},
		clappr:{},
		common:{
			'ComponentLibrary':'Component Library',
			'AddToComponentLibrary':'Add To Component Library',
			'DynamicParameter':'Dynamic Parameter',
			'NoData':'No Data',
		},
		datav:{},
		datetime:{
			'TimeFormat':'Time Format',
			'CustomFormat':'Custom Format',
			'FontSpacing':'Font Spacing',
			'FontSize':'Font Size',
			'FontBackground':'Font Background',
			'Alignment':'Alignment',
			'FontWeight':'Font Weight',
			'FontColor':'Font Color'
		},
		decoration:{
			'DecorationType':'Decoration Type',
			'MainColor':'Main Color',
			'SecondaryColor':'Secondary Color',
			'BackgroundColor':'Background Color',
			'AnimationDuration':'Animation Duration'
		},
		flop:{
			'Overall':'Overall',
			'Accuracy':'Accuracy',
			'LengthandWidth':'Length and Width',
			'Margin':'Margin',
			'Padding':'Padding',
			'Border':'Border',
			'BorderColor':'Border Color',
			'BorderWidth':'Border Width',
			'BackgroundColor':'Background Color',
			'ImageAddress':'Image Address',
			'ContentSettings':'Content Settings',
			'Content':'Content',
			'FontSize':'Font Size',
			'FontColor':'Font Color',
			'FontWeight':'Font Weight',
			'Alignment':'Alignment',
			'PrefixSettings':'Prefix Settings',
			'NoLineBreak':'No Line Break',
			'PrefixContent':'Prefix Content',
			'Color':'Color',
			'FontLineHeight':'Line Height',
			'SuffixSettings':'Suffix Settings',
			'SuffixContent':'Suffix Content'
		},
		funnel:{
			'TextInside':'Text Inside',
		},
		gauge:{
			'ScaleValue':'Scale Value',
			'ScaleFontSize':'Scale Font Size',
			'ScaleThickness':'Scale Thickness',
			'ScaleColor':'Scale Color',
			'IndicatorFontSize':'Indicator Font Size',
			'IndicatorUnit':'Indicator Unit'
		},
		iframe:{
			'Address':'Address',
		},
		img:{
			'Rotate':'Rotate',
			'Opacity':'Opacity',
			'BorderRadius':'Radius',
			'Duration':'Duration',
			'PicAddress':'IMG Address',
		},
		imgBorder:{
			'BackgroundColor':'Background Color',
			'Opacity':'Opacity',
			'PicAddress':'IMG Address',
		},
		line:{
			'LineChartSettings':'Line Settings',
			'SmoothCurve':'Smooth',
			'AreaStacking':'Area Stacking',
			'LineWidth':'Line Width',
			'CircleDot':'Round Dot',
			'SizeoftheDot':'Dot Size'
		},
		map:{
			'MapSelection':'Map Selection',
			'MapScale':'Map Scale',
			'MapZoom':'Map Zoom',
			'CarouselSettings':'Carousel Settings',
			'TurnonCarousel':'Turn on Carousel',
			'CarouselTime':'Carousel Time',
			'BasicSettings':'Basic Settings',
			'FontSize':'Font Size',
			'FontHighlightColor':'Font Highlight Color',
			'FontColor':'Font Color',
			'BorderColor':'Border Color',
			'RegionSettings':'Region Settings',
			'RegionLine':'Region Line',
			'RegionColor':'Region Color',
			'RegionHighlightColor':'Region Highlight Color',
			'TipSettings':'Tip Settings',
			'BackgroundColor':'Background Color',
			'TextColor':'Text Color',
			'TextSize':'Text Size'
		},
		pictorialBar:{
			'TitleColor': 'Title Color',
			'TitleSize': 'Title Size',
			'ValueColor': 'Value Color',
			'ValueSize': 'Value Size',
			'Icon': 'Icon',
			'IconSize': 'Icon Size',
			'Spacing': 'Spacing'
		},
		pie:{
			'PieChartSettings':'Pie Settings',
			'SetAsRing':'Set As Ring',
			'NightingaleRose':'Nightingale Rose',
			'AutoSort':'Auto Sort',
			'DoNotDisplayZero':'No Count'
		},
		radar:{
			'AreaOpacity':'Area Opacity',
		},
		rectangle:{},
		scatter:{},
		swiper:{
			'Type':'Type',
			'Interval':'Interval',
			'Indicator':'Indicator',
			'Opacity':'Opacity',
			'Autoplay':'Autoplay',
			'Controls':'Controls',
			'Loop':'Loop',
		},
		table:{
				'OpenRanking':'Enable Ranking',
				'RankingWidth':'Width of Ranking',
				'Border':'Border',
				'RoundCorner':'Rounded Corner',
				'AppendMode':'Append Mode',
				'EnableMergeColumn':'Enable Merge Column',
				'EnableScrolling':'Enable Scrolling',
				'ScrollInterval':'Scrolling Interval',
				'ScrollSpeed':'Scrolling Speed',
				'HeaderSettings':'Header Settings',
				'Display':'Display',
				'FontSize':'Font Size',
				'ColumnHeaderHeight':'Column Header Height',
				'BackgroundColor':'Background Color',
				'FontColor':'Font Color',
				'AlignmentMethod':'Alignment Method',
				'TableSettings':'Table Settings',
				'NumberofDisplayedRows':'Number of Displayed Rows',
				'FontSize':'Font Size',
				'TextColor':'Text Color',
				'OddRowColor':'Color of Odd Rows',
				'EvenRowColor':'Color of Even Rows',
				'TableColumnSettings':'Table Column Settings',
				'DefaultConfiguration':'Default Configuration',
				'RowBackground':'Row Background',
				'PleaseSelectColor':'Please Select Color',
				'RowFont':'Row Font',
				'CellBackground':'Cell Background',
				'CellFont':'Cell Font',
				'Condition':'Condition',
				'Value':'Value',
				'PleaseSelectCondition':'Please Select Condition',
				'PleaseEnterValue':'Please Enter Value',
				'Formatting':'Formatting',
				'Edit':'Edit',
				'FormattingTAB':'Formatting TAB',
				'EditData':'Edit Data',
				'LoadModel':'Load Model'
		},
		tabs:{
			'FontSize':'Font Size',
			'FontColor':'Font Color',
			'Type':'Type',
			'FontSpacing':'Font Spacing',
			'BorderSettings':'Border Settings',
			'BackgroundColor':'Background Color',
			'IconSpacing':'Icon Spacing',
			'IconSize':'Icon Size',
			'ImageAddress':'Image Address',
			'BorderColor':'Border Color',
			'BorderWidth':'Border Width',
			'HighlightSettings':'Highlight Settings',
			'FontHighlightColor':'Font Highlight Color',
			'BackgroundImage':'Background Image'
		},
		text:{
			'TextContent':'Text Content',
			'FontType':'Font Type',
			'FontSize':'Font Size',
			"FontColor":"Font Color",
			'FontSpacing':'Font Spacing',
			'FontLineHeight':'Line Height',
			'FontBackground':'Background',
			'FontWeight':'Font Weight',
			'Alignment':'Alignment',
			'MarqueeSettings':'Marquee Settings',
			'TurnOn':'On',
			'ScrollingSpeed':'Scrolling Speed',
			'HyperlinkSettings':'Hyperlink',
			'OpeningMethod':'Method',
			'HyperlinkAddress':'Hyperlink'
		},
		time:{
			'ExecutionInterval':'Exec Interval',
			'ExecutionLogic':'Exec Logic',
			'Edit':'Edit',
		},
		video:{
			'Cover':'Cover',
			'Address':'Address',
			'Autoplay':'Autoplay',
			'Controls':'Controls',
			'Loop':'Loop',
		},
		vue:{},
		wordCloud:{
			'MinimumFont':'Minimum Font',
			'MaximumFont':'Maximum Font',
			'Spacing':'Spacing',
			'Rotation':'Rotation'
		},
		progress:{
			'Description':'Description',
			'Type':'Type',
			'Width':'Width',
			'Color':'Color',
			'BackgroundColor':'Background Color',
			'FontSize':'Font Size',
			'FontColor':'Font Color',
			'FontWeight':'Font Weight',
			'PrefixFontSize':'Prefix Font Size',
			'PrefixFontColor':'Prefix Font Color',
			'PrefixFontWeight':'Prefix Font Weight'
		}
	},
	'新建大屏':'Create a new screen',
	'编辑大屏':'Edit the screen',
	'复制大屏':'Copy the screen',
	//////template page start///////
	"编辑模板":"Edit the template",
	"新建模板":"Create a new template",
	"复制模板":"Copy the template",
	//////template page end///////
	'柱状图':'Bar',
	'图表':'Chart',
	'文字':'Text',
	'媒体':'Media',
	'指标':'Indicator',
	'表格':'Table',
	'边框':'Border',
	'datav例子':'Datav Example',
	'装饰':'Decoration',
	'二次开发':'Development',
	'通用型': 'General Type',
	'柱形图': 'Bar',
	'折线图': 'Line',
	'饼图': 'Pie',
	'象形图': 'Pictogram',
	'雷达图': 'Radar',
	'散点图': 'Scatter',
	'漏斗图': 'Funnel',
	'地图': 'Map',
	'矩形图': 'Rectangular',
	'定时器': 'Timer',
	'自定义Vue组件': 'Custom Component',
	'文本框': 'TextBox',
	'跑马灯': 'Marquee',
	'超链接': 'Hyperlink',
	'实时时间': 'Real-time Time',
	'图片': 'Picture',
	'图片框': 'Picture Frame',
	'轮播图': 'Carousel',
	'video播放器': 'Video Player',
	'hls播放器': 'HLS Player',
	'翻牌器': 'Flip Card',
	'颜色块': 'Color Block',
	'环形图': 'Ring',
	'进度条': 'Progress Bar',
	'仪表盘': 'Dashboard',
	'字符云': 'Word Cloud',
	'表格': 'Table',
	'边框1': 'Border 1',
	'边框2': 'Border 2',
	'边框3': 'Border 3',
	'边框4': 'Border 4',
	'边框5': 'Border 5',
	'边框6': 'Border 6',
	'边框7': 'Border 7',
	'边框8': 'Border 8',
	'边框9': 'Border 9',
	'边框10': 'Border 10',
	'边框11': 'Border 11',
	'边框12': 'Border 12',
	'滚动排名': 'RollingRanking',
	'胶囊排名': 'CapsuleRanking',
	'水位图': 'Water Level',
	'进度池': 'Progress Pool',
	'锥形柱图': 'Conical Column',
	'动态环图': 'Dynamic Ring',
	'装饰1': 'Decoration 1',
	'装饰2': 'Decoration 2',
	'装饰3': 'Decoration 3',
	'装饰4': 'Decoration 4',
	'装饰5': 'Decoration 5',
	'装饰6': 'Decoration 6',
	'装饰7': 'Decoration 7',
	'装饰8': 'Decoration 8',
	'装饰9': 'Decoration 9',
	'装饰10': 'Decoration 10',
	'装饰11': 'Decoration 11',
	'装饰12': 'Decoration 12',
	'自定义组件': 'Custom',
	'滚动选项卡': 'Scrolling Tab',
	'滚动列表': 'Scrolling List',
	'选项卡': 'Tabs',
	'飞线图':'Flow Map',
	//////表格的列配置//////////
	'配置项':'Config',
	"名称":"Name",
	"key值":"KeyValue",
	"宽度":"Width",
	"状态":"Status",
	"换行":"WordBreak"
	
}