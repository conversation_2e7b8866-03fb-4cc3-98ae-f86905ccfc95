// import { Loading } from 'element-ui';
import { checkUrl } from '@/utils/utils'
import axios from 'axios';
import { setLocalStorageStore, getLocalStorageStore,getCookie } from './utils/setStore'
let multiOrganization = getLocalStorageStore("multiOrganization");
let language= getLocalStorageStore('language')
var userInfo=getLocalStorageStore('userInfo') 

window.$glob = {
  url: '',
  params: {},
  query: {},
  headers: {
    
  },
};
function getGlobParams () {
  var query = window.location.search.substring(1);
  query = query.split("&");
  query.forEach(ele => {
    var pair = ele.split("=");
    window.$glob.params[pair[0]] = pair[1]
  })
}
getGlobParams();
axios.defaults.timeout = 30000;
//返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500; // 默认的
};
//跨域请求，允许保存cookie
// let loadingInstance = '';
axios.defaults.withCredentials = true;
axios.interceptors.request.use(config => {

  if (!checkUrl(config.url)) config.url = window.$glob.url + config.url;
  let header = window.$glob.header || {};
  config.headers = Object.assign(config.headers, header);
  config.headers['Accept']="currentusername$$"+userInfo?.CUSER_NAME+"_-enterprisecode$$"+userInfo?.CENTERPRISE_CODE+'_-OrgCode$$'+multiOrganization?.CORG_CODE;
  let token = getLocalStorageStore("token")||getCookie('token');
  let userInfos = getCookie("userInfo");
      
  
  if(token){
    config.headers['Authorization']='Bearer '+token
 
 }

  if(language){
 var arr=getLocalStorageStore('language')
   config.headers['language']=arr
  }
 
 if(userInfos){
 this.$store.dispatch("LoginuserInfo", userInfos);
 }




  let data = window.$glob.query || {}
  let key;
  if (config.method == 'get') {
    key = "params"
  } else if (config.method == 'post') {
    key = "data"
  }
  if (typeof (config[key]) === 'object') {
    config[key] = Object.assign(config[key] || {}, data)
  }
  return config
}, error => {
  return Promise.reject(error)
});
//HTTPrequest拦截
axios.interceptors.response.use(config => {
  try {
    if(!config.data.data.Success){
      let msg = config.data.data.Content || '请求失败，请检查！';
      return Promise.reject(msg);
   }
  } catch (error) {
    return Promise.reject('请求失败，请检查'+error);
  }
 
  return config;
}, error => {
  // loadingInstance.close();
  return Promise.reject(new Error(error));
})

export default axios;
