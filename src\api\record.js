import { urls } from '@/config';
import request from '../axios'
export const getList = (params) => request({
  url: urls + '/record/list',
  method: 'get',
  params: params
});



export const getObj = (id) => request({
  url: urls + '/record/detail',
  method: 'get',
  params: {
    id
  }
});

export const addObj = (data) => request({
  url: urls + '/record/save',
  method: 'post',
  data: data
});
export const updateObj = (data) => request({
  url: urls + '/record/update',
  method: 'post',
  data: data
});



export const delObj = (id) => request({
  url: urls + '/record/remove',
  method: 'post',
  params: {
    ids: id
  }
});