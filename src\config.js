
export default {
  COMPNAME: "avue-echart-",
  NAME: "list",
  DEAFNAME: 'item'
}
//BASE_URL服务地址 云丰：http://**********:6682/， //'http://23o1l19278.zicp.fun:32882/',// 骏亚服务 http://**************:6682/ 本地服务 http://**************:19719/

const BASE_URL = process.env.NODE_ENV === 'production' ? '/' : 'http://**************:19719/' //http://23o1l19278.zicp.fun:32422/
export const website = window.$website
export const url = BASE_URL //website.url
export const urls = website.urls
export const tip = `
#组件列表动态参数配置，支持参数如下，包含注释符合，注意JSON格式【最后一项不可以逗号结尾】
#【组件中props 属性可以根据需要自由添加、修改，参考elementUI 组件的Attributes属性，不需要时，保留{}】
# 参数只支持在 function (data,params){return data}格式，其它格式，可能无法读取到this对象里面的其它属性，导致无法使用
#【const myChart = this.myChart 对象】
#【this.dataChart 格式化后返回的数据 Array】
#【this.dataModelOthers 动态参数对象Object】==> this.dataModelOthers.自定义参数名，如：name,color等
#【this.dataModelX X轴 String】
#【this.dataModelY Y轴 String】
#【this.dataMulModelY 多Y轴 Array】
/*配置信息不可删除#
    [
      { "field": "color", "title": "颜色","itemRender": { "name": "ColorPicker", "props": { "show-alpha": true } } },
      { "field": "dataLength", "title": "长度","itemRender": { "name": "Slider", "props": { "min": 0,"max": 100 } } },
      { "field": "isOnOrNot", "title": "启用","itemRender": { "name": "Switch", "props": { "active-text": "开","inactive-text": "关" } } },
      { "field": "name", "title": "名称","itemRender": { "name": "Input", "props": { "placeholder": "请输入名称" } } },
      { "field": "model", "title": "数据模型", "itemRender":  { "name": "Select","props": { "placeholder": "请选择模型" } } },
      { "field": "sex", "title": "性别", "itemRender":  { "name": "Select","options": [{ "value": "0", "label": "女" }, { "value": "1", "label": "男" }],"props": { "placeholder": "请选择性别" } } },
      { "field": "age", "title": "年龄", "itemRender":  { "name": "InputNumber","props": { "placeholder": "请输入年龄"}}}
    ]         
 #配置信息不可删除*/

//data为返回的数据
/**
 * 说明：只有样式编辑、数据处理、组件事件、请求头、请求参数方法需要返回函数
 * 静态数据或者配置数据直接返回JSON对象即可
 * 不写的话采用默认加载
*/
#样式编辑、数据处理、组件事件、请求头、请求参数
(data)=>{
  //处理数据逻辑
  return {
    //返回图表的标准数据结构体
  }
}

#事件处理
({name,type,value,data})=>{
  //直接写执行的逻辑即可
  alert(data,name)
}

#提示处理
(name,data) => {
  return 返回需要展示的文本
}

#文本框/图片框/Iframe框等通用数据格式
{
  value:'xxxxx'
}

#柱状图数据格式
{
  "categories": [
    "苹果",
  ],
  "series": [{
    "name": "手机品牌",
    "data": [
      1000879,
    ]
  }]
}

#折线图数据格式
{
  "categories": [
    "苹果",
  ],
  "series": [{
    "name": "手机品牌",
    "data": [
      1000879,
    ]
  }]
}

#饼图数据格式
[{
    "name": "PC",
    "value": 97,
    "url": "http://www.baidu.com"
},{
    "name": "PC",
    "value": 97,
    "url": "http://www.baidu.com"
}]

#象型图数据格式
{
  "categories": [
    "苹果",
  ],
    "series": [{
      "name": "手机品牌",
      "data": [
        1000879,
      ]
    }]
}
#雷达图数据格式
{
  indicator: [{
    name: '销售',
    max: 6500
  }],
  series: [{
    data: [{
      value: [4300, 10000, 28000, 35000, 50000, 19000],
      name: '预算分配（Allocated Budget）'
    }]
  }]
}
#散点图数据格式
[{
  data: [
    [1, 8.04],
    [2, 6.95]
  ]
},
{
  data: [
    [1, 4.04],
    [2, 3.95]
  ]
}]
#漏斗图数据格式
[{
  value: 335,
  name: '直接访问'
},
{
  value: 310,
  name: '邮件营销'
},
{
  value: 234,
  name: '联盟广告'
}]

#轮播图数据格式
[{
  value: '图片地址'
},
{
  value: '图片地址2'
}]

#地图数据格式
[{
  "name": "测试坐标1",
  "value": 1,
  "lng": 118.30078125,
  "lat": 36.91915611148194,
  "zoom": 1
},
{
  "name": "测试坐标2",
  "value": 1,
  "lng": 112.21435546875,
  "lat": 37.965854128749434,
  "zoom": 1
}]
 `