<template>
  <div  :class="isCilent?'Classification Classification1':'Classification'">
    <vxe-toolbar ref="xToolbar1">
      <template #buttons>
        <el-button
          type="primary"
          icon="vxe-icon-add"
          size="mini"
          @click="addProject"
          >&nbsp;
          <!-- 新增 -->
          {{ $t('page.classification.Add') }}
          </el-button
        >
      
        <el-upload
           
           style="margin: 0 10px;"
           :show-file-list="false"
           :httpRequest="httpRequest4"
           action=""
           ref="upload"
           accept=".json"
         >
         <el-button
         icon="el-icon-upload2
"
         type="primary"
         size="mini"
         @click="import_part"
         >
         <!-- 导入 -->
         {{ $t('page.classification.Import') }}
         </el-button
       >
         </el-upload>

       <el-button
         icon="el-icon-download"
         type="primary"
         size="mini"
         @click="export_part"
         >
         <!-- 导出 -->
         {{ $t('page.classification.Export') }}
         </el-button
       >  
              <el-button
              class="pinBtnOnCla"
              icon="el-icon-tickets"
              type="primary"
              size="small"
           
              @click="synchronous()"
            >
              <!-- 同步 -->
              {{ $t('page.classification.Synchronize') }}
            </el-button>
           
      </template>

      <template #tools>
        <span style="color: #333; font-size: 12px">
          <!-- 分组名称 -->
          {{ $t('page.classification.GroupingName') }}
        </span>
        <el-input @keyup.enter.native="onSubmit"  v-model="formData.name" placeholder="分组名称" :placeholder="`${$t('page.classification.GroupingName')}`"  size="mini"   style="width: 180px; margin: 0 10px"></el-input>
        <el-button title="查询"  :title="`${$t('page.classification.Query')}`" type="primary" icon="el-icon-search" @click="onSubmit" size="mini">
              
            </el-button>
            <el-button  type="primary" title="重置" :title="`${$t('page.classification.Reset')}`" icon="el-icon-refresh-right" size="mini" @click="reset">
              
            </el-button>
        <!-- <el-form
          :inline="true"
          :model="formData"
          class="demo-form-inline"
          size="small"
        >
          <el-form-item label="关键字">
            <el-input v-model="formData.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="onSubmit">
              查询
            </el-button>
            <el-button icon="el-icon-refresh-right" @click="reset">
              重置
            </el-button>
            <el-button
              status="primary"
              icon="vxe-icon-refresh"
              size="small"
              @click="findList"
              >刷新</el-button
            >
          </el-form-item>
        </el-form> -->
      </template>
    </vxe-toolbar>

    <vxe-table
      :loading="loading"
      border
      size="small"
      ref="xTable"
      align="center"
      show-overflow
      keep-source
      :row-config="{ isHover: true }"
      :tree-config="{
        transform: true,
        rowField: 'CID',
        parentField: 'CPARENT_ID',
      }"
      :data="tableData"
    ><vxe-column type="checkbox" width="60" ></vxe-column>
      <vxe-column width="80" type="seq" title="序号" :title="`${$t('page.classification.SerialNumber')}`" ></vxe-column>
      <vxe-column field="CNAME" title="分组名称" :title="`${$t('page.classification.GroupingName')}`"  tree-node align="left"></vxe-column>
      <vxe-column field="CNO" title="分组描述" :title="`${$t('page.classification.GroupingDescription')}`" ></vxe-column>
      <vxe-column title="操作" :title="`${$t('page.classification.Operation')}`" >
        <template #default="{ row }">
          <vxe-button
            type="text"
         
            status="primary"
            content="编辑"
            :content="`${$t('page.classification.Edit')}`"
            @click="edit(row)"
          ></vxe-button>
          <vxe-button
            type="text"
          
            status="danger"
            content="删除"
            :content="`${$t('page.classification.Delete')}`"
            @click="deletes(row)"
          ></vxe-button>
        </template>
      </vxe-column>
    </vxe-table>

    <!-- <vxe-pager
      align="right"
      :current-page.sync="page2.currentPage"
      :page-size.sync="page2.pageSize"
      :total="page2.totalResult"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total',
      ]"
    >
      >
    </vxe-pager> -->
    <Model ref="child"  :childEvent="onSubmit" :treeToArray="dataARR"/>
  </div>
</template>

<script>
import Model from "@/components/Modal.vue";
import { getCategory, GetListByQueryJson,DeleteByID , PushData1,Import2,Export2} from "@/api/visual";
import { setLocalStorageStore, getLocalStorageStore,getCookie } from '@/utils/setStore.js'
import { useLanguageSetting } from "@/utils/useLanguageSetting"
export default {
  name: "Classification",
  components: {
    Model,
  },
  data() {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      dataARR:[],
      loading: true,
      formData: {
        name: "",
        nickname: "",
        role: "",
        sex: "",
        age: "",
        num: "",
        date3: "",
        address: "",
      },
      showEdit: false,
      tableData: [],
     
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
    };
  },

  created() {
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.xTable.connect(this.$refs.xToolbar1);
    });
  
  },
  computed: {
   
   isCilent(){
  return getCookie('client')?true:false
 }
 },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    this.getDataList();
  },
  methods: {
      //同步
      synchronous(){
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
      PushData1(ids).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
        }else{
          this.$message.error(res.data.Content)
        }
      })
    },
    httpRequest4(file){
   const reader = new FileReader();
 // 异步处理文件数据
 reader.readAsText(file.file, 'UTF-8');
 // 处理完成后立马触发 onload
 reader.onload = fileReader => {
     const fileData = fileReader.target.result;
     if(fileData){
      var text=JSON.parse(fileData)


      Import2(text).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
          this.getDataList()

        }else{
          this.$message.error(res.data.Content)
        }
      })
     }
    
     // 上面的两个输出相同
 };

    },
    import_part() {
     // console.log(111);
    },
    export_part() {
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
     
     Export2(ids).then(res=>{
      res=res.data
        if(res.code===200&&res.data.Success){
      
        let link = document.createElement("a");
        link.download = "看板分类.json";
        link.href = "data:text/plain," + JSON.stringify(res.data.Datas);
        link.click();
        }else{
          this.$message.error(res.data.Content)
        }
     })
     
    },



    treeToArray(tree) {
      const obj = [];
      tree.forEach((item) => {
        if (item.CHILDS) {
          obj.push(item, ...item.CHILDS);
          // ES6新增的 删除对象的属性 Reflect.deleteProperty(对象，属性名)
          Reflect.deleteProperty(item, "CHILDS");
        } else {
          obj.push(item);
        }
      });

      return obj;
    },

    getDataList() {
  
        var params1 = {
          condition: this.formData.name,
       start:1,length:99999
        };
        GetListByQueryJson(params1).then((res) => {
                res=res.data
          if (res.code === 200 && res.data.Success) {
           this.dataARR=res.data.Datas
            this.tableData = this.treeToArray(
              JSON.parse(JSON.stringify(res.data.Datas))
            );
            
            setTimeout(() => {
            
              this.$refs.xTable.setAllTreeExpand(true)
            }, 200);
           
          }
        });
     
      this.loading = false;

      //this.activeName = (data[0] || {}).categoryValue;
      //  this.getList();
    },
    // 新建项目
    addProject() {
      this.$nextTick(() => {
        this.$refs.child.editEvent(this.tableData, false);
      });
    },
    edit(row) {
      this.$nextTick(() => {
        this.$refs.child.editEvent(row, true);
      });
    },
 
    deletes(value) {
      this.$confirm(`${this.$t('message.PermanentDeletion')}`, `${this.$t('message.Prompt')}`, {
        confirmButtonText: `${this.$t('message.confirmButtonText')}`,
        cancelButtonText: `${this.$t('message.cancelButtonText')}`,
        iconClass: "el-icon-error",
      })
        .then(() => {
        DeleteByID({CID:value.CID}).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message({
            message: res.data.Content,
            type: "success",
          });
          this.onSubmit();
        }else{
          this.$message({
            message:res.data.Content|| res.message.msg,
            type: "error",
          });
        }
      })
        })
        .catch(() => {

        });



    
   
    },

    submitEvent() {
      this.showEdit = false;
      this.$VXETable.modal.message({ content: "保存成功", status: "success" });
      Object.assign(this.selectRow, this.formData);
    },
    onSubmit() {
     

      this.getDataList();
    },
    //重置
    reset() {
      this.formData.name = "";
       this.getDataList();
    },
    handleDelete() {},
  },
};
</script>
<style lang="scss">
.Classification {
  margin: 10px;
  .vxe-table--body-wrapper {
    height: calc(100vh - 230px);
  }
  padding: 5px 10px 0px 10px;
  background: #fff;
  .el-button {
height: 32px;

}
 .vxe-header--column{
  line-height: 20px!important;
 }
 th{
  height: 48px;
 }
}
.Classification1{
  .vxe-table--body-wrapper {
    height: calc(100vh - 130px);
  }
}

 
</style>
