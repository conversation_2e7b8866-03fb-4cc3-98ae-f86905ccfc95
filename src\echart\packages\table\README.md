# 表格插件 (Table Plugin) 文档 Add by <PERSON> 2025-07-08

## 概述

表格插件是一个功能丰富的数据展示组件，基于 Element UI 的 el-table 组件开发，支持数据滚动、条件格式化、样式自定义等高级功能。适用于数据大屏、仪表板等场景。

## 核心文件

- **主组件**: `src/echart/packages/table/index.vue` - 表格渲染组件
- **配置组件**: `src/option/components/table.vue` - 表格属性配置界面
- **列配置组件**: `src/option/components/tableColumnsTabOptions.vue` - 表格列的高级配置
- **配置定义**: `src/option/config.js` - 表格相关配置选项

## 功能特性

### 1. 基础功能
- ✅ 数据展示与排序
- ✅ 表头显示/隐藏控制
- ✅ 边框样式控制
- ✅ 圆角表格支持
- ✅ 排名列显示
- ✅ 数据追加模式

### 2. 滚动功能
- ✅ 自动滚动开关
- ✅ 滚动速度控制 (scrollSpeed)
- ✅ 滚动间隔控制 (scrollTime)
- ✅ 鼠标悬停暂停滚动
- ✅ 智能滚动重置

### 3. 样式定制
- ✅ 表头样式 (背景色、字体色、字体大小、对齐方式)
- ✅ 表体样式 (奇偶行颜色、字体色、字体大小、对齐方式)
- ✅ 列头高度自定义
- ✅ 单元格自动换行 (wordBreak)

### 4. 高级功能
- ✅ 条件格式化 (支持8种条件判断)
- ✅ 自定义格式化函数
- ✅ 多条件配置 (Tab页形式)
- ✅ 动态颜色控制
- ✅ 模型数据自动加载
- ✅ **合并相同列功能** (智能单元格合并)

## 配置属性详解

### 基础配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `index` | Boolean | true | 是否显示排名列 |
| `indexWidth` | Number | 80 | 排名列宽度 |
| `border` | Boolean | false | 是否显示边框 |
| `roundedTable` | Boolean | false | 是否启用圆角 |
| `dataAppend` | Boolean | false | 数据追加模式 |
| `showHeader` | Boolean | true | 是否显示表头 |
| `count` | Number | 4 | 显示行数 |
| `enableMergeColumn` | Boolean | false | 🆕 是否启用合并列功能 |

### 滚动配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `scroll` | Boolean | false | 是否开启滚动 |
| `scrollTime` | Number | 3000 | 滚动间隔(毫秒) |
| `scrollSpeed` | Number | 1 | 滚动速度(像素/次) |

### 表头样式配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `headerBackground` | String | "#050e18" | 表头背景色 |
| `headerColor` | String | "#69bfe7" | 表头字体色 |
| `headerFontSize` | Number | 20 | 表头字体大小 |
| `headerColHeight` | Number | - | 列头高度 |
| `headerTextAlign` | String | "center" | 表头对齐方式 |

### 表体样式配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `bodyColor` | String | "#69bfe7" | 表体字体色 |
| `bodyFontSize` | Number | 18 | 表体字体大小 |
| `bodyTextAlign` | String | "center" | 表体对齐方式 |
| `nthColor` | String | "#09192c" | 奇数行背景色 |
| `othColor` | String | "#142a40" | 偶数行背景色 |

### 列配置

每列支持以下属性：

| 属性名 | 类型 | 说明 |
|--------|------|------|
| `label` | String | 列标题 |
| `prop` | String | 数据字段名 |
| `width` | Number | 列宽度 |
| `hide` | Boolean | 是否隐藏列 |
| `wordBreak` | Boolean | 是否自动换行 |
| `formatter` | Function | 格式化函数 |
| `mergeColumn` | Boolean | 🆕 是否启用合并列功能 |

### 条件格式化

支持8种条件判断：

| 条件值 | 说明 |
|--------|------|
| 1 | 大于 |
| 2 | 小于 |
| 3 | 等于 |
| 4 | 包含 |
| 5 | 大于等于 |
| 6 | 小于等于 |
| 7 | 不等于 |
| 8 | 不包含 |

每个条件可配置：
- `rowbackground` - 行背景色
- `rowfont` - 行字体色
- `cellbackground` - 单元格背景色
- `cellfont` - 单元格字体色
- `condition` - 条件类型
- `value` - 比较值

## 使用示例

### 基础用法

```javascript
// 表格配置示例
const tableOption = {
  showHeader: true,
  border: true,
  index: true,
  indexWidth: 60,
  count: 5,
  scroll: true,
  scrollTime: 2000,
  scrollSpeed: 2,
  headerBackground: "#1f4e79",
  headerColor: "#ffffff",
  headerFontSize: 16,
  bodyColor: "#333333",
  bodyFontSize: 14,
  nthColor: "#f5f5f5",
  othColor: "#ffffff",
  column: [
    {
      label: "姓名",
      prop: "name",
      width: 120
    },
    {
      label: "年龄", 
      prop: "age",
      width: 80
    },
    {
      label: "部门",
      prop: "department",
      width: 150
    }
  ]
}
```

### 条件格式化示例

```javascript
// 列配置中添加条件格式化
{
  label: "状态",
  prop: "status",
  condition: 3, // 等于
  value: "正常",
  cellbackground: "#67C23A", // 绿色背景
  cellfont: "#ffffff"
}
```

### 自定义格式化函数

```javascript
// 格式化函数示例
formatter: `(item, row) => {
  if (row.score >= 90) {
    row.font_Color = "#67C23A"; // 绿色
    return "优秀";
  } else if (row.score >= 80) {
    row.font_Color = "#E6A23C"; // 橙色
    return "良好";
  } else {
    row.font_Color = "#F56C6C"; // 红色
    return "需改进";
  }
}`
```

## 快速开始

### 1. 使用dist文件

```html
<!DOCTYPE html>
<html>
<head>
    <title>表格插件示例</title>
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="./dist/lib/index.css">
</head>
<body>
    <div id="app"></div>

    <!-- 引入Vue和Element UI -->
    <script src="./dist/cdn/vue/vue.min.js"></script>
    <script src="./dist/cdn/element-ui/index.js"></script>

    <!-- 引入表格插件 -->
    <script src="./dist/lib/index.umd.min.js"></script>

    <script>
        // 表格配置
        const tableConfig = {
          showHeader: true,
          index: true,
          count: 5,
          scroll: true,
          scrollTime: 2000,
          scrollSpeed: 2,
          column: [
            { label: "姓名", prop: "name", width: 100 },
            { label: "年龄", prop: "age", width: 80 },
            { label: "部门", prop: "department", width: 120 }
          ]
        };

        // 表格数据
        const tableData = [
          { name: "张三", age: 25, department: "技术部" },
          { name: "李四", age: 30, department: "销售部" },
          { name: "王五", age: 28, department: "市场部" }
        ];

        // 初始化表格
        // 具体使用方法参考 demo 目录中的示例
    </script>
</body>
</html>
```

### 2. 查看演示案例

项目提供了完整的演示案例，位于 `demo` 目录：

- **demo/table-basic-example.html** - 基础功能演示
- **demo/table-dist-example.html** - 生产环境使用示例
- **demo/table-merge-column-example.html** - 🆕 合并列功能演示

直接在浏览器中打开这些文件即可查看效果。

### 3. 在Vue项目中使用

```javascript
// 在Vue组件中使用
import TableComponent from './src/echart/packages/table/index.vue'

export default {
  components: {
    TableComponent
  },
  data() {
    return {
      tableOption: {
        showHeader: true,
        index: true,
        count: 5,
        // ... 其他配置
      },
      tableData: [
        // 数据数组
      ]
    }
  }
}
```

## 扩展开发指南

### 添加新的条件类型

1. 在 `src/option/config.js` 的 `dicOption.codition` 中添加新条件
2. 在 `src/echart/packages/table/index.vue` 的 `getColorByType` 方法中添加处理逻辑

### 添加新的样式属性

1. 在配置组件中添加新的表单项
2. 在主组件的样式方法中应用新属性
3. 更新默认配置

### 性能优化建议

- 大数据量时建议开启虚拟滚动
- 合理设置滚动间隔避免性能问题
- 复杂格式化函数建议使用防抖处理

## 技术栈

- Vue.js 2.x
- Element UI
- Monaco Editor (代码编辑器)
- Clone Deep (深拷贝工具)

## 版本信息

当前版本基于 CI.Web.Plugins.Bulletin 插件系统开发，支持多语言国际化。

## 高级特性详解

### 多条件配置系统

表格插件支持为每一列配置多个条件，通过Tab页的形式管理：

1. **默认配置Tab**: 基础的条件格式化设置
2. **扩展配置Tab**: 可添加多个额外的条件配置
3. **条件互斥**: 已选择的条件在其他Tab中会被禁用，避免冲突

### 滚动机制详解

```javascript
// 滚动控制逻辑
setTime() {
  // 延时3秒后开始滚动
  await this.sleep(3000);

  // 滚动到底部后停顿3秒，然后重置到顶部
  if (h1 + 10 >= h2) {
    setTimeout(() => {
      divData.scrollTop = 0;
      this.setTime(); // 重新开始滚动
    }, 3000);
  }

  // 按行滚动，每次滚动一行高度
  if (top >= this.cellHeight && this.scrollTime) {
    // 停顿指定时间后继续滚动
    setTimeout(() => {
      this.setTime();
    }, this.scrollTime);
  }
}
```

### 格式化函数高级用法

```javascript
// 复杂格式化示例
formatter: `(item, row) => {
  // 1. 数值格式化
  if (typeof row[item.prop] === 'number') {
    return row[item.prop].toLocaleString();
  }

  // 2. 日期格式化
  if (item.prop.includes('date')) {
    return new Date(row[item.prop]).toLocaleDateString();
  }

  // 3. 状态图标
  if (item.prop === 'status') {
    const statusMap = {
      'online': '<i class="el-icon-success" style="color: green;"></i> 在线',
      'offline': '<i class="el-icon-error" style="color: red;"></i> 离线'
    };
    return statusMap[row[item.prop]] || row[item.prop];
  }

  // 4. 动态颜色控制
  if (row.priority === 'high') {
    row.font_Color = '#F56C6C';
  }

  return row[item.prop];
}`
```

### 数据源集成

表格插件支持多种数据源：

1. **静态数据**: 直接在配置中定义数据
2. **API接口**: 通过HTTP请求获取数据
3. **自定义数据接口**: 支持复杂的数据处理逻辑
4. **全局数据源**: 与其他组件共享数据源
5. **WebSocket**: 实时数据更新

### 国际化支持

插件内置多语言支持，配置项会根据当前语言环境自动切换：

```javascript
// 多语言配置示例
{
  label: this.$t('components.table.OpenRanking'), // 开启排名
  prop: 'index'
}
```

## 常见问题与解决方案

### Q1: 表格滚动不流畅怎么办？
**A**: 调整 `scrollSpeed` 和 `scrollTime` 参数，建议 scrollSpeed 设置为 1-3，scrollTime 设置为 1000-3000ms。

### Q2: 如何实现表格数据的实时更新？
**A**: 使用 WebSocket 数据源或设置定时器定期调用 API 接口更新数据。

### Q3: 条件格式化不生效怎么办？
**A**: 检查条件配置是否正确，确保 `condition` 和 `value` 字段已正确设置，数据类型匹配。

### Q4: 如何隐藏某一列？
**A**: 设置列的 `hide` 属性为 `true`，或者将 `width` 设置为 `1` 实现视觉隐藏。

### Q5: 格式化函数中的HTML不显示？
**A**: 确保使用 `v-html` 指令，并且格式化函数返回有效的HTML字符串。

## 性能优化建议

1. **大数据量处理**:
   - 启用分页或虚拟滚动
   - 合理设置 `count` 属性限制显示行数

2. **格式化函数优化**:
   - 避免在格式化函数中进行复杂计算
   - 使用缓存机制存储计算结果

3. **滚动性能优化**:
   - 合理设置滚动间隔，避免过于频繁的DOM操作
   - 在数据量大时考虑关闭滚动功能

4. **内存管理**:
   - 及时清理定时器和事件监听器
   - 避免在组件中创建过多的响应式数据

## 注意事项

1. 格式化函数中可通过设置 `row.font_Color` 来动态控制字体颜色
2. 条件格式化支持多个Tab页配置，每个Tab页可设置不同的条件
3. 滚动功能会在鼠标悬停时自动暂停
4. 列宽设置为1时可实现隐藏列的效果
5. 支持从全局数据源和自定义数据接口加载模型数据
6. 测试案例全放到demo目录下
7. 使用当前打包好的dist文件，参考其它案例使用方法
8. 条件格式化的优先级：Tab页配置 > 默认配置 > 全局样式
9. 格式化函数支持异步操作，但建议避免使用以保证性能
10. 🆕 合并列功能需要数据按合并字段排序，空值不参与合并

## 🆕 合并列功能详解

**合并列功能**是表格插件的重要新特性，可以自动合并相同值的相邻单元格，特别适用于分组数据展示，如生产线工序、部门分组等场景。

### 功能特点：
- **智能合并**: 自动识别相同值的相邻单元格进行合并
- **多列支持**: 可以为不同列单独配置是否启用合并
- **向下合并**: 从上到下查找相同值进行合并
- **空值处理**: 空值或undefined不参与合并
- **视觉优化**: 合并后的表格更加清晰，减少重复信息

### 配置方式：

#### 1. 全局启用合并列功能
```javascript
tableConfig.enableMergeColumn = true;
```

#### 2. 为特定列启用合并
```javascript
column: [
  {
    label: "工序",
    prop: "workstation",
    mergeColumn: true  // 启用此列的合并功能
  },
  {
    label: "工站",
    prop: "station",
    mergeColumn: true  // 启用此列的合并功能
  }
]
```

### 使用示例：

```javascript
// 适合合并的数据结构（需要按合并字段排序）
const productionData = [
  { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061-001" },
  { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061-002" },
  { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062-001" },
  { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063-001" }
];

// 表格配置
const tableConfig = {
  enableMergeColumn: true,
  column: [
    { label: "工序", prop: "workstation", mergeColumn: true },
    { label: "工站", prop: "station", mergeColumn: true },
    { label: "流程卡号", prop: "serialNumber", mergeColumn: false }
  ]
};
```

### 效果展示：
```
┌────────┬──────────┬───────────┐
│ 工序   │ 工站     │ 流程卡号  │
├────────┼──────────┼───────────┤
│        │          │ J25061-001│
│免片线路├──────────┼───────────┤
│        │前处理1线 │ J25061-002│
│        ├──────────┼───────────┤
│        │前处理2线 │ J25062-001│
├────────┼──────────┼───────────┤
│外层线路│前处理3线 │ J25063-001│
└────────┴──────────┴───────────┘
```

### 技术实现

合并列功能通过Element UI的`span-method`属性实现：

```javascript
// 合并列方法
spanMethod({ row, column, rowIndex, columnIndex }) {
  if (!this.option.enableMergeColumn) {
    return [1, 1];
  }

  // 获取当前列的配置
  const currentColumn = this.option.column.find(col => col.prop === column.property);
  if (!currentColumn || !currentColumn.mergeColumn) {
    return [1, 1];
  }

  // 计算合并的行数
  const prop = column.property;
  const currentValue = row[prop];

  // 如果当前值为空，不合并
  if (currentValue === null || currentValue === undefined || currentValue === '') {
    return [1, 1];
  }

  // 查找连续相同值的行数
  let rowspan = 1;

  // 向下查找相同值
  for (let i = rowIndex + 1; i < this.dataChart.length; i++) {
    if (this.dataChart[i][prop] === currentValue) {
      rowspan++;
    } else {
      break;
    }
  }

  // 向上查找，如果上一行有相同值，则当前行不显示
  for (let i = rowIndex - 1; i >= 0; i--) {
    if (this.dataChart[i][prop] === currentValue) {
      return [0, 0]; // 不显示当前单元格
    } else {
      break;
    }
  }

  return [rowspan, 1];
}
```

### 注意事项

1. **数据排序**: 使用合并列功能前，确保数据按照需要合并的列进行排序
2. **空值处理**: 空值、null、undefined不会参与合并
3. **性能考虑**: 大数据量时合并计算可能影响性能，建议合理使用
4. **边框显示**: 建议启用边框以更好地显示合并效果
5. **列宽设置**: 合并列建议设置固定宽度以保持视觉效果
6. **滚动兼容**: 合并列功能与滚动功能完全兼容
7. **条件格式化**: 合并列与条件格式化可以同时使用

////////////////////
数据格式:tableData: [
        {
          key: 59,
          value: { id: 'BOGE082342A1', status: 'online', color: 'orange' }
        },
        {
          key: 60,
          value: { id: 'C08D139868C0', status: 'offline', color: 'red' }
        },
        {
          key: 61,
          value: { id: 'A04E160349A1', status: 'online', color: 'green' }
        }
      ], 修改VUE2组件，使用基础样式，返回格式如：<template>
    <div class="test">
        <h1 @click="handleClick">{{$parent.dataChart}}<h1>
    </div>
</template>
<script>
export default{
    data(){
        return{
            
        }
    },
    created(){
        console.log(this.$parent);
    },
    methods:{
        handleClick(){
            this.$message.success(this.dataChart.name)
        }
    }
}
</script>
<style>
    .test{
        text-align:center;
        color:red;
        font-size:40px;
    }
</style> 需要返回的效果如图 c:\Users\<USER>\Documents\WeChat Files\wxid_m17fz3mthi0d22\FileStorage\Temp\1752027933715.png，使用DIV，颜色使用使用数据字段:color
//////////////////////////////////////////////
