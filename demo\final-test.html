<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终验证测试</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background: #000;
            color: #0f0;
        }
        .result {
            border: 2px solid;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .pass { border-color: #0f0; background: rgba(0, 255, 0, 0.1); }
        .fail { border-color: #f00; background: rgba(255, 0, 0, 0.1); color: #f00; }
        .code {
            background: #222;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            border-left: 4px solid #555;
        }
        .before { border-left-color: #ff0; }
        .after { border-left-color: #0ff; }
    </style>
</head>
<body>
    <h1>🔬 最终验证测试</h1>
    <p>直接测试用户反馈的问题是否已解决</p>

    <div id="results"></div>

    <script>
        // 最新的格式化函数 - 使用占位符保护箭头函数
        function formatJavaScript(code) {
            if (!code || code.trim() === '') return code;

            try {
                console.log('开始格式化:', code);

                // 1. 保护字符串
                const strings = [];
                let stringIndex = 0;
                let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
                    const placeholder = `__STR_${stringIndex++}__`;
                    strings.push({ placeholder, content: match });
                    return placeholder;
                });
                console.log('保护字符串后:', formatted);

                // 2. 保护箭头函数 - 关键步骤
                const arrows = [];
                let arrowIndex = 0;
                formatted = formatted.replace(/\s*=>\s*/g, () => {
                    const placeholder = `__ARROW_${arrowIndex++}__`;
                    arrows.push({ placeholder, content: ' => ' });
                    console.log('发现箭头函数，替换为:', placeholder);
                    return placeholder;
                });
                console.log('保护箭头函数后:', formatted);

                // 3. 基本空白处理
                formatted = formatted.replace(/\s+/g, ' ').trim();
                console.log('空白处理后:', formatted);

                // 4. 处理操作符
                formatted = formatted
                    .replace(/\s*([+\-*/%])\s*/g, ' $1 ')
                    .replace(/\s*(==|!=|===|!==|<=|>=|<|>)\s*/g, ' $1 ')
                    .replace(/\s*(&&|\|\|)\s*/g, ' $1 ')
                    .replace(/\s*([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))\s*=\s*([^=])/g, '$1 = $2');
                console.log('操作符处理后:', formatted);

                // 5. 处理标点
                formatted = formatted
                    .replace(/,(?!\s)/g, ', ')
                    .replace(/;(?!\s*\n)/g, ';\n')
                    .replace(/\{(?!\s*\n)/g, ' {\n')
                    .replace(/\}(?!\s*\n)/g, '\n}')
                    .replace(/\s*\(\s*/g, '(')
                    .replace(/\s*\)\s*/g, ')');
                console.log('标点处理后:', formatted);

                // 6. 最终清理
                formatted = formatted
                    .replace(/\s*{\s*/g, ' {\n')
                    .replace(/\s*}\s*/g, '\n}')
                    .replace(/\s*;\s*/g, ';\n')
                    .replace(/\s*,\s*/g, ', ');
                console.log('最终清理后:', formatted);

                // 7. 恢复箭头函数
                arrows.forEach(({ placeholder, content }) => {
                    console.log('恢复箭头函数:', placeholder, '->', content);
                    formatted = formatted.replace(placeholder, content);
                });
                console.log('恢复箭头函数后:', formatted);

                // 8. 恢复字符串
                strings.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });
                console.log('恢复字符串后:', formatted);

                // 9. 缩进
                const lines = formatted.split('\n');
                let indent = 0;
                const result = lines.map(line => {
                    line = line.trim();
                    if (!line) return '';
                    if (line.includes('}')) indent = Math.max(0, indent - 1);
                    const indented = '    '.repeat(indent) + line;
                    if (line.includes('{')) indent++;
                    return indented;
                }).join('\n');

                console.log('最终结果:', result);
                return result;

            } catch (error) {
                console.error('格式化错误:', error);
                return code;
            }
        }

        // 验证函数
        function validate(code) {
            try {
                new Function(code);
                return true;
            } catch {
                return false;
            }
        }

        // 检查箭头函数错误
        function checkArrowError(code) {
            return code.includes('= >') || code.includes(' = >');
        }

        // 运行测试
        function runTest() {
            const testCode = '(data,params,refs)=>{return data}';
            
            console.log('\n=== 开始测试 ===');
            console.log('原始代码:', testCode);
            
            const formatted = formatJavaScript(testCode);
            
            console.log('格式化结果:', formatted);
            
            const originalValid = validate(testCode);
            const formattedValid = validate(formatted);
            const hasArrowError = checkArrowError(formatted);
            
            console.log('原始代码语法正确:', originalValid);
            console.log('格式化后语法正确:', formattedValid);
            console.log('包含箭头函数错误:', hasArrowError);
            
            const passed = originalValid && formattedValid && !hasArrowError;
            
            console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败');
            
            // 显示结果
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="result ${passed ? 'pass' : 'fail'}">
                    <h2>${passed ? '✅ 测试通过' : '❌ 测试失败'}</h2>
                    <h3>用户反馈的问题:</h3>
                    <div class="code before">原始: ${testCode}</div>
                    <div class="code after">格式化: ${formatted}</div>
                    <h3>验证结果:</h3>
                    <div>原始语法: ${originalValid ? '✅' : '❌'}</div>
                    <div>格式化语法: ${formattedValid ? '✅' : '❌'}</div>
                    <div>箭头函数: ${hasArrowError ? '❌ 有错误' : '✅ 正确'}</div>
                    <div><strong>总体: ${passed ? '✅ 问题已解决' : '❌ 问题仍存在'}</strong></div>
                </div>
            `;
            
            return passed;
        }

        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('开始最终验证测试...');
            const result = runTest();
            
            if (result) {
                console.log('\n🎉 恭喜！箭头函数格式化问题已彻底解决！');
            } else {
                console.log('\n⚠️ 问题仍然存在，需要进一步调试。');
            }
        });
    </script>
</body>
</html>
