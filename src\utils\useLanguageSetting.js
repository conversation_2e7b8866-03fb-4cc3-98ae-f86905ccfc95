import { getLocalStorageStore } from '@/utils/setStore'

export function useLanguageSetting(vueInstance) {
    return {
        // 设置默认语言
        setLanguage(lang) {
            //debugger
            let _language= getLocalStorageStore('language')
            if(_language=='en'){
                vueInstance.$i18n.locale = 'en'
            }else{
                vueInstance.$i18n.locale = 'zh'
            }
            // 强制设置默认语言
            if(lang){
                vueInstance.$i18n.locale = lang
            }
        }
    }
}