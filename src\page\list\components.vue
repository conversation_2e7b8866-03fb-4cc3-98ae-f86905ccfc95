<template>
  <div style="background-color: #fff;padding: 10px;margin:10px;">
    <div>
      <div class="flex justify-between items-center ">
        <div class="flex"
             v-if="menu">
          <div>
            <el-button
                type="primary"
                icon="vxe-icon-add"
                size="small"
                 @click="handleAdd"
                >&nbsp;
                <!-- 新增 -->
                {{ $t('page.components.Add') }}
                </el-button
            >
          </div>
        </div>
        <div :style="{marginBottom:(!menu?'10px':'0px')}" class="content__page">
          <div class="list-search">
            <el-input style="width: 220px;" v-model="search.name"
                      size="small"
                      clearable
                      @keyup.enter.native="getList"
                      placeholder="请输入名称"
                      :placeholder="`${$t('page.components.PleaseEnterName')}`"
                      >
              <i slot="suffix"
                 @click="getList"
                 class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
         
        </div>
      </div>
      <el-tabs class=""
               v-model="active"
               v-if="menu"
               @tab-click="handleTabClick">
        <el-tab-pane v-for="(item,index) in dicData"
                     :key="index"
                     :label="item.label"
                     :name="item.value"></el-tab-pane>
      </el-tabs>
    </div>
    <div style="height: calc(100vh - 230px);overflow-y: auto;" class="flex  flex-col justify-between ">
       <div  class="flex-1  w-full"
             v-loading="loading"
             v-bind="$loadingParams">
      <div style=" overflow-y: auto;" class="flex flex-wrap w-full  ">
        <template v-if="data.length>0">
          <div style="height: 250px;width: 350px; margin-right: 10px; margin-bottom: 10px;" class=" flex flex-col overflow-hidden border-gray "
               v-for="(item,index) in data"
               @mouseenter="item._menu=true"
               @mouseleave="item._menu=false"
               :key="index">
            <div style="width: 350px;" class="flex flex-col relative">
              <img height="200px" :src="getIMGUrl(item) || `${$router.options.base}img/components-default.png`"
                   alt="" />
              <div style="border:2px solid #00baff;width: 346px;" class=" w-full h-full absolute top-0 left-0 right-0"
                   v-if="item._menu">
                <div style="padding: 10px;background-color: #000;"  class="flex justify-end cursor-p">
                  <el-tooltip content="预览" :content="`${$t('page.components.Preview')}`">
                    <i style="color: white;" class="el-icon-view"
                       @click="handleView(item,index)"></i>
                  </el-tooltip>
                </div>
                <div style="margin-top: 50px;" class="flex  justify-center items-center">
                  <template v-if="menu">
                    <div style="margin-right: 10px;padding:5px; background: linear-gradient(to bottom, #2277d8 0%, #0d98d7 100%)" class="cursor-p"
                         @click="handleEdit(item,index)">
                      <el-tooltip content="编辑" :content="`${$t('page.components.Edit')}`">
                            <i class="el-icon-edit-outline text-white"></i>
                      </el-tooltip>
                    </div>
                    <div style="margin-right: 10px;padding:5px; background: linear-gradient(to bottom, #2277d8 0%, #0d98d7 100%)" class=" cursor-p"
                         @click="rowDel(item,index)">
                   
                      <el-tooltip content="删除" :content="`${$t('page.components.Delete')}`">
                        <i class="el-icon-delete text-white"></i>
                      </el-tooltip>
                    </div>
                  </template>
                  <div v-else
                      
                       style="color:#fff; margin-right: 10px;padding:5px; background: linear-gradient(to bottom, #2277d8 0%, #0d98d7 100%)" class="cursor-p"
                       @click="handleRow(item,index)">
                    
                    <el-tooltip content="选择" :content="`${$t('page.components.Select')}`">
                      <i class="el-icon-check"></i>
                      </el-tooltip>
                  </div>
                </div>

              </div>
            </div>
            <div style="font-size: 16px;font-weight: bold;background-color: black;color: #fff;border-top: 1px solid #ccc;" class="flex w-full  h-full justify-between items-center">
              <span style="margin-left: 10px;" class="content__name">{{item.CNAME}}</span>
              <span style="margin-right: 10px;" class="content__status">
                {{getTypeLabel(item.CTYPE)}}
              </span>
            </div>
          </div>
        </template>
        <el-empty v-else
                 style="height: calc(100vh - 302px);"
                  class="flex items-center w-full h-full"
                  description="暂无数据" :description="`${$t('page.components.NoDataAvailable')}`">
          <template #image>
            <svg-icon icon-class="empty" />
          </template>
        </el-empty>
      </div>
      </div>
      <div v-if="page.total/page.total>1" class="w-full flex justify-end" style="height: 30px; ">
        <!-- v-if="page.total/page.total>1" -->
        <el-pagination v-if="page.total/page.total>1"
                         layout="total, prev, pager, next"
                         style="color: #000;"
                         size="small"
                         @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :page-size="page.size"
                         :current-page.sync="page.page"
                         :total="page.total">
      </el-pagination>
      </div>
    
    </div>
   
    <el-dialog title="编辑组件"
               :title="`${$t('page.components.EditComponent')}`"
               width="70%"
               destroy-on-close
               append-to-body
               :close-on-click-modal="false"
               :visible.sync="dialog">
      <avue-form :option="option"
                 v-model="form"
               
                 @reset-change="handleReset"
                 @submit="handleSubmit">
        <template slot-scope="{}"
                  slot="content">
          <monaco-editor v-model="form.content" @blur="handleBlur"

                         :language="options.language"
                         :height="options.height"
                         :options="options"></monaco-editor>
          <br />
          <div v-if="form.type==1">
            <span>
              <a href="https://echarts.apache.org/examples/zh/index.html"
                 target="_blank">
                 <!-- 官方组件库 -->
                 {{ $t('page.components.OfficialComponentLibrary') }}
                </a>
            </span>&nbsp;&nbsp;
            <span>
              <a href="http://chart.majh.top"
                 target="_blank">
                 {{ $t('page.components.ThirdPartyComponentLibrary') }}1</a>
            </span>&nbsp;&nbsp;
            <span>
              <a href="http://echarts.zhangmuchen.top"
                 target="_blank"> {{ $t('page.components.ThirdPartyComponentLibrary') }}2</a>
            </span>&nbsp;&nbsp;
            <span>
              <a href="https://madeapie.com"
                 target="_blank"> {{ $t('page.components.ThirdPartyComponentLibrary') }}3</a>
            </span>&nbsp;&nbsp;
            <span>
              <a href="http://ppchart.com"
                 target="_blank"> {{ $t('page.components.ThirdPartyComponentLibrary') }}4</a>
            </span>
          </div>
          <el-button icon="el-icon-edit"
                     type="primary"
                     size="small"
                     @click="$refs.code.handleOpen()">
                     <!-- 放大编辑器 -->
                     {{ $t('page.components.EnlargeEditor') }}
                    </el-button>
        </template>
        <div slot-scope="{}"
             class="view"
             slot="view"
             v-if="reload">
          <div class="components__tip"
               v-if="error">{{error}}</div>
          <avue-echart-vue :option="vueOption"
                           v-if="form.type==0"
                           @error-change="errorChange"
                           width="100%"
                           height="100%"></avue-echart-vue>
          <avue-echart-common :echart-formatter="getFunction(vueOption.content)"
                              v-else-if="form.type==1"
                              @error-change="errorChange"
                              width="100%"></avue-echart-common>
          <!-- <avue-echart-html :option="vueOption"
                            @error-change="errorChange"
                            v-else-if="form.type==2"
                            width="100%"
                            height="100%"></avue-echart-html> -->
        </div>
      </avue-form>
    </el-dialog>

    <code-edit ref="code"
               v-model="form"></code-edit>
  </div>
</template>

<script>
// import codeEdit from '@/page/group/code';
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import { url as serverUrl } from "@/config";
import codeEdit from '@/page/components/code-edit'
import AvueEchartVue from '@/echart/packages/vue';
import AvueEchartCommon from '@/echart/packages/common';
// import AvueEchartHtml from '@/echart/packages/html';
import MonacoEditor from '@/page/components/monaco-editor'
import { getFunction,dataURLtoFile } from '@/utils/utils'
// import { uploadImg } from '@/api/visual'
import { UploadFile,getList, getObj, addObj, delObj, updateObj } from '@/api/components'
const dicData = [
  {
    label: 'vue',
    value: 0,
    content: "<template>\n    <div>\n        \n    </div>\n</template>\n<script>\n    export default{\n        data(){\n            return {}\n        },\n        created(){\n\n        },\n        methods:{\n\n        }\n    }\n<\/script>\n\n<style>\n\n<\/style>"
  },
  {
    label: 'echart',
    value: 1,
    content: "function (data,params){\n    const myChart = this.myChart;\n    return option;\n}"
  },
  // {
  //   label: 'html组件',
  //   value: 2,
  //   content: "<div style=''>html</div>"
  // }
]
export default {
  components: {
    codeEdit,
    AvueEchartCommon,
    AvueEchartVue,
    //AvueEchartHtml,
    MonacoEditor
  },
  props: {
    menu: {
      type: Boolean,
      default: true
    },
    activeName: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      selectedRow:{CID:null},
      serverUrl:serverUrl,
      search: {},
      error: '',
      loading: false,
      reload: false,
      active: 0,
      dicData,
      options: {
        height: 300,
        language: 'javascript',
      },
      content: '',
      index: -1,
      isEdit: false,
      form: {},
      vueOption: {},
      data: [],
      type: '',
      page: {
        size: 50,
        page: 1,
        total: 0
      },
      dialog: false,
      option: {}
    }
  },
  created () {
    this.getFunction = getFunction
    if (!this.menu) {
      this.active = this.activeName
    }
    this.getList()
  },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    this.option= {
        labelWidth: 120,
        column: [
          {
            label: this.$t('page.components.ComponentName'),//'组件名称',
            prop: 'name',
            row: true,
            rules: [{
              required: true,
              message: this.$t('page.components.PleaseEnterComponentName'),//"请输入组件名称",
              trigger: "blur"
            }]
          },
          {
            label: this.$t('page.components.ComponentType'),//'组件类型',
            prop: 'type',
            type: 'select',
            dicData: dicData,
            rules: [{
              required: true,
              message: this.$t('page.components.PleaseSelectComponentType'),//"请选择组件类型",
              trigger: "blur"
            }]
          },
          {
            label: this.$t('page.components.ComponentData'),//'组件数据',
            prop: 'content',
            span: 24
          },
          {
            label: this.$t('page.components.ComponentPreview'),//'组件预览',
            prop: 'view',
            span: 24
          }]
      }
  },
  watch: {
    'form.content' (val) {
      //this.handleOption()
    },
    'form.type' (val) {
      if (this.isEdit) {
        this.isEdit = false;
        return
      }
      let obj = dicData.find(ele => ele.value == val) || {}
      this.form.content = obj.content
    }
  },
  methods: {
    // 清空回调函数
    handleReset(params){
      this.form.content=""
    },
    // 离开焦点后触发加载修改后的数据
    handleBlur(params){
      this.handleOption()
    },
    getIMGUrl(item){
      let _originPath =window.location.origin
      let _pathUrl = item.CIMG_URL.indexOf('http') > -1 ? item.value.replace(_originPath, '') :  item.CIMG_URL.replace(_originPath, '')
      if(_pathUrl.indexOf('/img/bg/')>-1 && item.CIMG_URL.indexOf('http') == -1){
        _pathUrl=  'subapp/bulletin'+_pathUrl//兼容旧版路径
      }else{
        _pathUrl=  _pathUrl.replace("/",'')
      }
      if(!item.CIMG_URL){
         _pathUrl ="subapp/bulletin/img/components-default.png"
      }
      if(item.CIMG_URL.includes('components-default.png')){
         _pathUrl ="subapp/bulletin/img/components-default.png"
      }
      return this.serverUrl+_pathUrl
    },
    errorChange (val) {
      if (val) console.log(val)
      this.error = val
    },
    handleTabClick () {
      this.page.page = 1
      this.getList()
    },
    getTypeLabel (type) {
      return dicData.find(ele => ele.value == type).label
    },
    handleRow (row, index) {
      this.selectedRow =row
      this.$emit('change', row)
    },
    vaildData (id) {
      return [0, 1, 2, 3, 4].includes(id)
    },
    rowDel (row, index) {
      // if (this.vaildData(index) && this.$website.isDemo) {
      //   this.$message.error(this.$website.isDemoTip)
      //   return false;
      // }
      this.$confirm(`${this.$t('message.PermanentDeletion')}`, `${this.$t('message.Prompt')}`, {
        confirmButtonText: `${this.$t('message.confirmButtonText')}`,
        cancelButtonText: `${this.$t('message.cancelButtonText')}`,
        type: 'warning'
      }).then(() => {
        delObj(row).then(() => {
          this.$message.success(`${this.$t('message.DeletedSuccessfully')}`);//'删除成功'
          this.getList()
        })
      })
    },
    handleOption () {
      console.log("==========handleOption change...===========")
      this.reload = false;
      this.vueOption = this.deepClone(this.form)
      this.$nextTick(() => {
        this.reload = true;
      })
    },
    getImg () {
      return new Promise(resolve => {
        html2canvas(document.querySelector('.view'), {
          useCORS: true,
          backgroundColor: null,
          logging: false,
          allowTaint: true
        }).then(canvas => {
          let result = canvas.toDataURL('image/jpeg', 0.1);
          if (result.length < 10) resolve('')
          var file = dataURLtoFile(result, new Date().getTime() + '.jpg');
          var formdata = new FormData();
          formdata.append('file', file)
          UploadFile(formdata).then(res => {
             const data = res.data.data;
             const url = data.Content;
            resolve(url)
          })
        })
      })
    },
    getImgByte () {
      return new Promise(resolve => {
        html2canvas(document.querySelector('.view'), {
          useCORS: true,
          backgroundColor: null,
          logging: false,
          allowTaint: true
        }).then((canvas) => {
          function _dataURLtoFile(dataurl, filename) {
            var arr = dataurl.split(","),
              mime = arr[0].match(/:(.*?);/)[1],
              bstr = atob(arr[1]),
              n = bstr.length,
              u8arr = new Uint8Array(n);
            while (n--) {
              u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], filename, { type: mime });
          }
          var file = _dataURLtoFile(
            canvas.toDataURL("image/jpeg", 0.1),
            new Date().getTime() + ".jpg"
          );
          var array = []

          var reader = new FileReader();
          reader.readAsArrayBuffer(file);
          reader.onload = function () {
            //debugger
            var byts = new Uint8Array(this.result);
            array = Array.from(byts)
            
          };
          setTimeout(() => {
            UploadFile(array).then(res => {
              const data = res.data.data;
              const url = data.Content;
              resolve(url)
          })
          }, 1000);
        });
      })
    },
    handleAdd () {
      this.form = {
        name: '',
        type: '',
        content: ''
      };
      this.type = 'add'
      this.isEdit = true;
      this.handleOption()
      this.dialog = true;
    },
    handleEdit (row, index) {
      //debugger
      this.index = index;
      this.type = 'edit'
      this.isEdit = true;
       // this.form = row
        // this.form.type = Number(row.CTYPE) 
        // this.form.content = row.CCONTEXT
        // this.form.name = row.CNAME
        // this.form.CID= row.CID
        // this.handleOption()
        // this.dialog = true;
      getObj(row.CID).then(res => {
         const data = res.data.data.Datas;
          this.form.type = Number(data.CTYPE) 
          this.form.content = data.CCONTEXT
          this.form.name = data.CNAME
          this.form.CID= data.CID
        this.handleOption()
        this.dialog = true;
      })
    },
    handleSubmit (form, done) {
      if (this.type == 'add') {
        this.rowSave(this.from, done)
      } else if (this.type == 'edit') {
        this.rowUpdate(this.from, this.index, done)
      }
    },
    rowUpdate (row, index, done, loading) {
      let _self = this
      // if (this.vaildData(index) && this.$website.isDemo) {
      //   done();
      //   this.$message.error(this.$website.isDemoTip)
      //   return false;
      // }
      this.getImgByte().then(imgUrl => {
       let params ={
          "IS_UPDATE": true,
          "CID": _self.form.CID,
          "CNAME":_self.form.name,
          "CCONTEXT": _self.form.content,
          "CTYPE": _self.form.type,
          "CREMARK": "",
          "CIMG_URL": imgUrl
       }
        return updateObj(params)
      }).then(() => {
        done();
        this.dialog = false;
        this.$message.success(`${this.$t('message.ModifiedSuccessfully')}`);//'修改成功'
        this.getList()
      }).catch(err => {
        //loading()
      })
    },
    rowSave (row, done, loading) {
      let _self = this
      this.getImgByte().then(imgUrl => {
       // debugger
       // this.form.img = img;
       let params ={
          "IS_UPDATE": false,
          //"CID": 0,
          "CNAME":_self.form.name,
          "CCONTEXT": _self.form.content,
          "CTYPE": _self.form.type,
          "CREMARK": "",
          "CIMG_URL": imgUrl
       }
        return addObj(params)
      }).then(() => {
        done();
        this.dialog = false;
        this.$message.success(`${this.$t('message.NewlyAddedSuccessfully')}`);//'新增成功'
        this.getList()
      }).catch(err => {
        //loading()
      })
    },
    handleView (row, index) {
     // debugger
      getObj(row.CID).then(res => {
       // debugger
        this.type = 'edit'
        this.isEdit = true
        const data = res.data.data;
       let currentRow = data.Datas;
        this.form.type = Number(currentRow.CTYPE) 
        this.form.content = currentRow.CCONTEXT
        this.form.name = currentRow.CNAME
        this.form.CID= currentRow.CID
        this.vueOption = this.deepClone({
          content: currentRow.CCONTEXT//data.content
        })
        this.$refs.code.handleOpen()
      })
    },
    handleCurrentChange (val) {
      this.page.page = val;
      this.getList();
    },
    handleSizeChange (val) {
      this.page.size = val;
      this.getList();
    },
    getList () {
      this.loading = true
      this.data = []
      try {
        getList({
          condition: this.search.name,
          start: this.page.page,
          length: this.page.size,
          type: this.active
      }).then(res => {
        //debugger
          this.loading = false
          const data = res.data.data;
          this.page.total = data.TotalRows;
          let records = data.Datas
          records.forEach(ele => ele._menu = false);
          this.data = records;
      }).catch(err => {
        //debugger
        this.loading = false
      })
      } catch (error) {
        //debugger
        this.loading = false
      }
   
    }
  }
}
</script>
<style lang="scss">
@import "@/styles/baseFlex.scss";
</style>
<style lang="scss" scoped>

</style>