<template>
  <div class="modelConfig">
    <vxe-modal
      style="overflow-y: auto"
      v-model="showEdit"
      :title="title"
      width="600"
      @close="close"
      min-height="300"
      resize
      destroy-on-close
    >
      <template #default>
        <el-tabs
     
          v-model="activeName"
          @tab-click="handleClick"
          type="border-card"
        >
          <el-tab-pane
          :disabled="title=='数据源详情'"
            v-for="(item,sourceIndex) in second"
            :key="sourceIndex"
            :label="item.CNAME"
            :name="item.CCODE"
          >
            <el-form
              v-if="item.CCODE == 'DATABASE'"
              size="small"
              class="el_form"
              :rules="formRule"
              ref="form"
              :model="formData"
              label-width="100px"
            >
              <el-form-item label="数据源类型" prop="CDATASOURCE_TYPE">
                <el-select
                  class="main-select-tree"
                  ref="selectTree"
                  v-model="formData.CDATASOURCE_TYPE"
                  style="width: 100%"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.CID"
                    :label="item.CNAME"
                    :value="item.CID"
                    style="display: none"
                  />
                  <el-tree
                    class="main-select-el-tree"
                    :data="modalData"
                    node-key="CID"
                    highlight-current
                    :props="defaultProps"
                    @node-click="handleNodeClick"
                    :current-node-key="true"
                    default-expand-all
                  >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                      {{ node.label }}</span
                    >
                  </el-tree>
                </el-select>
              </el-form-item>
              <el-form-item label="数据源名称" prop="CNAME">
                <el-input
                  v-model="formData.CNAME"
                  placeholder="请输入数据源名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="主机名" prop="CHOST">
                <el-input
                  v-model="formData.CHOST"
                  placeholder="请输入主机名"
                ></el-input>
              </el-form-item>
              <el-form-item label="端口号" prop="CPORT">
                <el-input
                  v-model="formData.CPORT"
                  placeholder="请输入端口号"
                ></el-input>
              </el-form-item>
              <el-form-item label="数据库名称" prop="CDB_NAME">
                <el-input
                  v-model="formData.CDB_NAME"
                  placeholder="请输入数据库名称"
                ></el-input>
              </el-form-item>

              <el-form-item label="用户名" prop="CUSER_NAME">
                <el-input
                  v-model="formData.CUSER_NAME"
                  placeholder="请输入用户名"
                ></el-input>
              </el-form-item>
              <el-form-item label="密码" prop="CPASSWORD">
                <el-input
                show-password
                  v-model="formData.CPASSWORD"
                  placeholder="请输入密码"
                ></el-input>
              </el-form-item>
              <el-form-item label="使用SSL">
                <el-switch
                  v-model="formData.SSL"
                  :active-value="true"
                  :inactive-value="false"
                ></el-switch>
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  type="textarea"
                  v-model="formData.CDESC"
                  :autosize="{ minRows: 5 }"
                  placeholder="请输入内容"
                ></el-input>
              </el-form-item>

              <el-form-item >
                <el-button
                  :loading="loading"
                 
                  type="primary"
                  v-if="title!=='数据源详情'"
                  @click="testLink('form')"
                  >测试连接</el-button
                >
                <el-button @click="close()"  style="margin-left: 120px;">取消</el-button>
                <el-button type="primary"   v-if="title!=='数据源详情'" @click="submitEvent('form')"
                  >确定</el-button
                >
              </el-form-item>
            </el-form>

            <el-form
              v-if="item.CCODE == 'API'"
              size="small"
              class="el_form"
              :rules="formRules"
              ref="forms"
              :model="formDatas"
              label-width="100px"
            >
              <el-form-item label="数据源类型" prop="CDATASOURCE_TYPE">
                <el-select
                  class="main-select-tree"
                  ref="selecteltrees"
                  v-model="formDatas.CDATASOURCE_TYPE"
                  style="width: 100%"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.CID"
                    :label="item.CNAME"
                    :value="item.CID"
                    style="display: none"
                  />
                  <el-tree
                    class="main-select-el-tree"
                    :data="modalData"
                    node-key="CID"
                    highlight-current
                    :props="defaultProps"
                    @node-click="handleNodeClicks"
                    :current-node-key="true"
                    default-expand-all
                  >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                      {{ node.label }}</span
                    >
                  </el-tree>
                </el-select>
              </el-form-item>
              <el-form-item label="数据源名称" prop="CNAME">
                <el-input
                  v-model="formDatas.CNAME"
                  placeholder="请输入数据源名称"
                ></el-input>
              </el-form-item>

              <el-form-item label="URL" prop="CHOST">
                <el-input
                  v-model="formDatas.CHOST"
                  placeholder="请输入URL"
                ></el-input>
              </el-form-item>

              <el-form-item
                v-for="(domain, index) in formDatas.CHEADERS"
                :key="index"
                :label="index == 0 ? 'Headers' : ''"
                style="margin-bottom: 0px"
              >
                <el-input
                  style="width: 44.5%; margin-bottom: 8px"
                  v-model="domain.Key"
                  placeholder="key"
                ></el-input>
                &nbsp;&nbsp;
                <el-input
                  style="width: 44.5%"
                  v-model="domain.Value"
                  placeholder="value"
                ></el-input>
                &nbsp;
                <el-button
                  type="text"
                  :disabled="index == 0"
                  icon="el-icon-delete"
                  @click.prevent="removeDomains(index)"
                ></el-button>
              </el-form-item>

              <el-form-item style="margin-bottom: 0">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-plus"
                  @click="addDomains"
                  >添加</el-button
                >
              </el-form-item>

              <el-form-item
                v-for="(domain, index) in formDatas.CPARAMETERS"
                :key="index"
                :label="index == 0 ? 'Parameters' : ''"
                style="margin-bottom: 0px"
              >
                <el-input
                  style="width: 44.5%; margin-bottom: 8px"
                  v-model="domain.Key"
                  placeholder="Key"
                ></el-input>
                &nbsp;&nbsp;
                <el-input
                  style="width: 44.5%"
                  v-model="domain.Value"
                  placeholder="value"
                ></el-input>
                &nbsp;
                <el-button
                  type="text"
                  :disabled="index == 0"
                  icon="el-icon-delete"
                  @click.prevent="removeDomain(index)"
                ></el-button>
              </el-form-item>

              <el-form-item style="margin-bottom: 0">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-plus"
                  @click="addDomain"
                  >添加</el-button
                >
              </el-form-item>
              <el-form-item label="请求方式">
                <el-select
                  class="main-select-tree"
                  v-model="formDatas.CREQUEST"
                  style="width: 100%"
                  placeholder="请选择请求方式"
                >
                  <el-option
                    v-for="item in option"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="认证方式">
                <el-select
                  v-model="formDatas.CAUTH"
                  style="width: 100%"
                  placeholder="请选择请求方式"
                >
                  <el-option
                    v-for="item in option1"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="用户名"
                v-if="formDatas.CAUTH == 1 || formDatas.CAUTH == 2"
              >
                <el-input
                  v-model="formDatas.CUSER_NAME"
                  placeholder="请输入用户名"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="密码"
                v-if="formDatas.CAUTH == 1 || formDatas.CAUTH == 2"
              >
                <el-input
                show-password
                  v-model="formDatas.CPASSWORD"
                  placeholder="请输入密码"
                ></el-input>
              </el-form-item>
              <el-form-item style="text-align: right">
                <el-button @click="close()">取消</el-button>
                <el-button type="primary" v-if="title!=='数据源详情'" @click="submitEvents('forms')"
                  >确定</el-button
                >
              </el-form-item>
            </el-form>

       

            <el-upload
            v-if="item.CCODE == 'FILE'"
  class="upload-demo"
  drag
  :show-file-list="false"
  :httpRequest="httpRequest"
  action=""
  >
  <i class="el-icon-upload"></i>
  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>

</el-upload>





          </el-tab-pane>
        </el-tabs>







      </template>
    </vxe-modal>
  </div>
</template>

<script>
import {
  GetDataType,
  UploadFile,
  UpdateDatasource,
  ConnectionTest,
  AddDatasource,
} from "@/api/visual";
export default {
  name: "Modal",

  props: ["modelEvent", "title", "getList"],
  data() {
    return {
      activeName: "DATABASE",
      loading: false,
      showEdit: false,
      Blean:'',
      formData: {
        CDATASOURCE_TYPE: "",
        CNAME: "",
        CHOST: "",
        CPORT: "",
        SSL: false,
        CDB_NAME: "",
        CUSER_NAME: "",
        CPASSWORD: "",
        CDESC: "",
        CCONTENT: "",
      },
      formDatas: {
        CDATASOURCE_TYPE: "",
        CNAME: "",
        CHOST: "",
        CREQUEST: "",
        CCONTENT: "",
        CAUTH: "",
        CPARAMETERS: [
          {
            Key: "",
            Value: "",
          },
        ],
        CHEADERS: [
          {
            Key: "",
            Value: "",
          },
        ],
        CPASSWORD: "",
        CUSER_NAME: "",
      },
      formRule: {
        CDATASOURCE_TYPE: [
          { required: true, message: "请选择数据源类型", trigger: "change" },
        ],
        CNAME: [
          { required: true, message: "请输入数据源名称", trigger: "blur" },
        ],
        CHOST: [{ required: true, message: "请输入主机名", trigger: "blur" }],
        CPORT: [{ required: true, message: "请输入端口号", trigger: "blur" }],
        CDB_NAME: [
          { required: true, message: "请输入数据库名称", trigger: "blur" },
        ],
        CUSER_NAME: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        CPASSWORD: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      formRules: {
        CDATASOURCE_TYPE: [
          { required: true, message: "请选择数据源类型", trigger: "change" },
        ],
        CNAME: [
          { required: true, message: "请输入数据源名称", trigger: "blur" },
        ],
        CHOST: [{ required: true, message: "请输入URL", trigger: "blur" }],
      },
      modalData: [],
      defaultProps: {
        children: "children",
        label: "CNAME",
      },
      options: [],
      second: [],
      option: [
        {
          value: 1,
          name: "post",
        },
        {
          value: 0,
          name: "get",
        },
      ],
      option1: [
        {
          value: 0,
          name: "None",
        },
        {
          value: 1,
          name: "Basic",
        },
        {
          value: 2,
          name: "Digest",
        },
        {
          value: 3,
          name: "OAuth 2.0(Inherit from login)",
        },
      ],
      dataOption: [],
    };
  },
  mounted() {
    this.getdatasoures();
  },
  methods: {
    //根据code查询
    getCode(value) {
      var arr = this.dataOption.filter(
        (item) => item.CID == this.classification
      );
      return arr[0].CCODE == value ? true : false;
    },
    //根据节点过滤
    handleClick() {
      this.options = this.dataOption.filter(
        (item) => item.CPARENT_CODE == this.activeName
      );
      this.modalData = this.delDepartTree(this.options);
    },

    //过滤二级节点
    filterSecond(data) {
      this.second = data.filter((item) => item.CPARENT_ID == 0);
    //  console.log(this.second);
    },
    //新建
    addshowEdit(value) {
      this.Blean=value
      this.showEdit = true;
    },

    //编辑
    appendshowEdit(value,row) {
      this.Blean=value
      this.activeName = row.CDATASOURCE_TYPE_PARENT_CODE;
      this.handleClick()
      if (row.CDATASOURCE_TYPE_PARENT_CODE == "API") {
        this.formDatas.CNAME = row.CNAME;
        this.formDatas.CID=row.CID
        this.formDatas.CDATASOURCE_TYPE = row.CDATASOURCE_TYPE;
        this.formDatas.CCONTENT = row.CCONTENT;
        this.formDatas.CHOST = row.CHOST;
        this.formDatas.CREQUEST = row.CREQUEST;
        this.formDatas.CUSER_NAME = row.CUSER_NAME;
        this.formDatas.CPASSWORD = row.CPASSWORD;
        var CCONTENT = JSON.parse(row.CCONTENT);
        this.formDatas.CPARAMETERS = CCONTENT.CPARAMETERS;
        this.formDatas.CHEADERS = CCONTENT.CHEADERS;
        this.formDatas.CREQUEST = CCONTENT.CREQUEST;
        this.formDatas.CAUTH = CCONTENT.CAUTH;
      
        
      }
      if (row.CDATASOURCE_TYPE_PARENT_CODE == "DATABASE") {
        this.formData=row
        var CCONTENT = JSON.parse(row.CCONTENT);
        this.formData.CPORT = CCONTENT?.CPORT||'';
       
      }
      this.showEdit = true;
    },
    //详情
    detailsData(value,row){
      this.Blean=value
      this.activeName = row.CDATASOURCE_TYPE_PARENT_CODE;
      this.handleClick()
      if (row.CDATASOURCE_TYPE_PARENT_CODE == "API") {
        this.formDatas.CNAME = row.CNAME;
        this.formDatas.CID=row.CID
        this.formDatas.CDATASOURCE_TYPE = row.CDATASOURCE_TYPE;
        this.formDatas.CCONTENT = row.CCONTENT;
        this.formDatas.CHOST = row.CHOST;
        this.formDatas.CREQUEST = row.CREQUEST;
        this.formDatas.CUSER_NAME = row.CUSER_NAME;
        this.formDatas.CPASSWORD = row.CPASSWORD;
        var CCONTENT = JSON.parse(row.CCONTENT);
        this.formDatas.CPARAMETERS = CCONTENT.CPARAMETERS;
        this.formDatas.CHEADERS = CCONTENT.CHEADERS;
        this.formDatas.CREQUEST = CCONTENT.CREQUEST;
        this.formDatas.CAUTH = CCONTENT.CAUTH;
      
        
      }
      if (row.CDATASOURCE_TYPE_PARENT_CODE == "DATABASE") {
        this.formData=row
        var CCONTENT = JSON.parse(row.CCONTENT);
      //  console.log(CCONTENT);
        this.formData.CPORT = CCONTENT?.CPORT||'';
       
      }
      this.showEdit = true;
    },
    //获取数据源类型
    getdatasoures() {
      const params={
        type:0
      }
      GetDataType(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.dataOption = res.data.Datas;
          this.filterSecond(res.data.Datas);
          this.handleClick(this.activeName);
          // this.modalData = this.delDepartTree(res.data.Datas);
        }
      });
    },

    //树形结构转化
    delDepartTree(list) {
      // 1. 定义两个中间变量
      const treeList = [], // 最终要产出的树状数据的数组
        map = {}; // 存储映射关系

      // 2. 建立一个映射关系，并给每个元素补充children属性.
      // 映射关系: 目的是让我们能通过id快速找到对应的元素
      // 补充children：让后边的计算更方便
      list.forEach((item) => {
        item.children = [];
        map[item.CID] = item;
      });
      // 3. 循环
      list.forEach((item) => {
        // 对于每一个元素来说，先找它的上级
        //    如果能找到，说明它有上级，则要把它添加到上级的children中去
        //    如果找不到，说明它没有上级，直接添加到 treeList
        const parent = map[item.CPARENT_ID];
        if (parent) {
          parent.children.push(item);
        } else {
          treeList.push(item);
        }
      });
      // 4. 返回出去
      return treeList;
    },
  //上传
  httpRequest(item){
    //   console.log(item);

       var _this = this;
      var reader = new FileReader();
      reader.readAsArrayBuffer(item.file);
      reader.onload = function () {
        var byts = new Uint8Array(this.result);

        var array = Array.from(byts);
      ;
   var params=     {
	Bytes: array,
	FileName: item.file.name
}

UploadFile(params).then((res) => {
          res = res.data;
          if (res.code === 200 && res.data.Success) {

            this.$message({
                message: res.data.Content,
                type: "success",
              });
          }else{
           
            _this.$message({
                message: res.data.Content,
                type: "error",
              }); 
          }
        })
      }
  },
    //测试链接
    testLink(formName) {
      this.$refs[formName][0].validate((valid) => {
        if (valid) {
          this.loading = true;
          const params = {
            CIS_SSL: this.formData.SSL,
            //  CPORT:this.formData.CPORT
          };
          this.formData.CHOST = this.formData.CHOST + ":" + this.formData.CPORT;
          this.formData.CCONTENT = JSON.stringify(params);
          ConnectionTest(this.formData).then((res) => {
            res = res.data;
            if (res.code === 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.showEdit = false;
            } else {
              this.$message({
                message: res.data.Content,
                type: "error",
              });
            }
            this.loading = false;
          });
        } else {
        //  console.log("error submit!!");
          return false;
        }
      });
    },

    //获得焦点
    close() {
      (this.formData = {
        CDATASOURCE_TYPE: "",
        CNAME: "",
        CHOST: "",
        CPORT: "",
        SSL: false,
        CDB_NAME: "",
        CUSER_NAME: "",
        CPASSWORD: "",
        CDESC: "",
        CCONTENT: "",
      }),
        (this.formDatas = {
          CDATASOURCE_TYPE: "",
          CNAME: "",
          CHOST: "",
          CREQUEST: "",
          CCONTENT: "",
          CAUTH: "",
          CPARAMETERS: [
            {
              key: "",
              Value: "",
            },
          ],
          CHEADERS: [
            {
              key: "",
              Value: "",
            },
          ],
          CPASSWORD: "",
          CUSER_NAME: "",
        }),
        this.showEdit = false
    },

    handleNodeClick(node) {
      this.formData.CDATASOURCE_TYPE = node.CID;
    //  console.log(this.$refs);
      this.$refs.selectTree[0].blur();
    },
    handleNodeClicks(node) {
      this.formDatas.CDATASOURCE_TYPE = node.CID;
      this.$refs.selecteltrees[0].blur();
    },
    //添加
    addDomain() {
    //  console.log(this.formDatas);
      this.formDatas.CPARAMETERS.push({
        Value: "",
        Key: "",
      });
    },
    removeDomain(index) {
      this.formDatas.CPARAMETERS.splice(index, 1);
    },
    addDomains() {
      this.formDatas.CHEADERS.push({
        Value: "",
        Key: "",
      });
    },
    removeDomains(index) {
      this.formDatas.CHEADERS.splice(index, 1);
    },
    submitEvent(formName) {
      this.$refs[formName][0].validate((valid) => {
        if (valid) {
          const params = {
            CIS_SSL: this.formData.SSL,
            CPORT: this.formData.CPORT,
          };
          this.formData.CCONTENT = params;
          if(this.Blean==true){
            UpdateDatasource(this.formData).then((res) => {
            res = res.data;
            if (res.code === 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.close();
              this.getList();
            } else {
              this.$message({
                message: res.data.Content,
                type: "error",
              });
            }
          });
          }else{
             AddDatasource(this.formData).then((res) => {
            res = res.data;
            if (res.code === 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.close();
              this.getList();
            } else {
              this.$message({
                message: res.data.Content,
                type: "error",
              });
            }
          });
          }
         
        } else {
      //    console.log("error submit!!");
          return false;
        }
      });
    },
    submitEvents(formName) {
      this.$refs[formName][0].validate((valid) => {
        if (valid) {
          const params = {
            CREQUEST: this.formDatas.CREQUEST,
            CAUTH: this.formDatas.CAUTH,
            CHEADERS: this.formDatas.CHEADERS,
            CPARAMETERS: this.formDatas.CPARAMETERS,
          };
          this.formDatas.CCONTENT = params;
        if(this.Blean==true){
          UpdateDatasource(this.formDatas).then((res) => {
            res = res.data;
            if (res.code === 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.close();
              this.getList();
            } else {
              this.$message({
                message: res.data.Content,
                type: "error",
              });
            }
          });
        }else{
           AddDatasource(this.formDatas).then((res) => {
            res = res.data;
            if (res.code === 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.close();
              this.getList();
            } else {
              this.$message({
                message: res.data.Content,
                type: "error",
              });
            }
          });
        }

         
         

          
        } else {
       //   console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.modelConfig {
  .el-form-item__error {
    top: 67%;
  }

  .el_form {
    width: 500px;
    padding: 0 30px;
  }
}
</style>
