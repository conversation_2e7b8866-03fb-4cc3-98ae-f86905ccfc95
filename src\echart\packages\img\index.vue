<template>
  <div :class="[b(),className]"
       :style="styleSizeName"
       @click="handleClick">
    <!-- <span style="color: white;"> andy img:>>>>> {{ imgUrlFormat }}/////dataChart:{{ dataChart }}</span>
   -->
    <img v-if="imgUrlFormat" :style="[styleChartName,styleImgName]"
         :src="imgUrlFormat.value"
         :class="b({'rotate': rotate})"
         draggable="false" />
  </div>
</template>

<script>
import create from "../../create";
export default create({
  name: "img",
  computed: {
    imgUrlFormat () {
      let _optionData = (Array.isArray(this.dataChart)?this.dataChart[0]:this.dataChart)
      return _optionData
    },
    styleImgName () {
      return Object.assign(
        (() => {
          if (this.rotate) {
            return {
              animationDuration: this.duration / 1000 + "s"
            };
          }
          return {};
        })(),
        {
          width: '100%',
          height: '100%',
          borderRadius: this.setPx(this.option.borderRadius),
          opacity: this.option.opacity || 1
        }
      );
    },
    duration () {
      return this.option.duration || 3000;
    },
    rotate () {
      return this.option.rotate;
    }
  },
  methods: {
    handleClick () {
      this.clickFormatter && this.clickFormatter({
        data: this.dataChart
      }, this.getItemRefs());
    }
  }
});
</script>

