# 表格格式化函数源码修复总结

## 问题描述

您遇到的问题是格式化函数中设置的 `font_Color` 属性影响了整行的所有列，而不是仅影响当前列。

### 原始问题代码
```javascript
(name,data)=>{
    if(data["type1"].includes("1")){
        data.font_Color="red"  // 这里直接修改了原始数据
    } 
    if(data["type1"].includes("2")){ 
        data.font_Color="green" 
    } 
    return data["type1"] 
}
```

## 源码修复方案

我已经在源码层面进行了修复，主要修改了两个方法：

### 1. 修复 `getFormatter` 方法

**文件位置：** `src/echart/packages/table/index.vue` (第485-512行)

**修复内容：**
- 创建行数据副本，避免格式化函数污染原始数据
- 将列特定的颜色存储到 `_columnColors` 对象中
- 添加错误处理和类型检查

```javascript
getFormatter(item, row) {
  try {
    // 获取格式化函数
    const formatterFunc = getFunction(item.formatter)
    if (typeof formatterFunc !== 'function') {
      console.warn('格式化函数无效:', item.formatter)
      return row[item.prop] || ''
    }
    
    // 创建行数据的副本，避免格式化函数污染原始数据
    const rowCopy = { ...row }
    const result = formatterFunc(item, rowCopy)
    
    // 如果格式化函数设置了 font_Color，将其存储到列特定的属性中
    if (rowCopy.font_Color && rowCopy.font_Color !== row.font_Color) {
      // 使用列的 prop 作为键，存储列特定的颜色
      if (!row._columnColors) {
        this.$set(row, '_columnColors', {})
      }
      this.$set(row._columnColors, item.prop, rowCopy.font_Color)
    }
    
    return result
  } catch (error) {
    console.error('格式化函数执行出错:', error, { item, row })
    return row[item.prop] || ''
  }
}
```

### 2. 修复 `cellStyle` 方法

**文件位置：** `src/echart/packages/table/index.vue` (第578-604行)

**修复内容：**
- 优先使用列特定的颜色设置
- 保持向后兼容性
- 正确处理 Element UI 的 column 对象属性

```javascript
cellStyle({ row, column, rowIndex, columnIndex }) {
  let _defaultColor = row?.rowfont ? row?.rowfont : this.option.bodyColor
  
  // 检查是否有列特定的颜色设置
  // Element UI 的 column 对象中，属性名是 property
  const columnProp = column.property || column.prop
  if (row._columnColors && columnProp && row._columnColors[columnProp]) {
    _defaultColor = row._columnColors[columnProp]
  } else if (!!row["font_Color"]) {
    // 保持向后兼容性：如果没有列特定颜色，使用全局 font_Color
    // 但只在没有设置列特定颜色的情况下使用
    if (!row._columnColors || Object.keys(row._columnColors).length === 0) {
      _defaultColor = row["font_Color"]
    }
  }

  return {
    padding: 0,
    height: this.setPx(this.cellHeight),
    fontSize: this.setPx(this.option.bodyFontSize),
    color: _defaultColor,
    textAlign: column.type == 'index' ? 'center' : this.option.bodyTextAlign,
    backgroundColor: row?.rowbackground ? row?.rowbackground : rowIndex % 2 == 0 ? this.option.othColor : this.option.nthColor,
  }
}
```

## 修复原理

### 1. 数据隔离
- 格式化函数现在操作的是行数据的副本，不会污染原始数据
- 每列的颜色设置独立存储在 `_columnColors` 对象中

### 2. 列特定样式
- 使用列的 `prop` 作为键，为每列单独存储样式
- `cellStyle` 方法优先使用列特定的样式

### 3. 向后兼容
- 保持对现有 `font_Color` 属性的支持
- 只在没有列特定颜色时才使用全局 `font_Color`

## 使用方法

修复后，您的原始格式化函数代码可以直接使用，无需修改：

```javascript
// 您的原始代码现在可以正常工作
(name,data)=>{
    if(data["type1"].includes("1")){
        data.font_Color="red"
    } 
    if(data["type1"].includes("2")){ 
        data.font_Color="green" 
    } 
    return data["type1"] 
}
```

## 构建和测试

### 1. 重新构建项目
```bash
npm run build
```

### 2. 测试修复效果
打开 `demo/table-formatter-fix-example.html` 查看修复效果。

## 优势

1. **完全向后兼容** - 现有代码无需修改
2. **列级隔离** - 每列的样式设置互不影响
3. **错误处理** - 添加了完善的错误处理机制
4. **性能优化** - 避免了不必要的数据污染

## 注意事项

1. 修复后需要重新构建项目才能生效
2. 建议在生产环境使用前进行充分测试
3. 如果遇到问题，可以查看浏览器控制台的错误信息

这个修复确保了格式化函数的样式设置只影响当前列，完全解决了您遇到的问题。
