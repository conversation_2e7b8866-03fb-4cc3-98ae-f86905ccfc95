<!-- video配置 -->
<template>
  <div>
    <el-form-item label="地址" :label="`${$t('components.video.Address')}`">
      <avue-input type="textarea"
                  v-model="main.activeObj.data.value"></avue-input>
    </el-form-item>
    <el-form-item label="封面" :label="`${$t('components.video.Cover')}`">
      <img v-if="main.activeOption.poster"
           :src="main.activeOption.poster"
           alt=""
           width="100%" />
      <el-input v-model="main.activeOption.poster">
        <div @click="main.handleOpenImg('activeOption.poster')"
             slot="append">
          <i class="iconfont icon-img"></i>
        </div>
      </el-input>
    </el-form-item>
    <el-form-item label="自动播放" :label="`${$t('components.video.Autoplay')}`">
      <avue-switch v-model="main.activeOption.autoplay"></avue-switch>
    </el-form-item>
    <el-form-item label="控制台" :label="`${$t('components.video.Controls')}`">
      <avue-switch v-model="main.activeOption.controls"></avue-switch>
    </el-form-item>
    <el-form-item label="循环" :label="`${$t('components.video.Loop')}`">
      <avue-switch v-model="main.activeOption.loop"></avue-switch>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'video',
  inject: ["main"]
}
</script>

<style>
</style>