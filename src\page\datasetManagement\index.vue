<template>
  <div class="datasetManagement">
    <div class="datasetManagement_left">
      <p class="title">数据源</p>
      <el-tree
        :data="datasourList"
        node-key="CID"
        default-expand-all
        :props="defaultProps"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div
            style="overflow: hidden; text-overflow: ellipsis; max-width: 180px"
          >
            {{ node.label }}
          </div>
          <!-- <div v-if="data.children.length!==0">
            <el-button type="text" size="mini" @click.stop="() => append(data)">
              <i class="el-icon-plus"></i>
              添加
            </el-button>
          </div> -->
        </div>
      </el-tree>
    </div>
    <div class="datasetManagement_right">
      <div class="datasetManagement_right_table">
        <vxe-toolbar>
          <template #buttons>
            <el-button
              icon="el-icon-plus"
              type="primary"
              size="mini"
              @click="insertEvent"
              >新建</el-button
            >
            <!-- <el-button
            icon="el-icon-folder-add"
            type="primary"
            size="mini"
            @click="quickInsertEvent"
            >快速新建</el-button
          > -->
          </template>
          <template #tools>
            <!-- <span style="color: #333; font-size: 12px">项目名称</span>
          <el-input
            style="width: 180px; margin: 0 10px"
            size="mini"
            v-model="formData.ProjectName"
            placeholder="请输入项目名称"
          ></el-input> -->

            <span style="color: #333; font-size: 12px">数据源</span>
            <el-input
              style="width: 180px; margin: 0 10px"
              size="mini"
              v-model="formData.ProjectName"
              @keyup.enter.native="onSubmit"
              placeholder="请输入数据源"
            ></el-input>
            <el-button
              title="查询"
              size="mini"
              type="primary"
              icon="el-icon-search"
              @click="onSubmit"
              >{{
            }}</el-button>
            <el-button
              title="重置"
              size="mini"
              type="primary"
              icon="el-icon-refresh-right"
              @click="reset"
              >{{
            }}</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="xTable"
          border
          align="center"
          :loading="loading"
          show-overflow
          keep-source
          :data="tableData"
          highlight-current-row
          @current-change="handleCurrentChange"
        >
          <vxe-column type="seq" width="60" title="序号"></vxe-column>
          <vxe-column field="CNAME" title="名称"> </vxe-column>
          <vxe-column field="CDATASET_LABEL_NAME" title="标签名称"></vxe-column>
          <vxe-column title="发布状态">
            <template #default="{ row }">
              <span v-if="row.CPUBLISH_STATUS == 10" style="color: #008000"
                >已发布</span
              >
              <span v-if="row.CPUBLISH_STATUS == 0" style="color: #ff0000"
                >未发布</span
              >
            </template>
          </vxe-column>
          <vxe-column title="状态">
            <template #default="{ row }">
              <span v-if="row.CHEALTH_STATUS == 10" style="color: #ff0000"
                >异常</span
              >
              <span v-if="row.CHEALTH_STATUS == 0" style="color: #008000"
                >正常</span
              >
            </template>
          </vxe-column>
          <!-- <vxe-column field="CWORK_DATE" title="收藏"></vxe-column> -->

          <vxe-column title="操作" width="250" fixed="right">
            <template #default="{ row }">
              <!-- <el-button
           type="text"
              @click="$refs.xTable.revertData(row)"
        
              >详情</el-button
            > -->
              <el-button type="text" @click="append">编辑</el-button>
              <el-button style="color:#f56c6c" type="text" @click="deleteData"
                >删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <vxe-pager
          align="right"
          background
          size="small"
          @page-change="pageChange"
          :current-page.sync="page2.currentPage"
          :page-size.sync="page2.pageSize"
          :total="page2.totalResult"
          :layouts="[
            'PrevJump',
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'NextJump',
            'Sizes',
            'FullJump',
            'Total',
          ]"
        >
        </vxe-pager>
      </div>
      <DatasetConfiguration ref="Configuration" />
      <ModelConfig @func="shows" ref="modelEvent" :title="title" />
    </div>
  </div>
</template>
<script>
import { GetDataType, GetPagelist } from "@/api/visual";
import { mapGetters } from "vuex";
import ModelConfig from "./modelConfig.vue";
import DatasetConfiguration from "./datasetConfiguration.vue";
export default {
  name: "datasetManagement",
  components: {
    DatasetConfiguration,
    ModelConfig,
  },
  data() {
    return {
      title: "",
      formData: {
        ProjectName: "",
        WorkType: "",
      },
      CWORK_DATES: "",
      loading: true,
      dataTime: 0,
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
      projectoption: [],
      typeoptions: [
        { value: 0, name: "正常上班" },
        { value: 1, name: "工作日加班" },
        { value: 2, name: "周末加班" },
        { value: 3, name: "节假日加班" },
      ],
      typeId: 0,
      useroptions: [],
      tableData: [],
      datasourList: [],
      defaultProps: {
        children: "children",
        label: "CNAME",
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.getdatasoures();
    this.getdatasourList();
  },

  methods: {
    shows() {
      this.getdatasourList();
    },
    //行选中
    handleCurrentChange({ row }) {
      this.$refs.Configuration.currentRow(row);
    },
    //请求数据源接口
    getdatasoures() {
      const params = {
        type: 1,
      };
      GetDataType(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.datasourList = this.delDepartTree(res.data.Datas);
        }
      });
    },
    //请求数据集列表
    getdatasourList(value) {
      const params = {
        start: this.page2.currentPage,
        length: this.page2.pageSize,
        condition: value ? value : "",
        typeId: this.typeId,
      };
      GetPagelist(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.tableData = res.data.Datas;
          this.page2.totalResult = res.data.TotalRows;
        } else {
          this.$message({
            message: res.message.msg,
            type: "error",
          });
        }
        this.loading = false;
      });
    },
    //树形结构转化
    delDepartTree(list) {
      // 1. 定义两个中间变量
      const treeList = [], // 最终要产出的树状数据的数组
        map = {}; // 存储映射关系

      // 2. 建立一个映射关系，并给每个元素补充children属性.
      // 映射关系: 目的是让我们能通过id快速找到对应的元素
      // 补充children：让后边的计算更方便
      list.forEach((item) => {
        item.children = [];
        map[item.CID] = item;
      });
      // 3. 循环
      list.forEach((item) => {
        // 对于每一个元素来说，先找它的上级
        //    如果能找到，说明它有上级，则要把它添加到上级的children中去
        //    如果找不到，说明它没有上级，直接添加到 treeList
        const parent = map[item.CPARENT_ID];
        if (parent) {
          parent.children.push(item);
        } else {
          treeList.push(item);
        }
      });
      // 4. 返回出去
      return treeList;
    },
    //新建数据集
    insertEvent() {
      this.title = "创建数据集";
      this.$refs.modelEvent.addshowEdit();
    },

    append(data) {
      this.title = "编辑数据源";
      this.$refs.modelEvent.appendshowEdit();
      console.log(data, "编辑数据源");
    },
    selectchange() {
      this.onSubmit();
    },
    onSubmit() {
      // this.getUserProject();
    },
    reset() {
      this.formData.ProjectName = "";
      this.formData.WorkType = "";
      // this.getUserProject();
    },

    pageChange(value) {
      this.loading = true;
    },
  },
};
</script>
<style lang="scss">
.datasetManagement {
  display: flex;

  margin: 10px;
  .datasetManagement_left {
    height: calc(100vh - 126px);
    overflow-y: auto;
    .title {
      border-bottom: 1px solid #f1f1f1;
      font-size: 16px;
      height: 48px;
      line-height: 48px;
      padding-left: 20px;
      margin-bottom: 15px;
    }
    background: #fff;
    width: 260px;
    margin-right: 10px;
    .custom-tree-node {
      display: flex;
      justify-content: space-between;
      line-height: 26px;
      width: 100%;
      padding-right: 8px;
      color: #666666;
    }
  }
  .datasetManagement_right {
    flex: 1;
    overflow-y: auto;
    .vxe-table--body-wrapper {
      height: calc(100vh - 474px);
    }
    .datasetManagement_right_table {
      padding: 5px 10px 0px 10px;
      background: #fff;
    }
  }
}
.vxe-modal--content {
  padding-left: 5px !important;
}
</style>
