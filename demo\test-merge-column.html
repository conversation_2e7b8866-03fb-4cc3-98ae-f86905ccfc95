<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合并列功能测试</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
        }
        .control-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .table-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .test-info {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #409eff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔗 表格合并列功能测试</h1>
        
        <div class="controls">
            <div class="control-item">
                <label>
                    <input type="checkbox" id="enableMergeColumn" checked> 启用合并列功能
                </label>
            </div>
            <div class="control-item">
                <label>
                    <input type="checkbox" id="mergeWorkstation" checked> 合并工序列
                </label>
            </div>
            <div class="control-item">
                <label>
                    <input type="checkbox" id="mergeStation" checked> 合并工站列
                </label>
            </div>
            <div class="control-item">
                <label>
                    <input type="checkbox" id="showBorder" checked> 显示边框
                </label>
            </div>
            <div class="control-item">
                <button onclick="updateConfig()">应用配置</button>
            </div>
            <div class="control-item">
                <button onclick="generateData()">生成新数据</button>
            </div>
        </div>

        <div class="table-container" id="tableContainer">
            <!-- 表格将在这里渲染 -->
        </div>

        <div class="test-info">
            <h3>测试说明：</h3>
            <ul>
                <li>✅ 启用合并列功能后，相同值的相邻单元格会自动合并</li>
                <li>✅ 可以为每一列单独配置是否启用合并</li>
                <li>✅ 数据已按工序和工站排序，确保相同值相邻</li>
                <li>✅ 空值或undefined不参与合并</li>
                <li>✅ 合并功能与边框、滚动等其他功能兼容</li>
            </ul>
            <p><strong>当前状态：</strong> <span id="statusInfo">正在初始化...</span></p>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 测试数据
        let testData = [
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061001", operator: "费学进", time: "08:30" },
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061002", operator: "张三", time: "08:35" },
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061003", operator: "李四", time: "08:40" },
            { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062001", operator: "王五", time: "09:00" },
            { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062002", operator: "赵六", time: "09:05" },
            { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063001", operator: "孙七", time: "09:30" },
            { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063002", operator: "周八", time: "09:35" },
            { workstation: "外层线路", station: "前处理4线", serialNumber: "J25064001", operator: "吴九", time: "10:00" },
            { workstation: "内层显影", station: "显影1线", serialNumber: "J25071001", operator: "郑十", time: "10:30" },
            { workstation: "内层显影", station: "显影1线", serialNumber: "J25071002", operator: "钱一", time: "10:35" }
        ];

        // 表格配置
        let tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 10,
            border: true,
            enableMergeColumn: true,
            headerBackground: "#409eff",
            headerColor: "#ffffff",
            headerFontSize: 14,
            headerTextAlign: "center",
            bodyColor: "#333333",
            bodyFontSize: 12,
            bodyTextAlign: "center",
            nthColor: "#f9f9f9",
            othColor: "#ffffff",
            column: [
                { 
                    label: "工序", 
                    prop: "workstation", 
                    width: 120,
                    mergeColumn: true
                },
                { 
                    label: "工站", 
                    prop: "station", 
                    width: 120,
                    mergeColumn: true
                },
                { 
                    label: "流程卡号", 
                    prop: "serialNumber", 
                    width: 150,
                    mergeColumn: false
                },
                { 
                    label: "操作员", 
                    prop: "operator", 
                    width: 100,
                    mergeColumn: false
                },
                { 
                    label: "时间", 
                    prop: "time", 
                    width: 80,
                    mergeColumn: false
                }
            ]
        };

        // Vue实例 - 延迟初始化
        let app = null;

        // 初始化Vue应用
        function initApp() {
            if (typeof Vue === 'undefined') {
                console.error('Vue is not loaded');
                return;
            }

            app = new Vue({
                el: '#tableContainer',
                data: {
                    option: Object.assign({}, tableConfig),
                    data: [...testData]
                },
                template: `
                    <div style="height: 100%; padding: 10px;">
                        <el-table
                            :data="data"
                            :border="option.border"
                            :show-header="option.showHeader"
                            :span-method="option.enableMergeColumn ? spanMethod : null"
                            style="width: 100%; height: 100%;">
                            <el-table-column
                                type="index"
                                label="#"
                                width="60"
                                v-if="option.index">
                            </el-table-column>
                            <el-table-column
                                v-for="col in option.column"
                                :key="col.prop"
                                :prop="col.prop"
                                :label="col.label"
                                :width="col.width">
                            </el-table-column>
                        </el-table>
                    </div>
                `,
                methods: {
                    spanMethod({ row, column, rowIndex, columnIndex }) {
                        if (!this.option.enableMergeColumn) {
                            return [1, 1];
                        }

                        // 获取当前列的配置
                        const currentColumn = this.option.column.find(col => col.prop === column.property);
                        if (!currentColumn || !currentColumn.mergeColumn) {
                            return [1, 1];
                        }

                        // 计算合并的行数
                        const prop = column.property;
                        const currentValue = row[prop];

                        // 如果当前值为空或undefined，不合并
                        if (currentValue === null || currentValue === undefined || currentValue === '') {
                            return [1, 1];
                        }

                        // 查找连续相同值的行数
                        let rowspan = 1;

                        // 向下查找相同值
                        for (let i = rowIndex + 1; i < this.data.length; i++) {
                            if (this.data[i][prop] === currentValue) {
                                rowspan++;
                            } else {
                                break;
                            }
                        }

                        // 向上查找，如果上一行有相同值，则当前行不显示
                        for (let i = rowIndex - 1; i >= 0; i--) {
                            if (this.data[i][prop] === currentValue) {
                                return [0, 0]; // 不显示当前单元格
                            } else {
                                break;
                            }
                        }

                        return [rowspan, 1];
                    }
                },
                mounted() {
                    updateStatus();
                }
            });
        }

        // 更新配置
        function updateConfig() {
            if (!app) {
                console.error('App not initialized');
                return;
            }

            tableConfig.enableMergeColumn = document.getElementById('enableMergeColumn').checked;
            tableConfig.border = document.getElementById('showBorder').checked;
            tableConfig.column[0].mergeColumn = document.getElementById('mergeWorkstation').checked;
            tableConfig.column[1].mergeColumn = document.getElementById('mergeStation').checked;

            app.option = Object.assign({}, tableConfig);
            updateStatus();
        }

        // 生成新数据
        function generateData() {
            if (!app) {
                console.error('App not initialized');
                return;
            }

            const workstations = ['免片线路', '外层线路', '内层显影', '钻孔工序'];
            const stations = ['前处理1线', '前处理2线', '前处理3线', '前处理4线', '显影1线', '显影2线', '钻孔1线'];
            const operators = ['费学进', '张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十', '钱一'];

            testData = [];
            let serialNum = 1;

            workstations.forEach(workstation => {
                const stationCount = Math.floor(Math.random() * 3) + 2;
                for (let i = 0; i < stationCount; i++) {
                    const station = stations[Math.floor(Math.random() * stations.length)];
                    const recordCount = Math.floor(Math.random() * 3) + 2;

                    for (let j = 0; j < recordCount; j++) {
                        testData.push({
                            workstation: workstation,
                            station: station,
                            serialNumber: `J2506${String(serialNum).padStart(4, '0')}`,
                            operator: operators[Math.floor(Math.random() * operators.length)],
                            time: `${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`
                        });
                        serialNum++;
                    }
                }
            });

            // 按工序和工站排序
            testData.sort((a, b) => {
                if (a.workstation !== b.workstation) {
                    return a.workstation.localeCompare(b.workstation);
                }
                return a.station.localeCompare(b.station);
            });

            app.data = [...testData];
            updateStatus();
        }

        // 更新状态信息
        function updateStatus() {
            const status = document.getElementById('statusInfo');
            const mergeEnabled = tableConfig.enableMergeColumn;
            const workstationMerge = tableConfig.column[0].mergeColumn;
            const stationMerge = tableConfig.column[1].mergeColumn;
            
            status.innerHTML = `
                合并列功能: ${mergeEnabled ? '启用' : '禁用'} | 
                工序列合并: ${workstationMerge ? '启用' : '禁用'} | 
                工站列合并: ${stationMerge ? '启用' : '禁用'} | 
                数据条数: ${testData.length}
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('合并列功能测试页面已加载');
            console.log('表格配置:', tableConfig);
            console.log('测试数据:', testData);

            // 等待所有脚本加载完成后初始化
            setTimeout(() => {
                initApp();
            }, 100);
        });
    </script>
</body>
</html>
