<template>
  <el-dialog
    title="图库"
    width="80%"
    :close-on-click-modal="false"
    :visible.sync="imgVisible"
  >
    <div style="margin: 0 auto">
      <el-upload
        class="upload-demo"
        :show-file-list="false"
        action=""
        :httpRequest="httpRequest"
        list-type="picture"
      >
        <el-button size="small" icon="el-icon-upload" type="primary"
          >点击上传</el-button
        >
      </el-upload>
    </div>
    <el-scrollbar class="imgList">
      <!-- item.value.indexOf('http') > -1 ? item.value : urls + item.value -->
      <img
        :title="item.title?item.title:'默认图片'"
        :src="getIMGUrl(item)"
        :style="styleName"
        @click="handleSetimg(item.value)"
        v-for="(item, index) in imgOption[imgActive]"
        :key="index"
      />
    </el-scrollbar>
  </el-dialog>
</template>
<!-- //:src="item.value.indexOf('http')>-1?item.value:urls+item.value" -->
<script>
import { url as serverUrl } from "@/config";
// import {getUrlParams,addParam  } from "@/echart/util.js";
import { uploadImg } from "@/api/visual";
import { imgOption } from "@/option/config";
import {

GetPageListByQueryJson,


} from "./Api.js";
export default {
  data() {
    return {
      relativePath:"",
      serverUrl:serverUrl, //serverUrl+relativePath
      urls: "",
      imgVisible: false,
      imgObj: "",
      type: "",
      imgActive: 0,
      imgOption: imgOption,
      imgTabs: [],
      tableData:[],
    };
  },
  mounted() {
    // if (process.env.NODE_ENV === "development") {
    //   this.urls = window.location.origin;
    // }else{
    //   this.urls = window.location.origin + "/subapp/bulletin"
    // }
    this.urls = window.location.origin + "/subapp/bulletin"
  
  },
  computed: {
    originPath(){
      return window.location.origin
    },
    styleName() {
      if (this.type === "background") {
        return {
          width: "200px",
        };
      }
      return {};
    },
  },
  watch: {
    type: {
      handler() {
  
        if (this.type === "background") {
          this.imgActive = 0;
        } else if (this.type == "border") {
          this.imgActive = 1;
        } else {
          this.imgActive = 2;
        }
      },
      immediate: true,
    },
  },
  methods: {
    getIMGUrl(item){
      let _originPath =window.location.origin
      let _pathUrl = item.value.indexOf('http') > -1 ? item.value.replace(_originPath, '') :  item.value.replace(_originPath, '')
      if(_pathUrl.indexOf('/img/bg/')>-1 && item.value.indexOf('http') == -1){
        _pathUrl=  'subapp/bulletin'+_pathUrl//兼容旧版路径
      }else{
        _pathUrl=  _pathUrl.replace("/",'')
      }
    
      return this.serverUrl+_pathUrl
    },
    getdatasoures() {
      const text = {
        condition: '',
        start: 1,
        length: 10000
      };


      GetPageListByQueryJson(text).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.tableData = res.data.Datas;
          this.tableData.forEach(element => {
            var arr=this.imgOption[this.imgActive].filter(item=>item.label==element.CIMAGE_NAME)
            if(arr.length>=1){

            }else{
               this.imgOption[this.imgActive].unshift({
              label: element.CIMAGE_NAME,
              value: window.location.origin+element.CIMAGE_PATH,
              title: element.CIMAGE_NAME,
            });
            }
           
            console.log( this.imgOption);
          });
        
        } else {
          this.$message({
            message: res.message.msg || res.data.Content,
            type: "error",
          });
        }
        this.loading = false
      });
    },
    httpRequest(item) {
      var _this = this;
      var reader = new FileReader();
      reader.readAsArrayBuffer(item.file);
      reader.onload = function () {
    
        var byts = new Uint8Array(this.result);
     
        var array = Array.from(byts);
        uploadImg(array).then((res) => {
          res = res.data;
          if (res.code === 200 && res.data.Success) {
            var url = res.data.Content;

            _this.imgOption[_this.imgActive].unshift({
              label: window.location.origin+url,
              value: window.location.origin+url,
            });
          }
        });
      };
    },
    onSuccess(res) {
      // const url = res.data.link;
      // this.imgOption[this.imgActive].unshift({
      //   label: url,
      //   value: url
      // });
    },
    openImg(item, type) {
      this.type = type;
      this.imgObj = item;
      this.imgVisible = true;
      this.getdatasoures()
    },
    handleSetimg(item) {
      let urlHost = window.location.origin
      // 保存相对路径，非绝对路径 /subapp/bulletin/img/bg/bg18.jpg
      this.imgVisible = false;
      debugger
      let fullPath = item.indexOf("http") > -1 ? item : this.urls + item;
      this.relativePath = fullPath.replace(urlHost, "");
      this.$emit("change", this.relativePath, this.imgObj);
    },
  },
};
</script>

<style></style>
