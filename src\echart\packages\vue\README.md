# Vue组件渲染器 (vue) - 使用指南

## 📋 概述

Vue组件渲染器是CI.Web.Plugins.Bulletin系统中的创新功能，允许开发者通过配置动态加载和渲染Vue单文件组件。它支持运行时编译、样式隔离、数据传递等高级特性，为自定义业务组件提供了强大的扩展能力。

## 🎯 核心特性

- **🔄 动态编译**: 运行时解析和编译Vue单文件组件
- **🎨 样式隔离**: 自动处理组件样式，避免样式冲突
- **📡 数据传递**: 支持多种数据源类型的数据传递
- **🔗 事件通信**: 支持组件间事件通信和数据联动
- **♻️ 资源管理**: 自动清理样式和组件资源

## 🏗️ 工作原理

### 组件解析流程

```javascript
// 1. 解析Vue单文件组件
getSource(type) {
  const reg = new RegExp(`<${type}[^>]*>`);
  let content = this.content;
  let matches = content.match(reg);
  if (matches) {
    let start = content.indexOf(matches[0]) + matches[0].length;
    let end = content.lastIndexOf(`</${type}`);
    return content.slice(start, end);
  }
}

// 2. 动态注册组件
initVue() {
  let template = this.getSource("template");
  let script = this.getSource("script");
  let styleCss = this.getSource("style");

  // 处理脚本部分
  if (script) {
    script = script.replace(/export default/, "return");
  }

  // 创建样式
  let style = document.createElement("style");
  style.id = 'style-' + this.id;
  style.innerHTML = styleCss;
  document.head.appendChild(style);

  // 编译并注册组件
  let obj = new Function(script)();
  obj.template = template;
  obj.props = { dataChart: Object };

  Vue.component(this.id, obj);
}
```

## 📊 数据类型支持

Vue组件渲染器支持以下4种主要数据类型：

| 数据类型 | dataType值 | 描述 | 适用场景 |
|----------|------------|------|----------|
| **静态数据** | 0 | 直接在配置中定义 | 演示、测试、固定展示 |
| **API接口** | 1 | HTTP请求获取 | 常规业务数据、第三方接口 |
| **自定义数据接口** | 5 | 预定义数据集 | 标准化数据源、复杂查询 |
| **全局数据源** | 6 | 组件间共享数据 | 数据联动、缓存优化 |

## 🔧 基础配置

### 组件基本结构

```javascript
const vueComponentConfig = {
  // 组件尺寸
  width: 600,
  height: 400,

  // 数据源配置
  dataType: 0,  // 数据类型
  data: [],     // 静态数据

  // 组件内容
  option: {
    content: `
      <template>
        <!-- Vue模板 -->
      </template>

      <script>
      export default {
        // Vue组件选项
      }
      </script>

      <style>
      /* 组件样式 */
      </style>
    `
  },

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    // 数据处理逻辑
    return data;
  }`,

  // 点击事件
  clickFormatter: `(params, refs) => {
    // 事件处理逻辑
  }`
}
```

## 📝 使用案例详解

### 1. 静态数据案例

#### 案例1.1: 简单数据展示组件

```javascript
const staticDataExample = {
  width: 500,
  height: 300,
  dataType: 0,  // 静态数据
  data: [
    { id: 1, name: "张三", department: "技术部", score: 95 },
    { id: 2, name: "李四", department: "销售部", score: 88 },
    { id: 3, name: "王五", department: "市场部", score: 92 },
    { id: 4, name: "赵六", department: "技术部", score: 87 }
  ],

  option: {
    content: `
      <template>
        <div class="employee-card-container">
          <h3 class="title">员工信息卡片</h3>
          <div class="card-grid">
            <div
              v-for="employee in dataChart"
              :key="employee.id"
              class="employee-card"
              :class="getScoreClass(employee.score)"
              @click="handleCardClick(employee)"
            >
              <div class="card-header">
                <span class="name">{{ employee.name }}</span>
                <span class="score">{{ employee.score }}分</span>
              </div>
              <div class="card-body">
                <p class="department">{{ employee.department }}</p>
                <div class="score-bar">
                  <div
                    class="score-fill"
                    :style="{ width: employee.score + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        methods: {
          getScoreClass(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            return 'normal';
          },
          handleCardClick(employee) {
            this.$emit('card-click', employee);
            console.log('点击了员工:', employee.name);
          }
        }
      }
      </script>

      <style>
      .employee-card-container {
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        color: white;
      }
      .title {
        text-align: center;
        margin-bottom: 20px;
        color: #fff;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      .card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }
      .employee-card {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
      }
      .employee-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
      }
      .employee-card.excellent {
        border-left: 4px solid #67C23A;
      }
      .employee-card.good {
        border-left: 4px solid #E6A23C;
      }
      .employee-card.normal {
        border-left: 4px solid #F56C6C;
      }
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }
      .name {
        font-weight: bold;
        font-size: 16px;
      }
      .score {
        background: rgba(255,255,255,0.2);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
      }
      .department {
        margin: 5px 0;
        opacity: 0.8;
      }
      .score-bar {
        height: 6px;
        background: rgba(255,255,255,0.2);
        border-radius: 3px;
        overflow: hidden;
      }
      .score-fill {
        height: 100%;
        background: linear-gradient(90deg, #67C23A, #85ce61);
        transition: width 0.5s ease;
      }
      </style>
    `
  },

  // 数据格式化（可选）
  dataFormatter: `(data, params, refs) => {
    // 对静态数据进行额外处理
    return data.map(item => ({
      ...item,
      displayName: item.name + ' (' + item.department + ')',
      level: item.score >= 90 ? '优秀' : item.score >= 80 ? '良好' : '一般'
    }));
  }`,

  // 点击事件处理
  clickFormatter: `(params, refs) => {
    console.log('Vue组件事件:', params);
    // 可以触发其他组件的更新
    if (refs.relatedChart) {
      refs.relatedChart.updateData({
        filter: params.data.department
      });
    }
  }`
};
```

#### 案例1.2: 统计图表组件

```javascript
const staticChartExample = {
  width: 600,
  height: 400,
  dataType: 0,
  data: [
    { category: "技术部", count: 25, budget: 500000 },
    { category: "销售部", count: 18, budget: 300000 },
    { category: "市场部", count: 12, budget: 200000 },
    { category: "人事部", count: 8, budget: 150000 },
    { category: "财务部", count: 6, budget: 120000 }
  ],

  option: {
    content: `
      <template>
        <div class="chart-container">
          <div class="chart-header">
            <h3>部门统计概览</h3>
            <div class="summary">
              <span>总人数: {{ totalCount }}</span>
              <span>总预算: ¥{{ formatMoney(totalBudget) }}</span>
            </div>
          </div>

          <div class="chart-content">
            <div class="chart-bars">
              <div
                v-for="(item, index) in dataChart"
                :key="index"
                class="bar-item"
                @click="selectCategory(item)"
                :class="{ active: selectedCategory === item.category }"
              >
                <div class="bar-info">
                  <span class="category">{{ item.category }}</span>
                  <span class="count">{{ item.count }}人</span>
                </div>
                <div class="bar-visual">
                  <div
                    class="bar-fill"
                    :style="{
                      width: (item.count / maxCount * 100) + '%',
                      background: getBarColor(index)
                    }"
                  ></div>
                </div>
                <div class="budget">¥{{ formatMoney(item.budget) }}</div>
              </div>
            </div>

            <div v-if="selectedCategory" class="detail-panel">
              <h4>{{ selectedCategory }} 详细信息</h4>
              <div class="detail-stats">
                <div class="stat-item">
                  <label>人员数量:</label>
                  <span>{{ selectedData.count }}人</span>
                </div>
                <div class="stat-item">
                  <label>预算金额:</label>
                  <span>¥{{ formatMoney(selectedData.budget) }}</span>
                </div>
                <div class="stat-item">
                  <label>人均预算:</label>
                  <span>¥{{ formatMoney(selectedData.budget / selectedData.count) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        data() {
          return {
            selectedCategory: null,
            colors: ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C', '#909399']
          }
        },
        computed: {
          totalCount() {
            return this.dataChart.reduce((sum, item) => sum + item.count, 0);
          },
          totalBudget() {
            return this.dataChart.reduce((sum, item) => sum + item.budget, 0);
          },
          maxCount() {
            return Math.max(...this.dataChart.map(item => item.count));
          },
          selectedData() {
            return this.dataChart.find(item => item.category === this.selectedCategory);
          }
        },
        methods: {
          formatMoney(amount) {
            return (amount / 10000).toFixed(1) + '万';
          },
          getBarColor(index) {
            return this.colors[index % this.colors.length];
          },
          selectCategory(item) {
            this.selectedCategory = item.category;
            this.$emit('category-select', item);
          }
        }
      }
      </script>

      <style>
      .chart-container {
        padding: 20px;
        background: #f8fafc;
        border-radius: 12px;
        font-family: 'Microsoft YaHei', sans-serif;
      }
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e2e8f0;
      }
      .chart-header h3 {
        margin: 0;
        color: #2d3748;
      }
      .summary {
        display: flex;
        gap: 20px;
        font-size: 14px;
        color: #4a5568;
      }
      .chart-content {
        display: flex;
        gap: 20px;
      }
      .chart-bars {
        flex: 1;
      }
      .bar-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 10px;
        background: white;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .bar-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }
      .bar-item.active {
        border: 2px solid #409EFF;
        background: #f0f9ff;
      }
      .bar-info {
        width: 80px;
        text-align: right;
        margin-right: 15px;
      }
      .category {
        display: block;
        font-weight: bold;
        color: #2d3748;
      }
      .count {
        font-size: 12px;
        color: #718096;
      }
      .bar-visual {
        flex: 1;
        height: 20px;
        background: #e2e8f0;
        border-radius: 10px;
        overflow: hidden;
        margin-right: 15px;
      }
      .bar-fill {
        height: 100%;
        transition: width 0.5s ease;
        border-radius: 10px;
      }
      .budget {
        width: 80px;
        text-align: center;
        font-size: 12px;
        color: #4a5568;
      }
      .detail-panel {
        width: 200px;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
      .detail-panel h4 {
        margin: 0 0 15px 0;
        color: #2d3748;
      }
      .stat-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 14px;
      }
      .stat-item label {
        color: #718096;
      }
      .stat-item span {
        font-weight: bold;
        color: #2d3748;
      }
      </style>
    `
  }
};
```

### 2. API接口数据案例

#### 案例2.1: 实时数据监控组件

```javascript
const apiDataExample = {
  width: 800,
  height: 500,
  dataType: 1,  // API接口数据
  dataMethod: "get",
  url: "https://api.example.com/monitor/realtime",
  time: 5000,  // 5秒刷新一次

  // 请求参数配置
  dataQuery: `(url) => ({
    page: 1,
    size: 20,
    type: 'server_status',
    timestamp: Date.now()
  })`,

  // 请求头配置
  dataHeader: `(url) => ({
    'Authorization': 'Bearer ' + localStorage.getItem('token'),
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  })`,

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    // API返回格式: { code: 200, data: { servers: [...] } }
    if (data.code === 200 && data.data) {
      return data.data.servers.map(server => ({
        id: server.id,
        name: server.name,
        status: server.status,
        cpu: server.metrics.cpu,
        memory: server.metrics.memory,
        disk: server.metrics.disk,
        network: server.metrics.network,
        lastUpdate: new Date(server.lastUpdate).toLocaleTimeString()
      }));
    }
    return [];
  }`,

  option: {
    content: `
      <template>
        <div class="monitor-dashboard">
          <div class="dashboard-header">
            <h2>服务器实时监控</h2>
            <div class="status-summary">
              <div class="summary-item online">
                <span class="count">{{ onlineCount }}</span>
                <span class="label">在线</span>
              </div>
              <div class="summary-item offline">
                <span class="count">{{ offlineCount }}</span>
                <span class="label">离线</span>
              </div>
              <div class="summary-item warning">
                <span class="count">{{ warningCount }}</span>
                <span class="label">告警</span>
              </div>
            </div>
          </div>

          <div class="server-grid">
            <div
              v-for="server in dataChart"
              :key="server.id"
              class="server-card"
              :class="getStatusClass(server)"
              @click="selectServer(server)"
            >
              <div class="server-header">
                <h4>{{ server.name }}</h4>
                <span class="status-badge" :class="server.status">
                  {{ getStatusText(server.status) }}
                </span>
              </div>

              <div class="metrics">
                <div class="metric">
                  <label>CPU</label>
                  <div class="progress-bar">
                    <div
                      class="progress-fill cpu"
                      :style="{ width: server.cpu + '%' }"
                    ></div>
                    <span class="progress-text">{{ server.cpu }}%</span>
                  </div>
                </div>

                <div class="metric">
                  <label>内存</label>
                  <div class="progress-bar">
                    <div
                      class="progress-fill memory"
                      :style="{ width: server.memory + '%' }"
                    ></div>
                    <span class="progress-text">{{ server.memory }}%</span>
                  </div>
                </div>

                <div class="metric">
                  <label>磁盘</label>
                  <div class="progress-bar">
                    <div
                      class="progress-fill disk"
                      :style="{ width: server.disk + '%' }"
                    ></div>
                    <span class="progress-text">{{ server.disk }}%</span>
                  </div>
                </div>
              </div>

              <div class="server-footer">
                <span class="network">网络: {{ server.network }}MB/s</span>
                <span class="update-time">{{ server.lastUpdate }}</span>
              </div>
            </div>
          </div>

          <div v-if="!dataChart.length" class="loading">
            <div class="spinner"></div>
            <p>正在加载服务器数据...</p>
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        computed: {
          onlineCount() {
            return this.dataChart.filter(s => s.status === 'online').length;
          },
          offlineCount() {
            return this.dataChart.filter(s => s.status === 'offline').length;
          },
          warningCount() {
            return this.dataChart.filter(s => s.status === 'warning').length;
          }
        },
        methods: {
          getStatusClass(server) {
            return {
              'status-online': server.status === 'online',
              'status-offline': server.status === 'offline',
              'status-warning': server.status === 'warning'
            };
          },
          getStatusText(status) {
            const statusMap = {
              'online': '在线',
              'offline': '离线',
              'warning': '告警'
            };
            return statusMap[status] || '未知';
          },
          selectServer(server) {
            this.$emit('server-select', server);
            console.log('选择服务器:', server.name);
          }
        }
      }
      </script>

      <style>
      .monitor-dashboard {
        padding: 20px;
        background: #0f172a;
        color: #e2e8f0;
        border-radius: 12px;
        min-height: 400px;
      }
      .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #334155;
      }
      .dashboard-header h2 {
        margin: 0;
        color: #f1f5f9;
      }
      .status-summary {
        display: flex;
        gap: 20px;
      }
      .summary-item {
        text-align: center;
        padding: 10px 15px;
        border-radius: 8px;
        background: rgba(255,255,255,0.05);
      }
      .summary-item.online { border-left: 4px solid #10b981; }
      .summary-item.offline { border-left: 4px solid #ef4444; }
      .summary-item.warning { border-left: 4px solid #f59e0b; }
      .summary-item .count {
        display: block;
        font-size: 24px;
        font-weight: bold;
      }
      .summary-item .label {
        font-size: 12px;
        opacity: 0.8;
      }
      .server-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
      }
      .server-card {
        background: #1e293b;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #334155;
      }
      .server-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
      }
      .server-card.status-online {
        border-left: 4px solid #10b981;
      }
      .server-card.status-offline {
        border-left: 4px solid #ef4444;
      }
      .server-card.status-warning {
        border-left: 4px solid #f59e0b;
      }
      .server-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      .server-header h4 {
        margin: 0;
        color: #f1f5f9;
      }
      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
      }
      .status-badge.online {
        background: #10b981;
        color: white;
      }
      .status-badge.offline {
        background: #ef4444;
        color: white;
      }
      .status-badge.warning {
        background: #f59e0b;
        color: white;
      }
      .metrics {
        margin-bottom: 15px;
      }
      .metric {
        margin-bottom: 10px;
      }
      .metric label {
        display: block;
        font-size: 12px;
        margin-bottom: 5px;
        color: #94a3b8;
      }
      .progress-bar {
        position: relative;
        height: 20px;
        background: #334155;
        border-radius: 10px;
        overflow: hidden;
      }
      .progress-fill {
        height: 100%;
        transition: width 0.5s ease;
        border-radius: 10px;
      }
      .progress-fill.cpu { background: #3b82f6; }
      .progress-fill.memory { background: #10b981; }
      .progress-fill.disk { background: #f59e0b; }
      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 11px;
        font-weight: bold;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
      }
      .server-footer {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #94a3b8;
      }
      .loading {
        text-align: center;
        padding: 50px;
      }
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #334155;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      </style>
    `
  },

  // 点击事件处理
  clickFormatter: `(params, refs) => {
    console.log('服务器监控事件:', params);
    // 可以触发详细信息面板或其他组件更新
    if (refs.serverDetailPanel) {
      refs.serverDetailPanel.updateData({
        serverId: params.data.id
      });
    }
  }`
};
```

#### 案例2.2: 动态表单组件

```javascript
const dynamicFormExample = {
  width: 600,
  height: 400,
  dataType: 1,
  dataMethod: "post",
  url: "https://api.example.com/form/config",

  // 获取表单配置
  dataQuery: `(url) => ({
    formType: 'user_profile',
    version: '1.0'
  })`,

  dataHeader: `(url) => ({
    'Authorization': 'Bearer ' + localStorage.getItem('token'),
    'Content-Type': 'application/json'
  })`,

  // 处理表单配置数据
  dataFormatter: `(data, params, refs) => {
    if (data.code === 200) {
      return {
        fields: data.data.fields,
        rules: data.data.validation,
        layout: data.data.layout
      };
    }
    return { fields: [], rules: {}, layout: 'vertical' };
  }`,

  option: {
    content: `
      <template>
        <div class="dynamic-form">
          <h3>动态表单</h3>
          <form @submit.prevent="handleSubmit" v-if="formConfig.fields.length">
            <div
              v-for="field in formConfig.fields"
              :key="field.name"
              class="form-group"
              :class="formConfig.layout"
            >
              <label :for="field.name">{{ field.label }}</label>

              <!-- 文本输入 -->
              <input
                v-if="field.type === 'text'"
                :id="field.name"
                v-model="formData[field.name]"
                :placeholder="field.placeholder"
                :required="field.required"
                class="form-control"
              />

              <!-- 数字输入 -->
              <input
                v-else-if="field.type === 'number'"
                type="number"
                :id="field.name"
                v-model.number="formData[field.name]"
                :min="field.min"
                :max="field.max"
                :required="field.required"
                class="form-control"
              />

              <!-- 选择框 -->
              <select
                v-else-if="field.type === 'select'"
                :id="field.name"
                v-model="formData[field.name]"
                :required="field.required"
                class="form-control"
              >
                <option value="">请选择</option>
                <option
                  v-for="option in field.options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>

              <!-- 多行文本 -->
              <textarea
                v-else-if="field.type === 'textarea'"
                :id="field.name"
                v-model="formData[field.name]"
                :placeholder="field.placeholder"
                :required="field.required"
                class="form-control"
                rows="3"
              ></textarea>

              <!-- 复选框 -->
              <div v-else-if="field.type === 'checkbox'" class="checkbox-group">
                <label
                  v-for="option in field.options"
                  :key="option.value"
                  class="checkbox-label"
                >
                  <input
                    type="checkbox"
                    :value="option.value"
                    v-model="formData[field.name]"
                  />
                  {{ option.label }}
                </label>
              </div>

              <!-- 单选框 -->
              <div v-else-if="field.type === 'radio'" class="radio-group">
                <label
                  v-for="option in field.options"
                  :key="option.value"
                  class="radio-label"
                >
                  <input
                    type="radio"
                    :value="option.value"
                    v-model="formData[field.name]"
                  />
                  {{ option.label }}
                </label>
              </div>

              <!-- 验证错误信息 -->
              <div v-if="errors[field.name]" class="error-message">
                {{ errors[field.name] }}
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn btn-primary" :disabled="submitting">
                {{ submitting ? '提交中...' : '提交' }}
              </button>
              <button type="button" class="btn btn-secondary" @click="resetForm">
                重置
              </button>
            </div>
          </form>

          <div v-else class="loading">
            正在加载表单配置...
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        data() {
          return {
            formData: {},
            errors: {},
            submitting: false
          }
        },
        computed: {
          formConfig() {
            return this.dataChart || { fields: [], rules: {}, layout: 'vertical' };
          }
        },
        watch: {
          'formConfig.fields': {
            handler(fields) {
              // 初始化表单数据
              const data = {};
              fields.forEach(field => {
                if (field.type === 'checkbox') {
                  data[field.name] = [];
                } else {
                  data[field.name] = field.defaultValue || '';
                }
              });
              this.formData = data;
            },
            immediate: true
          }
        },
        methods: {
          validateForm() {
            this.errors = {};
            let isValid = true;

            this.formConfig.fields.forEach(field => {
              const value = this.formData[field.name];
              const rules = this.formConfig.rules[field.name] || [];

              rules.forEach(rule => {
                if (rule.required && (!value || value.length === 0)) {
                  this.errors[field.name] = field.label + '是必填项';
                  isValid = false;
                }
                if (rule.minLength && value.length < rule.minLength) {
                  this.errors[field.name] = field.label + '最少' + rule.minLength + '个字符';
                  isValid = false;
                }
                if (rule.pattern && !new RegExp(rule.pattern).test(value)) {
                  this.errors[field.name] = rule.message || field.label + '格式不正确';
                  isValid = false;
                }
              });
            });

            return isValid;
          },
          async handleSubmit() {
            if (!this.validateForm()) return;

            this.submitting = true;
            try {
              // 模拟提交
              await new Promise(resolve => setTimeout(resolve, 2000));
              this.$emit('form-submit', this.formData);
              alert('提交成功！');
            } catch (error) {
              alert('提交失败：' + error.message);
            } finally {
              this.submitting = false;
            }
          },
          resetForm() {
            this.formConfig.fields.forEach(field => {
              if (field.type === 'checkbox') {
                this.formData[field.name] = [];
              } else {
                this.formData[field.name] = field.defaultValue || '';
              }
            });
            this.errors = {};
          }
        }
      }
      </script>

      <style>
      .dynamic-form {
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
      .form-group {
        margin-bottom: 20px;
      }
      .form-group.horizontal {
        display: flex;
        align-items: center;
        gap: 15px;
      }
      .form-group.horizontal label {
        width: 120px;
        margin-bottom: 0;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
      }
      .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }
      .form-control:focus {
        outline: none;
        border-color: #409EFF;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
      .checkbox-group, .radio-group {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
      }
      .checkbox-label, .radio-label {
        display: flex;
        align-items: center;
        gap: 5px;
        font-weight: normal;
        cursor: pointer;
      }
      .error-message {
        color: #F56C6C;
        font-size: 12px;
        margin-top: 5px;
      }
      .form-actions {
        display: flex;
        gap: 10px;
        margin-top: 30px;
      }
      .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
      }
      .btn-primary {
        background: #409EFF;
        color: white;
      }
      .btn-primary:hover:not(:disabled) {
        background: #337ecc;
      }
      .btn-primary:disabled {
        background: #c0c4cc;
        cursor: not-allowed;
      }
      .btn-secondary {
        background: #909399;
        color: white;
      }
      .btn-secondary:hover {
        background: #767a82;
      }
      .loading {
        text-align: center;
        padding: 50px;
        color: #909399;
      }
      </style>
    `
  }
};
```

### 3. 自定义数据接口案例

#### 案例3.1: 数据集查询组件

```javascript
const customDataExample = {
  width: 700,
  height: 600,
  dataType: 5,  // 自定义数据接口
  dataSet: 1001,  // 数据集ID

  // 自定义数据接口配置
  isCustom: btoa(JSON.stringify({
    paramters: [
      { key: "department", value: "技术部" },
      { key: "status", value: "active" },
      { key: "startDate", value: "2024-01-01" },
      { key: "endDate", value: "2024-12-31" }
    ]
  })),

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    // 自定义数据接口返回的数据处理
    if (Array.isArray(data)) {
      return data.map(item => ({
        id: item.ID || item.id,
        name: item.Name || item.name,
        department: item.Department || item.department,
        position: item.Position || item.position,
        salary: item.Salary || item.salary,
        performance: item.Performance || item.performance,
        joinDate: item.JoinDate || item.joinDate,
        status: item.Status || item.status
      }));
    }
    return [];
  }`,

  option: {
    content: `
      <template>
        <div class="employee-management">
          <div class="management-header">
            <h2>员工管理系统</h2>
            <div class="filter-controls">
              <select v-model="filterDepartment" @change="applyFilter">
                <option value="">全部部门</option>
                <option v-for="dept in departments" :key="dept" :value="dept">
                  {{ dept }}
                </option>
              </select>
              <select v-model="filterStatus" @change="applyFilter">
                <option value="">全部状态</option>
                <option value="active">在职</option>
                <option value="inactive">离职</option>
              </select>
              <button @click="exportData" class="export-btn">导出数据</button>
            </div>
          </div>

          <div class="stats-overview">
            <div class="stat-card">
              <h4>总员工数</h4>
              <span class="stat-number">{{ totalEmployees }}</span>
            </div>
            <div class="stat-card">
              <h4>平均薪资</h4>
              <span class="stat-number">¥{{ averageSalary }}</span>
            </div>
            <div class="stat-card">
              <h4>优秀员工</h4>
              <span class="stat-number">{{ excellentEmployees }}</span>
            </div>
            <div class="stat-card">
              <h4>部门数量</h4>
              <span class="stat-number">{{ departments.length }}</span>
            </div>
          </div>

          <div class="employee-table">
            <div class="table-header">
              <div class="header-cell">姓名</div>
              <div class="header-cell">部门</div>
              <div class="header-cell">职位</div>
              <div class="header-cell">薪资</div>
              <div class="header-cell">绩效</div>
              <div class="header-cell">入职日期</div>
              <div class="header-cell">状态</div>
              <div class="header-cell">操作</div>
            </div>

            <div class="table-body">
              <div
                v-for="employee in filteredEmployees"
                :key="employee.id"
                class="table-row"
                :class="{ 'excellent': employee.performance >= 90 }"
              >
                <div class="table-cell">
                  <div class="employee-info">
                    <div class="avatar">{{ employee.name.charAt(0) }}</div>
                    <span>{{ employee.name }}</span>
                  </div>
                </div>
                <div class="table-cell">{{ employee.department }}</div>
                <div class="table-cell">{{ employee.position }}</div>
                <div class="table-cell salary">¥{{ formatSalary(employee.salary) }}</div>
                <div class="table-cell">
                  <div class="performance-bar">
                    <div
                      class="performance-fill"
                      :style="{
                        width: employee.performance + '%',
                        background: getPerformanceColor(employee.performance)
                      }"
                    ></div>
                    <span class="performance-text">{{ employee.performance }}%</span>
                  </div>
                </div>
                <div class="table-cell">{{ formatDate(employee.joinDate) }}</div>
                <div class="table-cell">
                  <span
                    class="status-badge"
                    :class="employee.status"
                  >
                    {{ employee.status === 'active' ? '在职' : '离职' }}
                  </span>
                </div>
                <div class="table-cell">
                  <button @click="viewEmployee(employee)" class="action-btn view">查看</button>
                  <button @click="editEmployee(employee)" class="action-btn edit">编辑</button>
                </div>
              </div>
            </div>
          </div>

          <div v-if="!dataChart.length" class="no-data">
            <p>暂无员工数据</p>
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        data() {
          return {
            filterDepartment: '',
            filterStatus: ''
          }
        },
        computed: {
          filteredEmployees() {
            let employees = this.dataChart || [];

            if (this.filterDepartment) {
              employees = employees.filter(emp => emp.department === this.filterDepartment);
            }

            if (this.filterStatus) {
              employees = employees.filter(emp => emp.status === this.filterStatus);
            }

            return employees;
          },
          departments() {
            const depts = [...new Set((this.dataChart || []).map(emp => emp.department))];
            return depts.filter(dept => dept);
          },
          totalEmployees() {
            return this.filteredEmployees.length;
          },
          averageSalary() {
            if (!this.filteredEmployees.length) return 0;
            const total = this.filteredEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0);
            return Math.round(total / this.filteredEmployees.length);
          },
          excellentEmployees() {
            return this.filteredEmployees.filter(emp => emp.performance >= 90).length;
          }
        },
        methods: {
          formatSalary(salary) {
            return (salary || 0).toLocaleString();
          },
          formatDate(dateStr) {
            if (!dateStr) return '';
            return new Date(dateStr).toLocaleDateString();
          },
          getPerformanceColor(performance) {
            if (performance >= 90) return '#67C23A';
            if (performance >= 80) return '#E6A23C';
            if (performance >= 60) return '#409EFF';
            return '#F56C6C';
          },
          applyFilter() {
            // 触发数据重新筛选
            this.$emit('filter-change', {
              department: this.filterDepartment,
              status: this.filterStatus
            });
          },
          viewEmployee(employee) {
            this.$emit('employee-view', employee);
            console.log('查看员工:', employee.name);
          },
          editEmployee(employee) {
            this.$emit('employee-edit', employee);
            console.log('编辑员工:', employee.name);
          },
          exportData() {
            // 导出当前筛选的数据
            const csvContent = this.generateCSV(this.filteredEmployees);
            this.downloadCSV(csvContent, 'employees.csv');
          },
          generateCSV(data) {
            const headers = ['姓名', '部门', '职位', '薪资', '绩效', '入职日期', '状态'];
            const rows = data.map(emp => [
              emp.name,
              emp.department,
              emp.position,
              emp.salary,
              emp.performance + '%',
              this.formatDate(emp.joinDate),
              emp.status === 'active' ? '在职' : '离职'
            ]);

            return [headers, ...rows].map(row => row.join(',')).join('\\n');
          },
          downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
          }
        }
      }
      </script>

      <style>
      .employee-management {
        padding: 20px;
        background: #f8fafc;
        border-radius: 12px;
        font-family: 'Microsoft YaHei', sans-serif;
      }
      .management-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .management-header h2 {
        margin: 0;
        color: #2d3748;
      }
      .filter-controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }
      .filter-controls select {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
      }
      .export-btn {
        padding: 8px 16px;
        background: #10b981;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: background 0.3s ease;
      }
      .export-btn:hover {
        background: #059669;
      }
      .stats-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
      }
      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .stat-card h4 {
        margin: 0 0 10px 0;
        color: #6b7280;
        font-size: 14px;
      }
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #1f2937;
      }
      .employee-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
      .table-header {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1.5fr;
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
      }
      .header-cell {
        padding: 15px 10px;
        font-weight: bold;
        color: #374151;
        text-align: center;
      }
      .table-body {
        max-height: 400px;
        overflow-y: auto;
      }
      .table-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1.5fr;
        border-bottom: 1px solid #f3f4f6;
        transition: background 0.2s ease;
      }
      .table-row:hover {
        background: #f9fafb;
      }
      .table-row.excellent {
        background: #f0fdf4;
        border-left: 4px solid #10b981;
      }
      .table-cell {
        padding: 15px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
      .employee-info {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #6366f1;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
      }
      .salary {
        font-weight: bold;
        color: #059669;
      }
      .performance-bar {
        position: relative;
        width: 80px;
        height: 20px;
        background: #e5e7eb;
        border-radius: 10px;
        overflow: hidden;
      }
      .performance-fill {
        height: 100%;
        transition: width 0.5s ease;
      }
      .performance-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 11px;
        font-weight: bold;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
      }
      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
      }
      .status-badge.active {
        background: #d1fae5;
        color: #065f46;
      }
      .status-badge.inactive {
        background: #fee2e2;
        color: #991b1b;
      }
      .action-btn {
        padding: 4px 8px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        margin: 0 2px;
        transition: all 0.2s ease;
      }
      .action-btn.view {
        background: #dbeafe;
        color: #1e40af;
      }
      .action-btn.view:hover {
        background: #bfdbfe;
      }
      .action-btn.edit {
        background: #fef3c7;
        color: #92400e;
      }
      .action-btn.edit:hover {
        background: #fde68a;
      }
      .no-data {
        text-align: center;
        padding: 50px;
        color: #6b7280;
      }
      </style>
    `
  },

  // 点击事件处理
  clickFormatter: `(params, refs) => {
    console.log('员工管理事件:', params);
    // 根据事件类型处理不同逻辑
    if (params.type === 'employee-view') {
      // 显示员工详情
      if (refs.employeeDetail) {
        refs.employeeDetail.updateData({
          employeeId: params.data.id
        });
      }
    } else if (params.type === 'filter-change') {
      // 更新筛选条件，重新获取数据
      // 可以调用updateData方法重新获取数据
    }
  }`
};
```

### 4. 全局数据源案例

#### 案例4.1: 数据联动仪表板

```javascript
const globalDataExample = {
  width: 800,
  height: 600,
  dataType: 6,  // 全局数据源
  dataSet: 2001,  // 全局数据集ID

  // 全局数据源配置
  globalDataConfig: {
    globalDataSource: [{
      time: 3000  // 3秒刷新间隔
    }]
  },

  // 自定义数据接口配置（用于全局数据源）
  isCustom: btoa(JSON.stringify({
    paramters: [
      { key: "dateRange", value: "last7days" },
      { key: "region", value: "all" },
      { key: "category", value: "sales" }
    ]
  })),

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    // 全局数据源返回的数据处理
    if (Array.isArray(data)) {
      return {
        summary: {
          totalSales: data.reduce((sum, item) => sum + (item.sales || 0), 0),
          totalOrders: data.reduce((sum, item) => sum + (item.orders || 0), 0),
          avgOrderValue: 0,
          growthRate: 0
        },
        regions: data.map(item => ({
          name: item.region,
          sales: item.sales,
          orders: item.orders,
          customers: item.customers,
          trend: item.trend
        })),
        timeline: data[0]?.timeline || []
      };
    }
    return { summary: {}, regions: [], timeline: [] };
  }`,

  option: {
    content: `
      <template>
        <div class="dashboard-container">
          <div class="dashboard-header">
            <h1>销售数据仪表板</h1>
            <div class="refresh-info">
              <span class="refresh-indicator" :class="{ active: isRefreshing }"></span>
              <span>实时更新</span>
            </div>
          </div>

          <!-- 关键指标卡片 -->
          <div class="kpi-cards">
            <div class="kpi-card sales">
              <div class="kpi-icon">💰</div>
              <div class="kpi-content">
                <h3>总销售额</h3>
                <div class="kpi-value">¥{{ formatNumber(dashboardData.summary.totalSales) }}</div>
                <div class="kpi-trend positive">↗ +12.5%</div>
              </div>
            </div>

            <div class="kpi-card orders">
              <div class="kpi-icon">📦</div>
              <div class="kpi-content">
                <h3>订单数量</h3>
                <div class="kpi-value">{{ formatNumber(dashboardData.summary.totalOrders) }}</div>
                <div class="kpi-trend positive">↗ +8.3%</div>
              </div>
            </div>

            <div class="kpi-card customers">
              <div class="kpi-icon">👥</div>
              <div class="kpi-content">
                <h3>客户数量</h3>
                <div class="kpi-value">{{ formatNumber(totalCustomers) }}</div>
                <div class="kpi-trend negative">↘ -2.1%</div>
              </div>
            </div>

            <div class="kpi-card conversion">
              <div class="kpi-icon">📈</div>
              <div class="kpi-content">
                <h3>转化率</h3>
                <div class="kpi-value">{{ conversionRate }}%</div>
                <div class="kpi-trend positive">↗ +5.7%</div>
              </div>
            </div>
          </div>

          <!-- 区域销售数据 -->
          <div class="regions-section">
            <h2>区域销售分析</h2>
            <div class="regions-grid">
              <div
                v-for="region in dashboardData.regions"
                :key="region.name"
                class="region-card"
                @click="selectRegion(region)"
                :class="{ active: selectedRegion === region.name }"
              >
                <div class="region-header">
                  <h4>{{ region.name }}</h4>
                  <span class="trend-indicator" :class="getTrendClass(region.trend)">
                    {{ getTrendIcon(region.trend) }}
                  </span>
                </div>

                <div class="region-metrics">
                  <div class="metric">
                    <label>销售额</label>
                    <span class="value">¥{{ formatNumber(region.sales) }}</span>
                  </div>
                  <div class="metric">
                    <label>订单数</label>
                    <span class="value">{{ region.orders }}</span>
                  </div>
                  <div class="metric">
                    <label>客户数</label>
                    <span class="value">{{ region.customers }}</span>
                  </div>
                </div>

                <div class="region-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: getProgressWidth(region.sales) + '%' }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ getProgressWidth(region.sales) }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 时间趋势图 -->
          <div class="timeline-section" v-if="dashboardData.timeline.length">
            <h2>销售趋势</h2>
            <div class="timeline-chart">
              <div class="chart-container">
                <svg width="100%" height="200" viewBox="0 0 800 200">
                  <defs>
                    <linearGradient id="salesGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:0.3" />
                      <stop offset="100%" style="stop-color:#4F46E5;stop-opacity:0" />
                    </linearGradient>
                  </defs>

                  <!-- 网格线 -->
                  <g class="grid">
                    <line v-for="i in 5" :key="'h'+i"
                          :x1="0" :y1="i*40" :x2="800" :y2="i*40"
                          stroke="#e5e7eb" stroke-width="1"/>
                    <line v-for="i in 8" :key="'v'+i"
                          :x1="i*100" :y1="0" :x2="i*100" :y2="200"
                          stroke="#e5e7eb" stroke-width="1"/>
                  </g>

                  <!-- 趋势线 -->
                  <polyline
                    :points="getTimelinePoints()"
                    fill="url(#salesGradient)"
                    stroke="#4F46E5"
                    stroke-width="3"
                  />

                  <!-- 数据点 -->
                  <circle
                    v-for="(point, index) in dashboardData.timeline"
                    :key="index"
                    :cx="index * (800 / (dashboardData.timeline.length - 1))"
                    :cy="200 - (point.value / maxTimelineValue * 180)"
                    r="4"
                    fill="#4F46E5"
                    @click="selectTimePoint(point)"
                  />
                </svg>
              </div>

              <div class="timeline-labels">
                <span
                  v-for="(point, index) in dashboardData.timeline"
                  :key="index"
                  class="timeline-label"
                >
                  {{ formatDate(point.date) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 数据为空时的提示 -->
          <div v-if="!hasData" class="no-data">
            <div class="no-data-icon">📊</div>
            <h3>暂无数据</h3>
            <p>正在从全局数据源加载数据...</p>
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        data() {
          return {
            selectedRegion: null,
            isRefreshing: false
          }
        },
        computed: {
          dashboardData() {
            return this.dataChart || { summary: {}, regions: [], timeline: [] };
          },
          hasData() {
            return this.dashboardData.regions.length > 0;
          },
          totalCustomers() {
            return this.dashboardData.regions.reduce((sum, region) => sum + (region.customers || 0), 0);
          },
          conversionRate() {
            const totalSales = this.dashboardData.summary.totalSales || 0;
            const totalOrders = this.dashboardData.summary.totalOrders || 0;
            return totalOrders > 0 ? ((totalSales / totalOrders) * 0.01).toFixed(1) : 0;
          },
          maxSales() {
            return Math.max(...this.dashboardData.regions.map(r => r.sales || 0));
          },
          maxTimelineValue() {
            return Math.max(...this.dashboardData.timeline.map(t => t.value || 0));
          }
        },
        watch: {
          dataChart: {
            handler() {
              // 数据更新时的动画效果
              this.isRefreshing = true;
              setTimeout(() => {
                this.isRefreshing = false;
              }, 1000);
            },
            deep: true
          }
        },
        methods: {
          formatNumber(num) {
            if (!num) return '0';
            if (num >= 10000) {
              return (num / 10000).toFixed(1) + '万';
            }
            return num.toLocaleString();
          },
          formatDate(dateStr) {
            return new Date(dateStr).toLocaleDateString('zh-CN', {
              month: 'short',
              day: 'numeric'
            });
          },
          getTrendClass(trend) {
            return {
              'trend-up': trend > 0,
              'trend-down': trend < 0,
              'trend-stable': trend === 0
            };
          },
          getTrendIcon(trend) {
            if (trend > 0) return '↗';
            if (trend < 0) return '↘';
            return '→';
          },
          getProgressWidth(sales) {
            return this.maxSales > 0 ? Math.round((sales / this.maxSales) * 100) : 0;
          },
          getTimelinePoints() {
            if (!this.dashboardData.timeline.length) return '';

            return this.dashboardData.timeline.map((point, index) => {
              const x = index * (800 / (this.dashboardData.timeline.length - 1));
              const y = 200 - (point.value / this.maxTimelineValue * 180);
              return x + ',' + y;
            }).join(' ');
          },
          selectRegion(region) {
            this.selectedRegion = region.name;
            this.$emit('region-select', region);

            // 触发其他组件更新
            this.$emit('data-filter', {
              region: region.name,
              type: 'region-detail'
            });
          },
          selectTimePoint(point) {
            this.$emit('time-select', point);

            // 触发时间范围筛选
            this.$emit('data-filter', {
              date: point.date,
              type: 'time-detail'
            });
          }
        }
      }
      </script>

      <style>
      .dashboard-container {
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 16px;
        min-height: 500px;
      }
      .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
      }
      .dashboard-header h1 {
        margin: 0;
        font-size: 28px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      .refresh-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        opacity: 0.9;
      }
      .refresh-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #10b981;
        animation: pulse 2s infinite;
      }
      .refresh-indicator.active {
        animation: spin 1s linear infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
      .kpi-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
      }
      .kpi-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 15px;
        transition: transform 0.3s ease;
      }
      .kpi-card:hover {
        transform: translateY(-5px);
      }
      .kpi-icon {
        font-size: 32px;
        opacity: 0.8;
      }
      .kpi-content h3 {
        margin: 0 0 8px 0;
        font-size: 14px;
        opacity: 0.8;
      }
      .kpi-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .kpi-trend {
        font-size: 12px;
        font-weight: bold;
      }
      .kpi-trend.positive { color: #10b981; }
      .kpi-trend.negative { color: #ef4444; }
      .regions-section {
        margin-bottom: 40px;
      }
      .regions-section h2 {
        margin: 0 0 20px 0;
        font-size: 20px;
      }
      .regions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
      }
      .region-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-radius: 10px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }
      .region-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
      }
      .region-card.active {
        border-color: #fbbf24;
        background: rgba(251,191,36,0.2);
      }
      .region-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      .region-header h4 {
        margin: 0;
        font-size: 16px;
      }
      .trend-indicator {
        font-size: 18px;
        font-weight: bold;
      }
      .trend-indicator.trend-up { color: #10b981; }
      .trend-indicator.trend-down { color: #ef4444; }
      .trend-indicator.trend-stable { color: #6b7280; }
      .region-metrics {
        margin-bottom: 15px;
      }
      .metric {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
      }
      .metric label {
        opacity: 0.8;
      }
      .metric .value {
        font-weight: bold;
      }
      .region-progress {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .progress-bar {
        flex: 1;
        height: 6px;
        background: rgba(255,255,255,0.2);
        border-radius: 3px;
        overflow: hidden;
      }
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #34d399);
        transition: width 0.5s ease;
      }
      .progress-text {
        font-size: 12px;
        font-weight: bold;
      }
      .timeline-section h2 {
        margin: 0 0 20px 0;
        font-size: 20px;
      }
      .timeline-chart {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 20px;
      }
      .chart-container {
        margin-bottom: 15px;
      }
      .timeline-labels {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        opacity: 0.8;
      }
      .no-data {
        text-align: center;
        padding: 80px 20px;
        opacity: 0.8;
      }
      .no-data-icon {
        font-size: 64px;
        margin-bottom: 20px;
      }
      .no-data h3 {
        margin: 0 0 10px 0;
        font-size: 24px;
      }
      .no-data p {
        margin: 0;
        font-size: 16px;
        opacity: 0.7;
      }
      </style>
    `
  },

  // 点击事件处理
  clickFormatter: `(params, refs) => {
    console.log('仪表板事件:', params);

    // 根据事件类型处理数据联动
    if (params.type === 'region-select') {
      // 区域选择，更新相关图表
      if (refs.regionDetailChart) {
        refs.regionDetailChart.updateData({
          region: params.data.name
        });
      }
      if (refs.regionTable) {
        refs.regionTable.updateData({
          filter: { region: params.data.name }
        });
      }
    } else if (params.type === 'time-select') {
      // 时间点选择，更新时间相关组件
      if (refs.timeDetailChart) {
        refs.timeDetailChart.updateData({
          date: params.data.date
        });
      }
    }

    // 全局数据源联动示例
    if (params.type === 'data-filter') {
      // 触发全局数据源重新获取数据
      // 这里可以更新全局数据源的参数
      const globalRefs = this.getItemRefs();
      Object.keys(globalRefs).forEach(key => {
        if (globalRefs[key].dataType === 6) {
          // 更新全局数据源组件的参数
          globalRefs[key].updateData(params.data);
        }
      });
    }
  }`
};
```

## 🎯 最佳实践

### 1. 组件设计原则

#### 单一职责原则
```javascript
// ✅ 好的做法：组件功能单一明确
const userProfileComponent = {
  // 只负责用户信息展示
  option: {
    content: `
      <template>
        <div class="user-profile">
          <!-- 只处理用户信息相关的UI -->
        </div>
      </template>
    `
  }
};

// ❌ 避免：组件功能过于复杂
const complexComponent = {
  // 同时处理用户信息、订单管理、统计分析等多个功能
};
```

#### 数据驱动设计
```javascript
// ✅ 好的做法：通过props接收数据，通过事件通信
export default {
  props: ['dataChart'],
  methods: {
    handleAction(data) {
      this.$emit('action-triggered', data);
    }
  }
}

// ❌ 避免：直接操作外部数据或DOM
export default {
  methods: {
    handleAction() {
      // 直接修改全局变量
      window.globalData = newData;
      // 直接操作外部DOM
      document.getElementById('external-element').innerHTML = 'new content';
    }
  }
}
```

### 2. 性能优化

#### 数据处理优化
```javascript
// ✅ 好的做法：在dataFormatter中进行数据预处理
dataFormatter: `(data, params, refs) => {
  // 一次性处理所有数据转换
  return data.map(item => ({
    id: item.id,
    displayName: item.firstName + ' ' + item.lastName,
    formattedSalary: '¥' + item.salary.toLocaleString(),
    statusClass: item.status === 'active' ? 'status-active' : 'status-inactive'
  }));
}`,

// ❌ 避免：在模板中进行复杂计算
// <template>
//   <div v-for="item in dataChart">
//     {{ item.firstName + ' ' + item.lastName }}
//     {{ '¥' + item.salary.toLocaleString() }}
//   </div>
// </template>
```

#### 事件处理优化
```javascript
// ✅ 好的做法：使用事件委托
export default {
  methods: {
    handleContainerClick(event) {
      const target = event.target;
      if (target.classList.contains('item-button')) {
        const itemId = target.dataset.itemId;
        this.handleItemAction(itemId);
      }
    }
  }
}

// ❌ 避免：为每个元素绑定事件
// <div v-for="item in items">
//   <button @click="handleItemAction(item.id)">Action</button>
// </div>
```

### 3. 错误处理

#### 数据容错
```javascript
// ✅ 好的做法：完善的错误处理
dataFormatter: `(data, params, refs) => {
  try {
    if (!data || !Array.isArray(data)) {
      console.warn('数据格式异常，使用默认数据');
      return [];
    }

    return data.map(item => {
      if (!item || typeof item !== 'object') {
        console.warn('数据项格式异常，跳过处理');
        return null;
      }

      return {
        id: item.id || 'unknown',
        name: item.name || '未知',
        value: Number(item.value) || 0,
        status: ['active', 'inactive'].includes(item.status) ? item.status : 'unknown'
      };
    }).filter(Boolean);
  } catch (error) {
    console.error('数据处理错误:', error);
    return [];
  }
}`,

// 组件内部错误处理
export default {
  methods: {
    safeOperation(data) {
      try {
        return this.processData(data);
      } catch (error) {
        console.error('操作失败:', error);
        this.$emit('error', { type: 'processing', error });
        return null;
      }
    }
  }
}
```

### 4. 样式管理

#### CSS作用域和命名
```css
/* ✅ 好的做法：使用BEM命名规范 */
.user-profile {
  /* 组件根容器 */
}
.user-profile__header {
  /* 组件内部元素 */
}
.user-profile__avatar--large {
  /* 组件修饰符 */
}

/* ✅ 好的做法：使用CSS变量 */
.dashboard-theme {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
}

/* ❌ 避免：全局样式污染 */
.header { /* 可能影响其他组件 */ }
.button { /* 过于通用的类名 */ }
```

#### 响应式设计
```css
/* ✅ 好的做法：移动端优先的响应式设计 */
.component-container {
  padding: 10px;
  font-size: 14px;
}

@media (min-width: 768px) {
  .component-container {
    padding: 20px;
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .component-container {
    padding: 30px;
    font-size: 18px;
  }
}
```

### 5. 数据源配置建议

#### API接口配置
```javascript
// ✅ 好的做法：完整的API配置
const apiConfig = {
  dataType: 1,
  dataMethod: "post",
  url: "/api/v1/data",

  // 请求参数配置
  dataQuery: `(url) => ({
    page: 1,
    size: 20,
    filters: {
      status: 'active',
      dateRange: {
        start: new Date(Date.now() - 7*24*60*60*1000).toISOString(),
        end: new Date().toISOString()
      }
    },
    sort: { field: 'createdAt', order: 'desc' }
  })`,

  // 请求头配置
  dataHeader: `(url) => {
    const token = localStorage.getItem('authToken');
    return {
      'Authorization': token ? 'Bearer ' + token : '',
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      'X-Client-Version': '1.0.0'
    };
  }`,

  // 错误处理
  dataFormatter: `(data, params, refs) => {
    if (!data) {
      console.error('API返回数据为空');
      return [];
    }

    if (data.code !== 200) {
      console.error('API返回错误:', data.message);
      // 可以显示错误提示
      if (refs.errorNotification) {
        refs.errorNotification.show(data.message);
      }
      return [];
    }

    return data.data || [];
  }`
};
```

#### 全局数据源配置
```javascript
// ✅ 好的做法：合理的全局数据源配置
const globalConfig = {
  dataType: 6,
  dataSet: 1001,

  // 合理的刷新间隔
  globalDataConfig: {
    globalDataSource: [{
      time: 5000  // 5秒，避免过于频繁的请求
    }]
  },

  // 数据缓存和优化
  dataFormatter: `(data, params, refs) => {
    // 检查数据是否有变化
    const currentHash = JSON.stringify(data).hashCode();
    if (this.lastDataHash === currentHash) {
      console.log('数据未变化，跳过处理');
      return this.lastProcessedData;
    }

    this.lastDataHash = currentHash;
    this.lastProcessedData = processData(data);
    return this.lastProcessedData;
  }`
};
```

## 📚 常见问题解答

### Q1: Vue组件不显示怎么办？
**A**: 检查以下几点：
1. 确认`content`中的Vue语法是否正确
2. 检查`<template>`、`<script>`、`<style>`标签是否完整
3. 查看浏览器控制台是否有JavaScript错误
4. 确认`dataChart`数据是否正确传递

### Q2: 样式不生效怎么办？
**A**:
1. 检查CSS语法是否正确
2. 确认样式没有被其他CSS覆盖
3. 避免使用过于通用的类名
4. 检查是否有CSS作用域冲突

### Q3: 数据更新不及时怎么办？
**A**:
1. 检查数据源配置是否正确
2. 确认`dataFormatter`函数没有错误
3. 对于全局数据源，检查刷新间隔设置
4. 查看网络请求是否正常

### Q4: 组件间通信不工作怎么办？
**A**:
1. 确认事件名称拼写正确
2. 检查`clickFormatter`函数中的逻辑
3. 确认目标组件的引用名称正确
4. 查看控制台是否有相关错误信息

### Q5: 性能问题怎么解决？
**A**:
1. 减少不必要的数据处理
2. 使用`v-if`而不是`v-show`来条件渲染大量内容
3. 合理设置数据刷新间隔
4. 避免在模板中进行复杂计算

## 🔧 开发工具推荐

### 1. 代码编辑器配置
- **VS Code插件**: Vetur、Vue Language Features
- **语法高亮**: 支持Vue单文件组件语法
- **代码格式化**: Prettier + ESLint

### 2. 调试工具
- **Vue DevTools**: 浏览器扩展，用于调试Vue组件
- **Network面板**: 监控数据源请求
- **Console面板**: 查看错误信息和调试输出

### 3. 测试工具
- **单元测试**: Jest + Vue Test Utils
- **端到端测试**: Cypress
- **性能测试**: Lighthouse

## 📖 参考资源

### 官方文档
- [Vue.js 官方文档](https://vuejs.org/)
- [Element UI 组件库](https://element.eleme.io/)
- [ECharts 图表库](https://echarts.apache.org/)

### 社区资源
- [Vue.js 社区](https://vue-community.org/)
- [Awesome Vue](https://github.com/vuejs/awesome-vue)
- [Vue.js Examples](https://vuejsexamples.com/)

---

**🎯 总结**: Vue组件渲染器为CI.Web.Plugins.Bulletin系统提供了强大的自定义组件能力，通过合理使用不同的数据源类型和遵循最佳实践，可以快速开发出功能丰富、性能优秀的数据可视化组件。

**⚠️ 注意**:
- 测试案例全放到 `demo/` 目录下
- 使用当前打包好的 `dist/` 文件
- 参考其它案例使用方法
- 遵循组件开发最佳实践，确保代码质量和可维护性