<template>
  <div     v-loading="loading"
    element-loading-text="loading..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <el-container class="list">
      <el-container>
        <el-main class="contents">
          <div
            style="
              width: 100%;
              height: 40px;
              line-height: 39px;
              display: flex;
              justify-content: space-between;
            "
          >
          <div style=" margin-left: 10px;   display: flex;">
            <!-- <el-button
            style="height: 27.8px;margin-top: 7px;"
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="insertEvent"
          >新增</el-button
        > -->
        <el-upload
           
           style="margin: 0 10px;"
           :show-file-list="false"
           :httpRequest="httpRequest4"
           action=""
           ref="upload"
           accept=".json"
         >
         <el-button
         icon="el-icon-upload2
"
         type="primary"
         size="mini"
         @click="import_part"
         >
         <!-- 导入 -->
         {{ $t('page.largescreentemplate.Import') }}
         </el-button
       >
         </el-upload>

       <el-button
         icon="el-icon-download"
         type="primary"
         size="mini"
         style="height: 27.8px;margin-top: 7px;"
         @click="export_part"
         >
         <!-- 导出 -->
         {{ $t('page.largescreentemplate.Export') }}
         </el-button
       >  
              <el-button
              class="pinBtnOnCla"
              icon="el-icon-tickets"
              type="primary"
              size="small"
              style="height: 27.8px;margin-top: 7px;"
              @click="synchronous()"
            >
              <!-- 同步 -->
              {{ $t('page.largescreentemplate.Synchronize') }}
            </el-button>
          </div>
          
            <div>
              <span style="width: 90px; font-size: 14px">
                <!-- 标签类别 -->
                {{ $t('page.largescreentemplate.TagCategory') }}
                &nbsp;</span>
              <el-select
                filterable
                style="width: 180px"
                v-model="allabel"
                @change="labelchange"
                size="mini"
                placeholder="请选择标签"
                :placeholder="`${$t('page.largescreentemplate.PleaseSelectLabel')}`"
              >
                <el-option
                  v-for="item in labeList"
                  :key="item.CID"
                  :label="item.CNAME"
                  :value="item.CID"
                ></el-option
              ></el-select>
             
              <span style="color: #333; font-size: 14px">
                <!-- 模板名称 -->
                {{ $t('page.largescreentemplate.TemplateName') }}
                &nbsp; </span>
              <el-input
                placeholder="请搜索模板名称"
                :placeholder="`${$t('page.largescreentemplate.PleaseSearchforTemplateName')}`"
                size="mini"
                @keyup.enter.native="searchs"
                style="width: 180px"
                v-model="search"
              >
              </el-input>
              &nbsp;
              <el-button
                title="查询"
                :title="`${$t('page.largescreentemplate.Query')}`"
              type="primary"  
                size="mini"
                icon="el-icon-search"
                @click="searchs"
                >{{
              }}</el-button>
              <el-button
              type="primary"  
                title="重置"
                :title="`${$t('page.largescreentemplate.Reset')}`"
                size="mini"
                icon="el-icon-refresh-right"
                @click="reset"
                >{{
              }}</el-button>
              &nbsp;
            </div>
          </div>
          <div
            class="content__box"
            style="height: calc(100vh - 207px); padding: 5px 0px 0px 4px"
          >
            <div
              @click="handleAdd"
              class="content__items"
              style="background: #ebeef5; text-align: center; margin-top: 0px"
            >
              <i
                class="el-icon-plus"
                style="
                  font-size: 90px;
                  margin-top: 40px;
                  margin-bottom: 10px;
                  color: #c0c4cc;
                "
              ></i>
              <div>
                <!-- 新建模板 -->
                {{ $t('page.largescreentemplate.CreateNewTemplate') }}
              </div>
            </div>
            <div
              class="content__item"
              v-for="(item, index) in list"
              :key="index"
              @mouseover="item._menu = true"
              @mouseout="item._menu = false"
            >
              <div class="content__info">
                <img
                  v-if="item.Kanban.CBACKGROUND_URL"
                  :src="item.Kanban.CBACKGROUND_URL"
                  alt=""
                />
                <div class="content__menu" v-show="item._menu">
                  <!-- <div class="content__right">
                    <el-tooltip content="分享">
                      <i
                        class="el-icon-share"
                        @click="handleUpdate(item, index)"
                      ></i>
                    </el-tooltip>
                    <el-tooltip content="预览">
                        <i
                          class="el-icon-view"
                          @click="handleViews(item, index)"
                        ></i>
                      </el-tooltip> 
                  </div> -->

                  <div class="content__list">
                    <!-- <div class="content__btn" @click="handleEdit(item)">设计</div> -->
                    <div class="content__btns">
                      <i
                        class="el-icon-rank"
                        @click="handleEdit(item)"
                        style="font-size: 25px"
                      ></i
                      ><br />
                      <span>
                        <!-- 设计 -->
                        {{ $t('page.largescreentemplate.Design') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i
                        style="font-size: 25px"
                        class="el-icon-view"
                        @click="handleViews(item, index)"
                      ></i
                      ><br />
                      <span>
                        <!-- 预览 -->
                        {{ $t('page.largescreentemplate.Preview') }}
                      </span>
                    </div>

                    <div class="content__btns">
                      <i
                        class="el-icon-delete"
                        @click="handleDel(item, index)"
                        style="font-size: 25px"
                      ></i
                      ><br />
                      <span>
                        <!-- 删除 -->
                        {{ $t('page.largescreentemplate.Delete') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i
                        class="el-icon-edit"
                        @click="handleupdate(item, index)"
                        style="font-size: 25px"
                      ></i
                      ><br />
                      <span>
                        <!-- 编辑 -->
                        {{ $t('page.largescreentemplate.Edit') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i
                        style="font-size: 25px"
                        class="el-icon-copy-document"
                        @click="handleCopy(item, index)"
                      ></i>
                      <br /><span>
                        <!-- 复制 -->
                        {{ $t('page.largescreentemplate.Copy') }}
                      </span>
                    </div>
                    <!-- <div class="content__btns">
                      <i
                        style="font-size: 25px"
                        class="el-icon-plus"
                        @click="handleAddMoban(item, index)"
                      ></i>
                      <br /><span>添加模板</span>
                    </div> -->
                    <!-- <el-tooltip content="修改">
                        <i
                          class="el-icon-edit"
                          @click="handleUpdate(item, index)"
                        ></i>
                      </el-tooltip> -->
                    <!-- <el-tooltip content="删除">
                        <i
                          class="el-icon-delete"
                          @click="handleDel(item, index)"
                        ></i>
                      </el-tooltip> -->
                    <!-- <el-tooltip content="复制">
                        <i
                          class="el-icon-copy-document"
                          @click="handleCopy(item, index)"
                        ></i>
                      </el-tooltip> -->
                  </div>
                </div>
              </div>
              <div class="content__main">
                <span :title="item.Kanban.CTITLE" class="content__name" style="width:200px">
                  <el-checkbox-group v-model="checkList" @change="checkchang">
    <el-checkbox :label="item.Kanban.CID">{{ item.Kanban.CTITLE }}</el-checkbox>
    
  </el-checkbox-group>
                  
                  
                   </span>
                <div class="content__menulist">
                  <div class="content__view"></div>
                  <span
                    style="color: #666"
                    class="content__status"
                    :class="{ 'content__status--active': item.status }"
                  >
                  {{getCategoryName(item.Kanban.CKANBAN_LABEL_ID)}}
                    <!-- {{  CKANBAN_TYPE item.status == 1 ? "已发布" : "未发布" }} -->
                  </span>
                </div>
              </div>
            </div>
            <div class="page_vxes" v-if="!loading">
              <vxe-pager
                align="right"
                background
                @page-change="pageChange"
                :current-page.sync="page.page"
                :page-size.sync="page.size"
                :total="page.total"
                :layouts="[
                  'PrevJump',
                  'PrevPage',
                  'JumpNumber',
                  'NextPage',
                  'NextJump',
                  'Sizes',
                  'FullJump',
                  'Total',
                ]"
              ></vxe-pager>
            </div>
          </div>
        </el-main>
      </el-container>
      <vxe-modal v-model="box" width="600" @close="cancel('ruleForm')">
        <template #title>
          <!-- <span>{{ title }}</span> -->
          <span>{{ $t(title) }}</span>
        </template>
        <template #default>
          <el-form
            style="width: 100%; margin-top: 8px"
            :model="ruleForm"
            ref="ruleForm"
            label-width="100px"
            :rules="rules"
            class="demo-ruleForm1"
          >
            <el-form-item label="模板名称" :label="`${$t('page.largescreentemplate.TemplateName')}`" prop="CTITLE">
              <el-input
                v-model="ruleForm.CTITLE"
                placeholder="请输入模板名称"
                :placeholder="`${$t('page.largescreentemplate.PleaseEnterTemplateName')}`"
              ></el-input>
            </el-form-item>

            <el-form-item label="模板标签" :label="`${$t('page.largescreentemplate.TemplateTag')}`" prop="CKANBAN_LABEL_ID">
              <el-select
                filterable
                ref="selecteltree"
                style="width: 100%"
                v-model="ruleForm.CKANBAN_LABEL_ID"
                clearable
                placeholder="请选择标签"
                 :placeholder="`${$t('page.largescreentemplate.PleaseSelectLabel')}`"
              >
                <el-option
                  v-for="item in labeList"
                  :key="item.CID"
                  :label="item.CNAME"
                  :value="item.CID"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="大屏宽度" :label="`${$t('page.largescreentemplate.BigScreenWidth')}`">
              <el-input
                v-model="ruleForm.CWIDTH"
                placeholder="请输入大屏宽度"
                 :placeholder="`${$t('page.largescreentemplate.PleaseEnterBigScreenWidth')}`"
              ></el-input>
            </el-form-item>
            <el-form-item label="大屏高度" :label="`${$t('page.largescreentemplate.BigScreenHeight')}`">
              <el-input
                v-model="ruleForm.CHEIGHT"
                placeholder="请输入大屏高度"
                  :placeholder="`${$t('page.largescreentemplate.PleaseEnterBigScreenHeight')}`"
              ></el-input>
            </el-form-item>

            <el-form-item align="right">
              <el-button size="small" @click="cancel('ruleForm')"
                >
                <!-- 取消 -->
                {{ $t('page.largescreentemplate.Cancel') }}
                </el-button
              >&nbsp;<el-button
                size="small"
                type="primary"
                @click="onsubmit('ruleForm')"
                >
                <!-- 确认 -->
                {{ $t('page.largescreentemplate.Confirm') }}
                </el-button
              >
            </el-form-item>
          </el-form>
        </template>
      </vxe-modal>
    </el-container>
  </div>
</template>
<script>
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import {
  getList,
  updateObj,
  kanbanTemplateDeleteByID,
  getCategory,
  AddTemplate,
  GetPageListByQueryJson,
  getall,
  kanbanTemplate,
  TemplateGetPageList,
  UpdateTemplate,
  PushData,Import1,Export1
} from "@/api/visual";
import { getObj } from "@/api/visual";
import { url } from "@/config";
export default {
  name: "list",
  data() {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      loading:true,
      index: 0,
      title: "",
      labeList: [],
      allabel: 0,
      checkList:[],
      ruleForm: {
        CTITLE: "",

        CKANBAN_LABEL_ID: "",

        CWIDTH: "1920",
        CHEIGHT: "1080",
      },
      rules: {
        CTITLE: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: "blur",
          },
        ],
        CKANBAN_LABEL_ID: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      search: "",
      expandOnClickNode: false,
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
      dataTypeList: [],
      defaultProps: {
        label: "CNAME",
        children: "CHILDS",
      },
      typeList: [],
      box: false,

      page: {
        page: 1,
        size: 20,
        total: 0,
      },
      form: {},
      activeName: "",
      list: [],
      optionList: [],
      copymoban: "",
    };
  },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    this.rules= {
        CTITLE: [
          {
            required: true,
            message: this.$t('rules.PleaseEnterTemplateName'),//"请输入模板名称",
            trigger: "blur",
          },
        ],
        CKANBAN_LABEL_ID: [
          {
            required: true,
            message: this.$t('rules.PleaseSelect'),//"请选择",
            trigger: "change",
          },
        ],
      }
  },
  created() {
    //this.getCategory();
    this.getAll();
    this.$eventBus.$on("updateMoBanList", () => {
      this.reset();
    });
  },
  methods: {
    getCategoryName(CID){
      let _lbl = this.labeList.find((item) => item.CID == CID);
      return _lbl ? _lbl.CNAME : "";
    },
    checkchang(value){
   //console.log(this.checkList,value);
    },



  //同步
  synchronous(){
    
      PushData(this.checkList).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
        }else{
          this.$message.error(res.data.Content)
        }
      })
    },
    httpRequest4(file){
   const reader = new FileReader();
 // 异步处理文件数据
 reader.readAsText(file.file, 'UTF-8');
 // 处理完成后立马触发 onload
 reader.onload = fileReader => {
     const fileData = fileReader.target.result;
     if(fileData){
      var text=JSON.parse(fileData)

      res=res.data
      Import1(text).then(res=>{
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
          this.getAll()

        }else{
          this.$message.error(res.data.Content)
        }
      })
     }
    
     // 上面的两个输出相同
 };

    },
    import_part() {
   //   console.log(111);
    },
    export_part() {
    
     Export1(this.checkList).then(res=>{
      res=res.data
        if(res.code===200&&res.data.Success){
        let link = document.createElement("a");
        link.download = "看板模板.json";
        link.href = "data:text/plain," + JSON.stringify(res.data.Datas);
        link.click();
        }else{
          this.$message.error(res.data.Content)
        }
     })
     
    },













    handleupdate(item, index) {
      this.title = "编辑模板";
      this.ruleForm = item.Kanban;
      this.box = true;
    },
    //获取模板库标签
    getAll() {
      getall({
        start: 1,
        length: 1000,
        condition: "",
      }).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.labeList = [
            {
              CID: 0,
              CNAME: "全部",
            },
            ...res.data.Datas,
          ];
          this.getList();
        }
      });
    },
    labelchange() {
   //   console.log(this.allabel);
      this.getList();
    },
    cancel(formName) {
      // this.$refs[formName].resetFields();
      this.box = false;
    },
    //保存
    onsubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.title == "新建模板") {
            kanbanTemplate(this.ruleForm).then((res) => {
              res = res.data;
              if (res.code == 200 && res.data.Success) {
                this.$message({
                  message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
                  type: "success",
                });
                this.$refs[formName].resetFields();
                this.box = false;
                this.getList();
              } else {
                this.$message({
                  message: res.data.Content || res.message.msg,
                  type: "error",
                });
              }
            });
          } else if (this.title == "编辑模板") {
            UpdateTemplate(this.ruleForm).then((res) => {
              res = res.data;
              if (res.code == 200 && res.data.Success) {
                this.$message({
                  message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
                  type: "success",
                });
                this.$refs[formName].resetFields();
                this.box = false;
                this.getList();
              } else {
                this.$message({
                  message: res.data.Content || res.message.msg,
                  type: "error",
                });
              }
            });
          } else {
            this.copymoban.Kanban = this.ruleForm;
            var value = JSON.parse(this.copymoban.KanbanConfig.CDETAIL);
            this.copymoban.Kanban.CHEIGHT = value.height;
            this.copymoban.Kanban.CWIDTH = value.width;
            this.copymoban.Kanban.CTITLE = value.name;
            AddTemplate(this.copymoban).then((res) => {
              res = res.data;
              if (res.code == 200 && res.data.Success) {
                this.$message({
                  message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
                  type: "success",
                });
                this.$refs[formName].resetFields();
                this.box = false;
                this.getList();
              } else {
                this.$message({
                  message: res.data.Content || res.message.msg,
                  type: "error",
                });
              }
            });
          }
        } else {
       //   console.log("error submit!!");
          return false;
        }
      });
    },
    //分享复制
    onCopy() {
      document.getElementById("input").select();
      document.execCommand("Copy");
      document.getElementById("input").blur();
    },
    //添加模板
    // handleAddMoban() {
    //   alert("暂无接口");
    // },
    pageChange(val) {
      this.loading=true
   
      this.page.page = val.currentPage;
      this.page.size = val.pageSize;
      this.getList(this.search);
    },
    //搜索看板列表
    searchs() {
      this.loading=true
      this.getList(this.search);
    
    },
    //重置
    reset() {
      this.loading=true
      this.search = "";
      this.page.page = 1;
      this.getList();
    },
    handleSelect(key) {
      this.activeName = key;
      this.page.page = 1;
      this.getList();
    },
    TreeToArray(tree) {
      // 判断 tree 是否有值，无返回 []
      if (!Array.isArray(tree) || !tree.length) return [];
      let res = [];
      tree.forEach((v) => {
        // tree的每个元素都 放入到 res里面
        res.push(v);
        if (v.CHILDS) {
          // 有children 就把 children数据递归 返回  依次放到 res里面
          res.push(...this.TreeToArray(v.CHILDS));
        }
      });
      return res;
    },

    // //下拉树模板
    // handleNodeClicks(node) {
    //   this.ruleForm.CGROUP_ID = node.CID;
    //   this.$refs.selecteltree.blur();
    // },
    // //根据模板id筛选
    // handleNodeClick() {
    //   var params = {
    //     queryJson: { ids: this.$refs.selectTree.getCheckedKeys() },
    //     start: this.page.page,
    //     length: this.page.size,
    //   };

    //   GetPageListByQueryJson(params).then((res) => {
    //     res = res.data;
    //     if (res.code === 200 && res.data.Success) {
    //       this.list = res.data.Datas;
    //       this.page.total = res.data.TotalRows;

    //       this.initData();
    //     }
    //   });
    // },
    vaildData(id) {
      const list = [];
      for (var i = 0; i < 20; i++) {
        list.push(i);
      }
      return list.includes(id);
    },
    //获得模板数据
    getCategory() {
      const params = {
        queryJson: "",
      };
      getCategory(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.dataTypeList = res.data.Datas;
          this.optionList = this.TreeToArray(res.data.Datas);
         // console.log(this.optionList);

          //this.option.column[0].dicData = res.data.Datas || [];
        }
        this.getList();
      });
    },

    // handleExport(item) {
    //   getObj(item.id).then((res) => {
    //     // this.$message.success("大屏导出成功");
    //     this.$message.success("大屏分享成功");
    //     const data = res.data.data;
    //     let mode = {
    //       detail: JSON.parse(data.config.detail),
    //       component: JSON.parse(data.config.component),
    //     };
    //     var zip = new window.JSZip();
    //     zip.file("view.js", `const option =${JSON.stringify(mode, null, 4)}`);
    //     zip.file(
    //       "说明.txt",
    //       `把view.js替换index.zip中文件即可,运行index.html即可看到效果`
    //     );
    //     zip.generateAsync({ type: "base64" }).then((content) => {
    //       this.downFile("data:application/zip;base64," + content, "view.zip");
    //       // setTimeout(() => {
    //       //   location.href = "/index.zip";
    //       // }, 1000);
    //     });
    //   });
    // },
    handleCopy(item) {
      this.title = "复制模板";
      this.copymoban = item;
      this.ruleForm = item.Kanban;
     // console.log(this.ruleForm, "+++");
      this.box = true;
      // if (this.$website.isDemo) {
      //   this.$message.error(this.$website.isDemoTip)
      //   return
      // }handleEdit
      // this.$confirm("确认复制当前大屏", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      //   .then(() => {
      //       kanbanTemplateCopy({id:item.Kanban.CID}).then((res) => {
      //       res = res.data;
      //       this.$message.success("复制成功");
      //       const id = res.data.data;
      //       this.handleEdit(item);
      //     });
      //   })
      //   .catch(() => {});
    },
    //删除
    handleDel(item, index) {
      this.$confirm(`${this.$t('message.PermanentDeletion')}`, `${this.$t('message.Prompt')}`, {
        confirmButtonText: `${this.$t('message.confirmButtonText')}`,
        cancelButtonText: `${this.$t('message.cancelButtonText')}`,
        iconClass: "el-icon-error",
      })
        .then(() => {
          kanbanTemplateDeleteByID({ CID: item.Kanban.CID }).then((res) => {
            res = res.data;
            if (res.code === 200 || res.data.Success) {
              this.$message.success(`${this.$t('message.DeletedSuccessfully')}`);//"删除成功"
              this.page.page = 1;
              this.getList();
            } else {
              this.$message.error(res.message.msg);
            }
          });
        })
        .catch(() => {});
    },
    //新建大屏
    handleAdd() {
      //   this.$router.push({
      //     path: "/create",
      //     query: {
      //       category: this.activeName,
      //     },
      //   });
      this.title = "新建模板";
    //  console.log(this.ruleForm);
      this.ruleForm.CWIDTH = "1920";
      this.ruleForm.CHEIGHT = "1080";
      this.ruleForm.CKANBAN_LABEL_ID = this.allabel;
      this.box = true;
    },
    //分享
    // handleUpdate(item, index) {
    //   let routeUrl = this.$router.resolve({
    //     path: "/build/" + item.Kanban.CID,
    //   });
    //   this.ruleForm = item.Kanban;
    //   this.ruleForm.link = window.__POWERED_BY_QIANKUN__
    //     ? window.location.origin + `/subapp${routeUrl.href}`
    //     : window.location.origin + routeUrl.href;
    // this.box = true;
    // },
    //大屏设计
    handleEdit(item) {
      let routeUrl = this.$router.resolve({
        path: "/build/" + item.Kanban.CID,
      });
      window.__POWERED_BY_QIANKUN__
        ? window.open(`/subapp${routeUrl.href}`, "_blank")
        : window.open(routeUrl.href, "_blank");
      //  window.open('http://192.168.1.29:10006/build/'+item.Kanban.CID, "_blank");
    },
    handleViews(item) {
      let routeUrl = this.$router.resolve({
        path: "/view/" + item.Kanban.CID,
      });
      window.__POWERED_BY_QIANKUN__
        ? window.open(`/subapp${routeUrl.href}`, "_blank")
        : window.open(routeUrl.href, "_blank");
      // window.open(routeUrl.href, "_blank");
    },
    handleSave(form, done) {
      updateObj({
        id: this.form.id,
        category: this.form.category,
        password: this.form.password,
        status: this.form.status,
        title: this.form.title,
      }).then(() => {
        done();
        this.$message.success(`${this.$t('message.ModifiedSuccessfully')}`);//"修改成功"
        this.getList();
      });
    },

    //请求大屏列表数据
    getList(value) {
      const params = {
        queryJson: {
          labelId: this.allabel,
          keyword: value ? value : "",
        },
        start: this.page.page,
        length: this.page.size,
      };
      this.list = [];
      TemplateGetPageList(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.list = res.data.Datas;
          this.page.total = res.data.TotalRows;
          this.initData();
          this.loading=false
        } else {
          this.$message.error(res.message.msg||res.data.Content);
          this.loading=false
        }
      });
    },
    initData() {
      this.list.forEach((ele, index) => {
        this.$set(this.list[index], "_menu", false);
      });
    },
  },
};
</script>
<style lang="scss">
.demo-ruleForm1 {
  .el-form-item__error {
    line-height: 5px;
  }
}
.borderColor {
  border: 1px solid #409eff !important;
  color: #409eff;
}
@import "@/styles/list.scss";

.content_aside {
  margin: 10px;

  background: #fff;
  height: calc(100vh - 174px);
}

.el-tree-node__label {
  height: 20px !important;
  line-height: 20px !important;
  width: 200px;
}

.page_vxes {
  position: absolute;
  bottom: 12px;
  right: 10px;
  width: 86.4%;
  padding-right: 10px;
  background: #fff;
  .vxe-pager--total {
    margin-top: -4px;
  }
}

.moban {
  line-height: 48px;
  height: 48px;
  padding-left: 10px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 2px solid #f1f1f1;
}

.collapse-transition {
  transition: none; //权重稍微高一点覆盖掉组件本身的动画效果
}

.content__btns {
  display: inline-block;
  text-align: center;
  margin: 0 2px;

  i {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;

    border-radius: 50%;
    background: #419efb;
  }

  span {
    color: #fff;
    display: inline-block;
    margin-top: 10px;
    font-size: 12px;
  }
}
</style>
