<template>
  <div class="build">
    <contentmenu ref="contentmenu"></contentmenu>
    <imglist ref="imglist" @change="handleSetimg"></imglist>
    <headers :transfer="transfer" ref="headers"></headers>

    <div class="app" :class="{ 'app--none': !menuFlag }">
      <div class="menu" v-show="menuFlag && menuShow" @click.self="handleInitActive">
        <el-radio-group v-model="tabPosition" style="
            width: 200px;
            margin-bottom: 30px;
            z-index: 10;
            position: absolute;
          ">
          <el-radio-button style="width: 49.1%" label="left">
            <!-- 组件 -->
            {{$t('page.build.Component')}}
          </el-radio-button>
          <el-radio-button style="width: 49.1%" label="right">
            <!-- 图层 -->
            {{$t('page.build.Layer')}}
          </el-radio-button>
        </el-radio-group>

        <top v-if="tabPosition == 'left'" ref="top" style="margin-top: 40px"></top>
        <layer v-else ref="layer" style="padding: 0 5px; margin-top: 43px" :nav="nav"></layer>
      </div>
      <!-- 中间区域 -->
      <div ref="section" @contextmenu.prevent="() => { }" class="section">
        <div class="refer-line-img" @click="imgClick">
          <img :src="isShowReferLine ? imgOpenData : imgClose" />
        </div>
        <sketch-rule :thick="thick" :scale="scale" :width="width" :height="height" :startX="startX" :startY="startY"
          :isShowReferLine="isShowReferLine" :palette="palette" :shadow="shadow" :horLineArr="lines.h"
          :verLineArr="lines.v" />
        <div ref="screensRef" id="screens" @mousedown="handleInitActive" @wheel="handleWheel" @scroll="handleScroll">
          <div ref="containerRef" class="screen-container">
            <div class="canvas" ref="canvas" :style="canvasStyle">
              <container ref="container" :wscale="scale"></container>
            </div>
          </div>
        </div>
      </div>

      <div class="menu params" v-show="menuFlag && paramsShow">
        <div v-if="isActive && !isSelectActive" style="
            text-align: left;
            color: #fff;
            height: 40px;
            line-height: 40px;
            background: #000;
          ">
          <!-- <i class="el-icon-setting"></i> -->
          <!-- 组件配置 -->
          {{$t('page.build.ComponentConfiguration')}}
        </div>

        <el-tabs class="menu__tabs" @tab-click="handleTabClick" stretch v-model="menuTabs">
          <template>
            <!-- 数据配置 -->
            <el-tab-pane name="1" v-if="validProp('dataList')">
              <span slot="label"><i class="el-icon-document-copy"></i>
                <!-- 数据 -->
                {{$t('page.build.Data')}}
              </span>
              <template v-if="menuTabs == 1">
                <el-form label-width="150px" label-position="left" size="small">
                  <el-form-item label1="数据类型" :label="`${$t('page.build.DataType')}`">
                    <avue-select v-model="activeObj.dataType" :dic="dicOption.dataType"
                      @change="dataTypeChange"></avue-select>
                  </el-form-item>
                  <template v-if="isSql">
                    <el-form-item label1="数据源选择" :label="`${$t('page.build.DataSourceSelection')}`">
                      <avue-select filterable :dic="DIC.sql" v-model="db"></avue-select>
                    </el-form-item>
                    <el-form-item label="SQL语句" label-position="top">
                      <monaco-editor v-model="sql" language="sql" height="100"></monaco-editor>
                    </el-form-item>
                  </template>
                  <template v-else-if="isApi">
                    <el-form-item label-width="150px" label1="接口地址" :label="`${$t('page.build.InterfaceAddress')}`">
                      <avue-input v-model="activeObj.url"></avue-input>
                    </el-form-item>
                    <el-form-item label-width="150px" label1="请求方式" :label="`${$t('page.build.RequestMethod')}`">
                      <avue-select v-model="activeObj.dataMethod" :dic="dicOption.dataMethod"></avue-select>
                    </el-form-item>
                  </template>
                  <template v-else-if="isWs">
                    <el-form-item label="WS地址">
                      <el-input v-model="activeObj.wsUrl"> </el-input>
                    </el-form-item>
                  </template>
                  <template v-else-if="isRecord">
                    <el-form-item label1="数据集选择" :label="`${$t('page.build.DatasetSelection')}`">
                      <avue-select filterable :dic="DIC.data" v-model="activeObj.record"></avue-select>
                    </el-form-item>
                  </template>
                  <template v-else-if="isCustom">
                    <el-form-item label1="数据源选择" :label="`${$t('page.build.DataSourceSelection')}`">
                      <avue-select filterable @change="datasoureChange" :dic="DIC.sql"
                        v-model="activeObj.dataSource"></avue-select>
                    </el-form-item>
                    <el-form-item label1="数据集选择" :label="`${$t('page.build.DatasetSelection')}`">
                      <avue-select filterable :dic="DIC.data" v-model="activeObj.dataSet" @change="datasetChange"></avue-select>

                    </el-form-item>
                    <el-form-item label1="刷新时间(ms)" :label="`${$t('page.build.RefreshTime')}`" v-if="isApi || isWs || isSql || isCustom">
                      <avue-input-number @change="timeChange($event, activeObj)" v-model="activeObj.time"
                        placeholder="0"></avue-input-number>
                    </el-form-item>
                    <el-form-item label="参数配置" :label="`${$t('page.build.ParameterConfiguration')}`">
                      <el-button size="small" type="primary" @click="paramsSetting">
                        <!-- 参数配置 -->
                        {{$t('page.build.ParameterConfiguration')}}
                      </el-button>
                    </el-form-item>
                    <!-- <el-form-item label="数据模型">
                      <avue-select
                        :dic="DIC.model"
                        v-model="dataModel"
                      ></avue-select>
                    </el-form-item>

                    <el-form-item label="渲染参数">
                      <avue-select
                        :dic="DIC.params"
                        v-model="renderParams"
                      ></avue-select>
                    </el-form-item> -->
                  </template>
                  <template v-else-if="isGlobalDataSource">

                    <el-form-item label1="全局数据集" :label="`${$t('page.build.GlobalDataset')}`">
                      <!-- :disabled="isGlobalDataSource" -->
                      <avue-select :dic="DIC.globalDataSet" v-model="activeObj.dataSet"
                        @change="datasetChange_global"></avue-select>
                    </el-form-item>

                    <el-form-item label1="参数配置" :label="`${$t('page.build.ParameterConfiguration')}`">
                      <el-button size="small" type="primary" @click="paramsSetting">
                        <!-- 参数配置 -->
                        {{$t('page.build.ParameterConfiguration')}}
                      </el-button>
                    </el-form-item>
                  </template>
                  <el-form-item label1="刷新时间" :label="`${$t('page.build.RefreshTime')}`" v-if="isApi || isWs || isSql">
                    <avue-input-number v-model="activeObj.time" placeholder="0"></avue-input-number>
                  </el-form-item>
                  <el-form-item label-width="0">
                    <el-button size="small" type="primary" class="block" @click="handleSetting">
                      <!-- 更多设置 --> 
                      {{$t('page.build.MoreSettings')}}
                    </el-button>
                  </el-form-item>
                  <!-- <el-form-item label-width="0">
                    <el-button
                      size="small"
                      type="danger"
                      class="block"
                      @click="openCode('stylesFormatter', '编辑样式')"
                      >编辑样式</el-button
                    >
                  </el-form-item> -->
                  <el-form-item label-width="0">
                    <el-button :loading="isTestLoading" size="small" type="primary" class="block" @click="handleRes">
                      <!-- 请求数据3 首页-->
                      {{$t('page.build.requestData')}}
                    </el-button>
                  </el-form-item>
                  <el-form-item label-width="0">
                    <monaco-editor v-model="dataRes" disabled height="400"></monaco-editor>
                  </el-form-item>
                </el-form>
              </template>
            </el-tab-pane>

            <el-tab-pane name="0">
              <span v-if="isSelectActive || activeIndex" slot="label" style="text-align: left">
                <i class="el-icon-setting"></i>
                <!-- 基础 -->
                {{$t('page.build.basis')}}
              </span>
              <span v-else slot="label" style="text-align: left">
                <!-- 大屏配置 -->
                {{$t('page.build.LargeScreenConfiguration')}}
              </span>

              <el-form label-width="150px" label-position="left" size="small">
                <!-- 多选配置选项 -->
                <template v-if="isSelectActive">
                  <el-form-item label="水平方式">
                    <el-tooltip content="左对齐" placement="top">
                      <i class="el-icon-s-fold icon" @click="$refs.container.handlePostionSelect('left')"></i>
                    </el-tooltip>
                    <el-tooltip content="居中对齐" placement="top">
                      <i class="el-icon-s-operation icon" @click="$refs.container.handlePostionSelect('center')"></i>
                    </el-tooltip>
                    <el-tooltip content="右对齐" placement="top">
                      <i class="el-icon-s-unfold icon" @click="$refs.container.handlePostionSelect('right')"></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label="垂直方式">
                    <el-tooltip content="顶对齐" placement="top">
                      <i class="el-icon-s-fold icon" @click="$refs.container.handlePostionSelect('top')"></i>
                    </el-tooltip>
                    <el-tooltip content="中部对齐" placement="top">
                      <i class="el-icon-s-operation icon" @click="$refs.container.handlePostionSelect('middle')"></i>
                    </el-tooltip>
                    <el-tooltip content="底对齐" placement="top">
                      <i class="el-icon-s-unfold icon" @click="$refs.container.handlePostionSelect('bottom')"></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label-width="0">
                    <el-button type="primary" size="small" class="block" @click="handleFolder">
                      <!-- 分组 -->
                      {{$t('page.build.Grouping')}}
                    </el-button>
                  </el-form-item>
                  <el-form-item label-width="0">
                    <el-button type="danger" size="small" class="block" @click="handleDeleteSelect">
                      <!-- 删除 -->
                      {{$t('page.build.Delete')}}
                    </el-button>
                  </el-form-item>
                </template>
                <!-- 组件配置 -->
                <template v-else-if="activeIndex">
                  <p class="title">{{ $t(activeObj.title) }}</p>
               <!-- {{ activeObj }} -->
                  <el-form-item label1="X轴模型(文)" :label="`${$t('page.build.XAxisModel')}`" v-if="isCustom &&  !['翻牌器','仪表盘'].includes(activeObj.title)">
                    <avue-select  @blur="handleRes(false)" :dic="DIC.model" v-model="activeObj.dataModelX"></avue-select>
                  </el-form-item>
                
                  <el-form-item label1="Y轴模型(值)" :label="`${$t('page.build.YAxisModel')}`" v-if="isCustom">
                    <avue-select @blur="handleRes(false)" :dic="DIC.model" v-model="activeObj.dataModelY"></avue-select>
                  </el-form-item>

                  <el-form-item label1="多Y轴模型(值)" :label="`${$t('page.build.MultiYAxisModel')}`" v-if="isCustom &&  ['bar','line','common'].includes(activeObj.component.name)">
                    <avue-select multiple  :dic="DIC.model" v-model="activeObj.dataMulModelY"></avue-select>
                  </el-form-item>
                  <el-form-item label1="图层名称" :label="`${$t('page.build.LayerName')}`">
                    <avue-input disabled v-model="activeObj.name"></avue-input>
                  </el-form-item>
                  <template v-if="!isFolder">
                    <el-form-item label1="隐藏" :label="`${$t('page.build.Hide')}`">
                      <avue-switch v-model="activeObj.display"></avue-switch>
                    </el-form-item>
                    <el-form-item label1="锁定" :label="`${$t('page.build.Lock')}`">
                      <avue-switch v-model="activeObj.lock"></avue-switch>
                    </el-form-item>
                  </template>
                  <template v-if="validProp('colorList')">
                    <el-form-item label1="系统配色" :label="`${$t('page.build.SystemColor')}`">
                      <avue-switch v-model="activeOption.switchTheme"></avue-switch>
                    </el-form-item>
                  </template>
                  <component :is="activeComponent.prop + 'Option'"></component>
                  <main-option></main-option>
                </template>
                <!-- 主屏的配置项 -->
                <template v-else>
                  <el-form-item label1="大屏名称" :label="`${$t('page.build.LargeScreenName')}`">
                    <avue-input v-model="config.name"></avue-input>
                  </el-form-item>
                  <el-form-item label1="大屏宽度" :label="`${$t('page.build.LargeScreenWidth')}`">
                    <el-input-number style="width: 100%" v-model="config.width"></el-input-number>
                  </el-form-item>
                  <el-form-item label1="大屏高度" :label="`${$t('page.build.LargeScreenHeight')}`">
                    <el-input-number style="width: 100%" v-model="config.height"></el-input-number>
                  </el-form-item>
                  <el-form-item label1="大屏简介" :label="`${$t('page.build.LargeScreenIntroduction')}`">
                    <avue-input placeholder1="请输入大屏简介" :placeholder="`${$t('page.build.LargeScreenIntroduction')}`" v-model="config.info" type="textarea" :min-rows="5"></avue-input>
                  </el-form-item>
                  <el-form-item label1="背景颜色" :label="`${$t('page.build.BackgroundColor')}`">
                    <avue-input-color v-model="config.backgroundColor"></avue-input-color>
                  </el-form-item>
                  <el-form-item label1="背景图片" :label="`${$t('page.build.BackgroundPicture')}`">
                    <img :src="config.backgroundImage" @click="
      handleOpenImg('config.backgroundImage', 'background')
      " alt="" width="100%" />
                    <el-input clearable v-model="config.backgroundImage">
                      <div @click="
      handleOpenImg('config.backgroundImage', 'background')
      " slot="append">
                        <i class="iconfont icon-img"></i>
                      </div>
                    </el-input>
                  </el-form-item>
                  <globalDataSource @change="change_global" :config="config"></globalDataSource>

                  <el-form-item label1="刷新页时间" :label="`${$t('page.build.RefreshPageTime')}`">
                     <el-time-select
                        v-model="config.refreshPageDateTime"
                        :picker-options="{
                          start: '08:30',
                          step: '00:15',
                          end: '18:30'
                        }"
                       
                        >
                      </el-time-select>
                  </el-form-item>
                  <!-- <el-form-item v-show="config.refreshType=='2'" label="刷新时间">
                    <avue-select v-model="config.refreshPageHours"
                                :dic="dicRefreshDateList">
                    </avue-select>
                  </el-form-item> -->
                  <el-form-item label1="预览方式" :label="`${$t('page.build.PreviewMode')}`">
                    <el-radio-group v-model="config.screen">
                      <el-radio label="x">
                        <!-- X轴铺满，Y轴自适应滚动 -->
                        {{$t('page.build.XAxisFilling')}}
                      </el-radio>
                      <el-radio label="y">
                        <!-- Y轴铺满，X轴自适应滚动 -->
                        {{$t('page.build.YAxisFilling')}}
                      </el-radio>
                      <el-radio label="xy">
                        <!-- 强行拉伸画面，填充所有视图 -->
                        {{$t('page.build.ForceStretchingThePicture')}}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label-width="0">
                    <el-button size="small" type="primary" class="block" @click="globShow = true">
                      <!-- 更多设置 -->
                      {{$t('page.build.MoreSettings')}}
                    </el-button>
                  </el-form-item>
                </template>
              </el-form>
            </el-tab-pane>

            <!-- 交互事件配置 -->
            <el-tab-pane name="2" v-if="validProp('eventList')">
              <span slot="label"><i class="el-icon-thumb"></i>
                <!-- 交互 -->
                {{$t('page.build.Interaction')}}
              </span>
              <el-form label-width="150px" label-position="left" size="small">
                <el-form-item label1="子类" :label="`${$t('page.build.Subclass')}`">
                  <avue-select multiple v-model="activeObj.child.index" :dic="childList" :props="childProps">
                  </avue-select>
                </el-form-item>
                <el-form-item label1="参数名称" :label="`${$t('page.build.ParameterName')}`">
                  <avue-input v-model="activeObj.child.paramName"></avue-input>
                </el-form-item>
                <el-form-item label1="映射字段" :label="`${$t('page.build.MappingFields')}`">
                  <avue-input v-model="activeObj.child.paramValue"  :placeholder="`${$t('page.build.Defaultvalue')}`"></avue-input>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <!-- 其他事件配置 -->
            <el-tab-pane name="3" v-if="!isFolder && isActive && !isSelectActive">
              <span slot="label"><i class="el-icon-mouse"></i>
                <!-- 事件 -->
                {{$t('page.build.Event')}}
              </span>
              <el-form label-width="150px" label-position="left" size="small">
                <el-form-item label="点击事件" :label="`${$t('page.build.ClickEvent')}`">
                  <el-button size="small" type="primary" @click="openCode('clickFormatter', `${$t('page.build.ClickEvent')}`)">
                    <!-- 编辑 -->
                    {{$t('page.build.Edit')}}
                  </el-button>
                </el-form-item>
                <template v-if="validProp('labelFormatterList')">
                  <el-form-item label="提示事件" :label="`${$t('page.build.PromptEvent')}`">
                    <el-button size="small" type="primaractiveIndexy"
                      @click="openCode('formatter', `${$t('page.build.PromptEvent')}`)">
                      <!-- 编辑 -->
                      {{$t('page.build.Edit')}}
                    </el-button>
                  </el-form-item>
                  <el-form-item label="标题事件" :label="`${$t('page.build.TitleEvent')}`">
                    <el-button size="small" type="primary" @click="openCode('labelFormatter', `${$t('page.build.TitleEvent')}`)">
                      <!-- 编辑 -->
                      {{$t('page.build.Edit')}}
                    </el-button>
                  </el-form-item>
                </template>
              </el-form>
            </el-tab-pane>
            <!-- 基本参数配置 -->
            <el-tab-pane name="4" v-if="isActive && !isSelectActive">
              <span slot="label"><i class="el-icon-folder"></i>
                <!-- 参数 -->
                {{$t('page.build.Parameter')}}
              </span>
              <el-form label-width="120px" label-position="left" v-if="menuTabs == 4" size="small">
                <el-form-item label1="序号" :label="`${$t('page.build.SerialNumber')}`">
                  <avue-input @click.native="handleCopyIndex" v-model="activeIndex" disabled>
                  </avue-input>
                </el-form-item>
                <el-form-item label1="配置" :label="`${$t('page.build.Configuration')}`">
                  <monaco-editor v-model="activeObj" disabled language="javascript"  height="100"></monaco-editor>
                </el-form-item>
                <template v-if="!isFolder">
                  <el-form-item label1="进入动画" :label="`${$t('page.build.EntryAnimation')}`">
                    <avue-select filterable allow-create v-model="activeComponent.animated"
                      :dic="dicOption.animated"></avue-select>
                    <div>
                      <a href="https://www.dowebok.com/demo/2014/98/" target="_blank">
                        <!-- 点击查看动画类型 -->
                        {{$t('page.build.ClickToViewAnimationType')}}
                      </a>
                    </div>
                  </el-form-item>
                  <el-form-item label1="位置" :label="`${$t('page.build.Position')}`">
                    <div class="flex">
                      <avue-input-number style="width: 130px" v-model="activeObj.left"></avue-input-number>
                      &nbsp;x&nbsp;
                      <avue-input-number style="width: 130px" v-model="activeObj.top"></avue-input-number>
                    </div>
                  </el-form-item>
                  <el-form-item label1="尺寸" :label="`${$t('page.build.Size')}`">
                    <div class="flex">
                      <avue-input-number style="width: 130px" v-model="activeComponent.width"></avue-input-number>
                      &nbsp;x&nbsp;
                      <avue-input-number style="width: 130px" v-model="activeComponent.height"></avue-input-number>
                    </div>
                  </el-form-item>
                  <el-form-item label1="字体" :label="`${$t('page.build.Font')}`">
                    <avue-input v-model="activeComponent.fontFamily" placeholder="输入字体库的url">
                    </avue-input>
                  </el-form-item>
                  <el-form-item label-width="120px" label1="透视" :label="`${$t('page.build.Perspective')}`">
                    <avue-slider v-model="activeComponent.perspective" :max="1000"></avue-slider>
                  </el-form-item>
                  <el-form-item label-width="120px" label1="缩放" :label="`${$t('page.build.Scale')}`">
                    <avue-slider v-model="activeComponent.scale" :max="10"></avue-slider>
                  </el-form-item>
                  <el-form-item label-width="120px" label1="透明度" :label="`${$t('page.build.Opacity')}`">
                    <avue-slider :step="0.1" v-model="activeComponent.opacity" :max="1"></avue-slider>
                  </el-form-item>
                  <el-form-item label-width="120px" label1="X旋转度" :label="`${$t('page.build.XRotationDegree')}`">
                    <avue-slider v-model="activeComponent.rotateX" :min="-360" :max="360"></avue-slider>
                  </el-form-item>
                  <el-form-item label-width="120px" label1="Y旋转度" :label="`${$t('page.build.YRotationDegree')}`">
                    <avue-slider v-model="activeComponent.rotateY" :min="-360" :max="360"></avue-slider>
                  </el-form-item>
                  <el-form-item label-width="120px" label1="Z旋转度" :label="`${$t('page.build.ZRotationDegree')}`">
                    <avue-slider v-model="activeComponent.rotateZ" :min="-360" :max="360"></avue-slider>
                  </el-form-item>
                </template>
              </el-form>
            </el-tab-pane>
          </template>
        </el-tabs>
      </div>
    </div>
    <codeedit @submit="closeCode" v-if="code.box" :title="code.title" :type="code.type" v-model="code.obj"
      :visible.sync="code.box"></codeedit>
    <el-dialog append-to-body top="1%" 
      title1="更多设置" :title="`${$t('page.build.MoreSettings')}`"
      :close-on-click-modal="false" :visible.sync="globShow" width="70%">
      <el-form size="small" label-width="220px">
        <el-form-item label="全局请求地址" :label="`${$t('page.build.GlobalRequestAddress')}`">
          <avue-input v-model="config.url" placeholder="/"></avue-input>
        </el-form-item>
        <el-form-item label="全局请求参数" :label="`${$t('page.build.GlobalRequestParameters')}`">
          <monaco-editor v-model="config.query" language="javascript" disabled height="100"></monaco-editor>
          <el-button type="primary" icon="el-icon-edit" @click="openCode('query', `${$t('page.build.GlobalRequestParameters')}`)">
            <!-- 编辑 -->
            {{$t('page.build.Edit')}}
          </el-button>
        </el-form-item>
        <el-form-item label="全局请求头" :label="`${$t('page.build.GlobalRequestHeader')}`">
          <monaco-editor v-model="config.header" language="javascript" disabled height="100"></monaco-editor>
          <el-button type="primary" icon="el-icon-edit" @click="openCode('header', `${$t('page.build.GlobalRequestHeader')}`)">
            <!-- 编辑 -->
            {{$t('page.build.Edit')}}
          </el-button>
        </el-form-item>
        <el-form-item label1="大屏水印" :label="`${$t('page.build.LargeScreenWatermark')}`">
          <avue-switch v-model="config.mark.show"></avue-switch>
        </el-form-item>
        <template v-if="config.mark.show">
          <el-form-item label1="内容" :label="`${$t('page.build.Content')}`">
            <avue-input v-model="config.mark.text"></avue-input>
          </el-form-item>
          <el-form-item label1="大小" :label="`${$t('page.build.Size')}`">
            <avue-input-number v-model="config.mark.fontSize"></avue-input-number>
          </el-form-item>
          <el-form-item label1="颜色" :label="`${$t('page.build.Color')}`">
            <avue-input-color v-model="config.mark.color"></avue-input-color>
          </el-form-item>
          <el-form-item label1="角度" :label="`${$t('page.build.Angle')}`">
            <avue-input-number v-model="config.mark.degree"></avue-input-number>
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
    <el-dialog append-to-body top="1%" title1="更多设置" :title="`${$t('page.build.MoreSettings')}`" :close-on-click-modal="false" :visible.sync="show" width="70%">
      <el-form size="small" label-width="150px">
        <el-form-item label1="数据类型" :label="`${$t('page.build.DataType')}`">
          <avue-select v-model="activeObj.dataType" :dic="dicOption.dataType" @change="dataTypeChange"></avue-select>
        </el-form-item>
        <template v-if="isStatic">
          <el-form-item label1="数据值" :label="`${$t('page.build.DataValue')}`" label-position="top">
            <el-button size="small" type="primary" icon="el-icon-edit"
              @click="openCode('data', `${$t('page.build.DataValue')}`)">
              <!-- 编辑数据值 -->
              {{$t('page.build.EditDataValue')}}
            </el-button>
            &nbsp;
            <el-upload :show-file-list="false" :auto-upload="false" style="display: inline-block" accept=".xls,.xlsx"
              :on-change="handleImport">
              <el-button size="small" icon="el-icon-upload" type="success">
                <!-- 导入数据(Excel) -->
                {{$t('page.build.ImportData')}}
              </el-button>
            </el-upload>
          </el-form-item>
        </template>
        <template v-else-if="isSql">
          <el-form-item label1="数据源选择" :label="`${$t('page.build.DataSourceSelection')}`">
            <avue-select filterable :dic="DIC.sql" v-model="db"></avue-select>
          </el-form-item>

          <el-form-item label="SQL语句" label-position="top">
            <el-tooltip effect="dark" content="如果要获取变量，直接写成函数返回sql语句即可" placement="top">
              <i class="el-icon-info"></i>
            </el-tooltip>
            <monaco-editor v-model="sql" language="sql" height="100"></monaco-editor>
          </el-form-item>
        </template>
        <template v-else-if="isApi">
          <el-form-item label-width="150px" label1="接口地址" :label="`${$t('page.build.InterfaceAddress')}`">
            <avue-input v-model="activeObj.url"></avue-input>
          </el-form-item>
          <el-form-item label-width="150px" label1="请求方式" :label="`${$t('page.build.RequestMethod')}`">
            <avue-select v-model="activeObj.dataMethod" :dic="dicOption.dataMethod"></avue-select>
          </el-form-item>
        </template>
        <template v-else-if="isWs">
          <el-form-item label="WS地址">
            <el-input v-model="activeObj.wsUrl"> </el-input>
          </el-form-item>
        </template>
        <template v-else-if="isRecord">
          <el-form-item label1="数据集选择" :label="`${$t('page.build.DatasetSelection')}`">
            <avue-select filterable :dic="DIC.data" v-model="activeObj.record"></avue-select>

          </el-form-item>
        </template>
        <el-form-item label-width="150px" label="请求配置" :label="`${$t('page.build.RequestConfiguration')}`" v-if="isWs || isApi">
          <el-tabs class="menu__tabs" v-model="dataTabs">
            <el-tab-pane label1="请求参数（Body）" :label="`${$t('page.build.RequestParameters_BODY')}`" name="0">
              <template v-if="dataTabs == 0">
                <!-- <el-radio-group
                  v-if="['post', 'put'].includes(activeObj.dataMethod)"
                  v-model="activeObj.dataQueryType"
                >
                  <el-radio label="json">JSON数据</el-radio>
                  <el-radio label="form">FORM表单</el-radio>
                </el-radio-group> -->
                <monaco-editor v-model="activeObj.dataQuery" language="javascript" disabled
                  height="100"></monaco-editor>
                <el-button size="small" type="primary" icon="el-icon-edit"
                  @click="openCode('dataQuery', `${$t('page.build.RequestParameters')}`)">
                  <!-- 编辑 -->
                  {{$t('page.build.Edit')}}
                </el-button>
              </template>
            </el-tab-pane>
            <el-tab-pane  label1="请求头（Headers）" :label="`${$t('page.build.RequestHeader')}`" name="1" v-if="isApi">
              <template v-if="dataTabs == 1">
                <monaco-editor v-model="activeObj.dataHeader" language="javascript" disabled
                  height="100"></monaco-editor>
                <el-button size="small" type="primary" icon="el-icon-edit"
                  @click="openCode('dataHeader', `${$t('page.build.RequestHeader')}`)">
                  <!-- 编辑 -->
                  {{$t('page.build.Edit')}}
                </el-button>
              </template>
            </el-tab-pane>
          </el-tabs>
        </el-form-item>
        <template v-else-if="isCustom">
          <el-form-item label1="数据源选择" :label="`${$t('page.build.DataSourceSelection')}`">
            <avue-select filterable @change="datasoureChange" :dic="DIC.sql" v-model="activeObj.dataSource"></avue-select>
          </el-form-item>
          <el-form-item label1="数据集选择" :label="`${$t('page.build.DatasetSelection')}`">
            <avue-select filterable :dic="DIC.data" v-model="activeObj.dataSet" @change="datasetChange"></avue-select>
           &nbsp; &nbsp;<el-button :disabled="!activeObj.dataSet" type='text' @click="editDatasource(activeObj.dataSet,activeObj.dataSource)">
            <!-- 编辑 -->
            {{$t('page.build.Edit')}}
          </el-button>
          </el-form-item>
          <el-form-item :label="index == 0 ? 'Headers' : ''" :key="index" v-for="(ele, index) in APIForm.domains">
            <el-input v-model="ele.key" style="width: 48%"></el-input>&nbsp;&nbsp;
            <el-input v-model="ele.value" style="width: 48%"></el-input>
          </el-form-item>
          <el-form-item :label="index == 0 ? 'Paramters' : ''" :key="index" v-for="(item, index) in APIForm.Paramters">
            <el-input v-model="item.key" style="width: 48%"></el-input>&nbsp;&nbsp;
            <el-input @change="changeParamtersValue(item, index)" v-model="item.value" style="width: 48%"></el-input>

          </el-form-item>
        </template>
        <template v-else-if="isGlobalDataSource">

          <el-form-item label2="全局数据集2" :label="`${$t('page.build.GlobalDataset')}`">
            <avue-select :dic="DIC.globalDataSet" v-model="activeObj.dataSet"
              @change="datasetChange_global"></avue-select>
          </el-form-item>
          <el-form-item :label="index == 0 ? 'Headers' : ''" :key="index" v-for="(ele, index) in APIForm.domains">
            <el-input v-model="ele.key" style="width: 48%"></el-input>&nbsp;&nbsp;
            <el-input v-model="ele.value" style="width: 48%"></el-input>
          </el-form-item>
          <el-form-item :label="index == 0 ? 'Paramters' : ''" :key="index" v-for="(item, index) in APIForm.Paramters">
            <el-input v-model="item.key" style="width: 48%"></el-input>&nbsp;&nbsp;
            <el-input @change="changeParamtersValue(item, index)" v-model="item.value" style="width: 48%"></el-input>
         
          </el-form-item>
        </template>
        <el-form-item label1="过滤器" :label="`${$t('page.build.Filter')}`">
          <monaco-editor v-model="activeObj.dataFormatter" language="javascript" disabled height="100"></monaco-editor>
          <el-button size="small" type="primary" icon="el-icon-edit"
            @click="openCode('dataFormatter', `${$t('page.build.EditFilter')}`)">
            <!-- 编辑 -->
            {{$t('page.build.Edit')}}
          </el-button>
        </el-form-item>
        <el-form-item label1="响应数据" :label="`${$t('page.build.ResponseData')}`">
          <monaco-editor v-model="dataRes" disabled height="200"></monaco-editor>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="show = false">
          <!-- 关闭 -->
          {{$t('page.build.Close')}}
        </el-button>
        <el-button :loading="isTestLoading" size="small" type="primary" @click="handleRes()">
          <!-- 过滤并请求 -->
          {{$t('page.build.FilterAndRequest')}}
        </el-button>
      
        <el-button
          :loading="isTestLoading"
          size="small"
          type="primary"
          icon="el-icon-refresh"
          @click="(isCustom || isGlobalDataSource) ? handleClick() : handleRes()"
          >
          <!-- 原始请求 -->
          {{$t('page.build.OriginalRequest')}}
        </el-button>
        
      </span>
    </el-dialog>

    <el-dialog append-to-body top="1%" title1="参数配置" :title="`${$t('page.build.ParameterConfiguration')}`" :close-on-click-modal="false" :visible.sync="showEdit" width="50%">
      <el-form size="small" label-width="150px" :model="APIForm">
        <el-form-item :label="index == 0 ? 'Headers' : ''" :key="index" v-for="(ele, index) in APIForm.domains">
          <el-input v-model="ele.key" style="width: 48%"></el-input>&nbsp;&nbsp;
          <el-input v-model="ele.value" style="width: 48%"></el-input>
        </el-form-item>
        <el-form-item :label="index == 0 ? 'Paramters' : ''" :key="index" v-for="(item, index) in APIForm.Paramters">
          <el-input v-model="item.key" style="width: 48%"></el-input>&nbsp;&nbsp;
          <el-input v-model="item.value" style="width: 48%"></el-input>
          <!-- <el-select
            v-model="item.value"
            style="width: 48%"
            filterable
            allow-create
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="响应数据" :label="`${$t('page.build.ResponseData')}`">
          <monaco-editor v-model="dataRes" disabled height="200"></monaco-editor>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="handleClick">
          <!-- 请求数据2 -->
          {{$t('page.build.requestData')}}
        </el-button>
      </span>
    </el-dialog>

    <el-dialog append-to-body top="0%" title1="数据源修改" :title="`${$t('page.build.DataSourceEdit')}`" :close-on-click-modal="false" :visible.sync="showEdit1" height="100%" width="100%">
    <div class="dialogH">

      <iframe
        :src="url"
        width="100%"
        height="100%"
        crossOrigin="anonymous"
        frameborder="0"
        sandbox="allow-same-origin allow-scripts allow-forms"
      >
      </iframe>

    </div>

    </el-dialog>




  </div>
</template>
<script>
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import globalDataSource from "@/page/components/globalDataSource";
import MonacoEditor from "@/page/components/editor";
import layer from "./group/layer";
import top from "./group/top";
import headers from "./group/header";
import imglist from "./group/imglist";
import contentmenu from "./group/contentmenu";
import codeedit from "./group/code";
import { dicOption } from "@/option/config";
import init from "@/mixins/";
import { createFile, uuid } from "@/utils/utils";
import components from "@/option/components";
import SketchRule from "vue-sketch-ruler";
// import { getList as getDbList } from "@/api/db";
// import { getList as getRecordList } from "@/api/record";
import { setLocalStorageStore, getLocalStorageStore } from '@/utils/setStore'
import {
  GetDataAll,
  GetSoureAll,
  GetModelAll,
  GetInputParameterByDataSetId,
  GetListByDataSetId,
} from "@/api/kanban";
import crypto from "@/utils/crypto";
export default {
  mixins: [init, components],
  data() {
    return {
      isTestLoading:false,
      useLanguageSettingFn: useLanguageSetting(this),
      dicRefreshType: [{
        label: '固定时间',
        value: 1
      }, {
        label: '指定小时',
        value: 2
      }],
      dicRefreshDateList: [],
      showEdit1:false,
      defaultModelX:null,
      SourceData: "",
      filterdDatasource: "",
      APIForm: {
        domains: [
          {
            value: "",
            key: "",
          },
        ],
        Paramters: [
          {
            value: "",
            key: "",
          },
        ],
      },
      //自定义专用

      title: "",
      showEdit: false,

      dataModel: "",
      renderParams: "",
      //自定义专用
      tabPosition: "left",
      currentHistoryIndex: -1,
      menuId: "avue-data-menu",
      menuShow: true,
      paramsShow: true,
      globShow: false,
      show: false,
      cacheList: {
        timer: null,
        nav: null,
        copy: null,
        history: [],
      },
      keys: {
        ctrl: false,
      },
      dataRes: "",
      db: "",
      sql: "",
      nav: [],
      DIC: {
        sql: [],
        data: [],
        model: [],
        params: [],
        globalDataSet: [],
      },
      loading: "",
      childProps: {
        label: "name",
        value: "index",
      },
      key: "",
      url:"",
      menuFlag: true,
      code: {
        title: "",
        box: false,
        type: "",
        obj: "",
      },
      form: {},
      dicOption: dicOption,
      menuTabs: 0,
      dataTabs: 0,
      // 标尺
      scale: 0.94, //初始化标尺的缩放
      startX: 0, //x轴标尺开始的坐标数值
      startY: 0,
      lines: {
        //初始化水平标尺上的参考线
        h: [],
        v: [],
      },
      shadow: { x: 0, y: 0 }, // 阴影大小
      thick: 20, //标尺的厚度
      width: 0, // 标尺宽,后面会初始化
      height: 0, // 标尺高,后面会初始化
      isShowReferLine: true, // 显示参考线
      imgOpenData:
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAbCAYAAAAOEM1uAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAQNSURBVHja7JdvSON1HMdfP126/shSmaZ1ntuZbTLihOnSdJlPhIquB0VR1DZM9En0wCB3qCXKVOh86mmakdGDOqyHityBidYN1HPYZqbhOZprMGTOUk/9/XryWyxvek5NIu4Lg/H+fPj8Xt/P98/n8xUkSeK/PIT7gP8GoCAI8cTQAoWAHkgFRCAA3AKmgeBRA8VkOSZgMvAy8DZQCqQf4OcFRoDPgYmzAnwdaAAuxpFlCbgGfAR4ThUwYhcE4QngExnw71FWVuax2WwBk8mkSE9PV+7t7Ymrq6vbw8PD0uDgYO7CwsK5KPc14ENJkj497FtxAwqCYAK+kvcbANXV1U6Hw6HIyMh4GlAckJHwzMzMrM1my3a5XNoo01XgPUmSdk8MCLwEfAmoAPLz872jo6OrOTk5xVGBQ0tLS575+fnt7OzsRIPBcD4pKelctL2/v3+mtrbWLIpigixfA94BNk8C+JoMlyRn7WZvb68mISEhI+IQCASmKyoq0jweT25EU6lU4aGhoZnKykpzdNzl5eWbRqPxyWAwmCZL3wJvAHeOA/iCPMsHARwOx7jdbi+JXs7t7e3lrKys1LW1NVWsJXa73ZN6vb40WltfX3cbDIZ0r9ebKUv9wLvxAhYC1+V7je7u7rG6urrn9vu1tbWNNzU1lR90KgsLC5emp6cfB5TRejgc9mg0msyoTNYDXfEAjgFmQGxpaZlobm6OBbFrMpl+dTqd+YdcLztbW1ve5ORk7X6D3++f0ul0+aFQKAXYAF6RJOn6UQGDQBrgE0VRJQjCw7EAjUbj8tTUVN4hgLubm5u3lUrlhVjG+vr6ya6ursgWaJck6fJRAW8AzwNia2vrRGNjY8xltNvtEx0dHc8eRKfX62+73W418NB+m8/nm9LpdE+Fw+FHgD+AS/Fk8CJwI7IHe3t7x2pqau7agxsbG/NqtTp3a2tLGQtwcnJyvKSk5K7JhcPhnzQaTXYwGEyVpQ+AK8c5xd9EZt/e3v59Q0ND6f5LeXFx8cfy8vILfr9fHdEUCsVOX1/fhMViqdgfOxQKzRUUFKh9Pt+JTnHk76vyPZgcqR49PT3nExMTM/+x0XZ3fePj47/Mzc2RlZVFVVVVRkpKin5/3JWVFWdRUVFeIBCInN7v5NJ55ySV5EUZ8lEArVb728jIiDcvL++ZONqo0MDAwK2ampoyURQTZXlI7ob+PI1aXAR8AegiutVqdXZ2dt6zFs/OzrosFstjLpcr+iR3A+9LkrRzKs2CrGUCV4C3on3NZrPHarX+Xlxc/MARu5nLkiRdPbVuJsa4BDQCxjj6QRH4GvgY+PksOmqF3FG/KVcc9T066s+AH86y5Y8eOXI282XQyJtkRv6d/pvk/rPz/wT41wBibRrpeMs+PAAAAABJRU5ErkJggg==", // 左上角图片
      imgClose:
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAPYSURBVHja7JVPTFRXFIe/+96gGRHEhQzBFMJKSKSkLTgtGxemSXcktohW2RBmTAjUClIYZ6Cxi+fQDgOCLJgJhqTSBAtLdjaxaSqQWnDSNp2U0I6FFh0SDdV0oMx7pwuZCUXjnyZNuvBsbnLvufe7555zfleJCP+1qReQfwVRSqWmqoFDIE3A+iZXQDbGlMmmNTatP5xPn/0ohOOgLgNtIB8DOlAKvAzsBTKBP4FF4Dvge1DrzwsBaAAuAJ8CxbpuezU/P397QcFLZGVlcf/+fRYXF1lc/G3VNJM/AJ8Dw8CdZ4QoQI4AIWBXaWkpQ0ND5v79+zW73Z5+n9XVVWZnb8rExIQ2MnKZWCz2M/Dhw1d4eiTngXafz2dmZ2ebPp9P6+vrl5qaI2p8fFyi0aheUlJiHj78tpaTs0sHJB6PW4HAJ3og0I2I+AHPkyAeu91uDA4OmrW1tRpAT09vsr29XXM4ciWZTJKXl2ctLCyoHTsy1ZUro+J0OjNSWR8ZGbFcLpeeSCTOAucfBykDpvr7+7c3NjamS+bevXvJ4uIS4vE7tra2tjW/32+7ffu21Ne7rJmZb7VIJKL27Nmjp0ADAwM0NjauAW+IyM2tkAGn8/WG69e/NjVNS20iHA6vd3Z2qrq6OtMwDFswGLROnz6dcffu3WRRUZEVCARwuVzbUv6WZVmVlZXa9PT0RRFp2gq56na7Dw0ODv6jGc6cOWNdu/Zl8saNb/RgMGi1tLSo7u5uaW5u1srLK8yDBw/aursD2ubmcbtPqnA4dFVE3twKuVBeXv7e5OSkabPZ0pGEQuG/WltbicV+0Xbv3m0LBnvWW1qatbNnvclLl4b0c+c+ErfblcoLa2trptPp1CORSEBEWrdCSoBpwzB2ejyedE6Wl5fNsrIyqaiokFAopBwOh/J4PKbf79/mcDiS0WiUnJwcWyoKr9erDMNYBg6ISOxx1dWg6/pAb2+vtZF8DWBqanr96NEalUgkpKCgQJaWljRN04jH43R1dcmpU6dsgBiGobxer3qoGnz2pD5pBfx1dXWaz+czi4qKNECtrKyYY2NjVjQa1fftK7aqq99hdHRUmpqaVEdHhzU/P58xPDycAN4Hws8iK28B/tzc3LJjx96lqqrKOnCggszMzLRmPHjwQM3MzFj19fX63NwcwDJQBUymK+ApEEDtBDkOnFBKvZafv9deWFhIdnYWKysr3Lr1K0tLvydE5CvgJ1AnQZqBi88DSV1aA0qAV4CCDRX+A1gAZoEfN/w/ALqAEyAjzwvZ8mc8KukblgGqD/gCZOyxkBd//P8G8vcAMK383pmr7aEAAAAASUVORK5CYII=",
    };
  },
  components: {
    globalDataSource,
    MonacoEditor,
    imglist,
    layer,
    codeedit,
    top,
    headers,
    contentmenu,
    SketchRule,
  },
  computed: {
    // 能否撤销
    canUndo() {
      return this.currentHistoryIndex > 0;
    },
    canRedo() {
      return this.cacheList.history.length > this.currentHistoryIndex + 1;
    },
    isKeysCtrl() {
      return this.keys.ctrl == true;
    },
    isStatic() {
      return this.activeObj.dataType == 0;
    },
    isApi() {
      return this.activeObj.dataType == 1;
    },
    isSql() {
      return this.activeObj.dataType == 2;
    },
    isWs() {
      return this.activeObj.dataType === 3;
    },
    isRecord() {
      return this.activeObj.dataType == 4;
    },
    isCustom() {
      return this.activeObj.dataType == 5;
    },
    isGlobalDataSource() {
      return this.activeObj.dataType == 6;
    },
    isFolder() {
      return this.activeObj.children;
    },
    isActive() {
      return this.activeIndex;
    },
    isSelectActive() {
      return this.active.length > 1;
    },
    childList() {
      return this.list.filter((ele) => {
        if (ele.children) return false;
        let component = ele.component || {};
        return !["tabs"].includes(component.prop);
      });
    },
    activeComponent() {
      return this.activeObj.component || {};
    },
    activeOption() {
      return this.activeObj.option || {};
    },
    activeObj() {
      let item = this.findList(this.activeIndex) || {};
      if (!item.child) item.child = {};
      return item;
    },
    activeList() {
      let result = [];
      this.active.forEach((ele) => {
        let item = this.findnav(ele);
        result.push(item.item);
      });
      return result;
    },
    /* 标尺用的 */
    palette() {
      return {
        bgColor: "#18181c", // ruler bg color
        longfgColor: "#BABBBC", // ruler longer mark color
        shortfgColor: "#9C9C9C", // ruler shorter mark color
        fontColor: "#fff", // ruler font color
        shadowColor: "#18181c", // ruler shadow color
        lineColor: "#EB5648",
        borderColor: "#B5B5B5",
        cornerActiveColor: "#fff",
      };
    },
    // 画布大小,一定要是computer里面,否则缩放页面会失效
    canvasStyle() {
      return {
        width: window.innerWidth - 530 + "px",
        transform: `scale(${this.scale})`,
      };
    },
  },
  watch: {
    nav: {
      handler(val, old) {
        this.recordHistoryCache(val);
      },
      deep: true,
    },

    activeObj: {
      handler(val) {
        this.bindParamsWhenEdit()
        if (this.activeObj.sql && this.isSql) {
          let mode = JSON.parse(crypto.decrypt(this.activeObj.sql));
          this.db = mode.id;
          this.sql = mode.sql;
        } else {
          this.db = "";
          this.sql = "";
        }
        if (this.activeObj.dataSource && this.isCustom) {
          this.datasoureChange(this.activeObj.dataSource);
        }
        if (this.activeObj.dataSet && this.isCustom) {
          this.getDatasourceParams(this.activeObj.dataSet);
        }
      },
      deep: true,
    },
    "activeObj.dataType"() {
      this.dataTabs = "0";
    },
    activeOverIndex(n, o) {
      this.dataRes[(o, n)]?.forEach((ele, index) => {
        if (!ele) return;
        this.setActive(ele, index === 1, "setOverActive");
      });
    },
    active(n, o) {
      [o, n].forEach((ele, index) => {
        ele.forEach((item) => {
          this.setActive(item, index === 1, "setActive");
        });
      });
      // 初始化选项卡
      this.menuTabs = "0";
    },
  },
  created() {
    this.getDatasoure();
  },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    for(let i=1;i<=24;i++){
      this.dicRefreshDateList.push({
        label:i+"小时",
        value:i*60*60*1000
      })
    }
    //这里可以进行初始化设置
    setTimeout(() => {
      this.initFun();
      this.initSize();
      this.listenKey();
    });
    this.$eventBus.$on('result', (data) => {
      this.dataRes = data
    })
  },
  methods: {
    editDatasource(id,ids){
     this.showEdit1=true
      if(process.env.NODE_ENV == 'development'){
       // console.log(window.location.origin);
        this.url=  `http://61.144.186.197:19719/subapp/datasource/publicData?id=`+id
            }
            else{
      this.url=window.location.origin+ "/subapp/datasource/publicData?id="+id
      }
      
    },
    timeChange($event, activeObj) {
      let _self = this
      let val = $event.value
      if (val != 0 && val < 1000) {
        this.$message.error(`${this.$t('message.TheTimeCannotBeLessThan1000')}`)//"时间不能小于1000!!"
        setTimeout(() => {
          _self.$set(_self.activeObj, 'time', 1000);
        }, 300)
      }
    },
    changeParamtersValue(item, index) {
      // let tt = this.APIForm.Paramters 
      // debugger
    },
    // 绑定编辑查询参数
    bindParamsWhenEdit() {
      setTimeout(() => {
        // 编辑时，绑定数据源查询参数
        if (!!this.activeObj.isCustom && "string" == typeof this.activeObj.isCustom) {
          console.log("===watch========activeObj=======activeObj.isCustom==", this.activeObj.isCustom)
          let _paramsAndHeaderList = JSON.parse(crypto.decrypt(this.activeObj.isCustom));
          if (_paramsAndHeaderList && _paramsAndHeaderList.headers && _paramsAndHeaderList.headers.length > 0) {
            this.APIForm.domains = _paramsAndHeaderList.headers;
          }
          if (_paramsAndHeaderList && _paramsAndHeaderList.paramters && _paramsAndHeaderList.paramters.length > 0) {
            this.APIForm.Paramters = _paramsAndHeaderList.paramters;
          }

        }
      }, 1000)

    },
    //数据类型 改变，
    dataTypeChange(e) {
      let { value } = e
      if (value == 6) {
        //如果为全局数据源，默认选中第一个,因为当前只支持一个全局数据源
        if (this.DIC.globalDataSet && this.DIC.globalDataSet.length > 0) {
          this.activeObj.dataSource = this.DIC.globalDataSet[0].dataSource
          this.activeObj.dataSet = this.DIC.globalDataSet[0].value
          this.activeObj.time = 0
          // 查询参数
          this.getDatasourceParams(this.activeObj.dataSet);
        }
      } else {
        // this.activeObj.dataSource  = ""
        // this.activeObj.dataSet = ""
        // this.DIC.data=[]
      }

    },
    // 全局数据集改变
    datasetChange_global(e) {
      //   setTimeout(()=>{
      //   this.$set(this.activeObj,"dataSet",value)
      // },100)
    },
    change_global(val) {
      // console.log('change_global:',val)
      this.config['globalDataSource'] = val
    },
    //请求数据
    handleClick(defaultParams) {
      this.dataRes =null
      const params = {
        Parameter: {},
        Id: this.activeObj.dataSet,
      };
      try {
        this.APIForm.Paramters.map((itm) => {
          if (itm.key != "") {
            params.Parameter[itm.key] = itm.value;
          }
        });
      } catch (error) {
        
      }
    

      GetListByDataSetId(params).then((res) => {
        //debugger
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          // edit by andy 
          if ("string" == typeof res.data.Datas) {
            //this.dataRes = JSON.parse(res.data.Datas);
            this.dataRes = res.data.Datas
            this.$message({
              message: '返回数据为字符串，请确认是否需要使用转换 return JSON.parse(data)',
              type: "warning",
            });
          } else {
            this.dataRes = res.data.Datas
          }
          console.log("格式化后的数据+this.dataRes:", this.dataRes)
          ///debugger
        } else {
          this.$message({
            message: res.data.content,
            type: "error",
          });
        }
      }).catch(error => {
        this.$message({
          message: error,
          type: "error",
        });
      });
      return this.dataRes;
    },
    paramsSetting() {
      //请求参数
      this.showEdit = true;
    },

    //数据集改变
    datasetChange({ value }) {
      // setTimeout(()=>{
      //   this.$set(this.activeObj,"dataSet",value)
      // },100)
      if (value) {
        this.getDatasourceParams(value);
        this.getDatasourceModel(value);
      } else {
        console.log("this.activeObj.dataSet:", this.activeObj.dataSet)
        if (this.activeObj.dataSet) {
          this.getDatasourceParams(this.activeObj.dataSet);
          this.getDatasourceModel(this.activeObj.dataSet);
        }

      }
    },
    //数据源改变
    datasoureChange({ value }) {
      // setTimeout(()=>{
      //   this.$set(this.activeObj,"dataSource",value)
      // },100)
      if (value) {
        this.getdatacellect(value);
      } else {
        this.getdatacellect(this.activeObj.dataSource);
      }
    },
    //请求数据源参数
    getDatasourceParams(value) {
      const params = {
        Id: value,
      };
      GetInputParameterByDataSetId(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {

          this.APIForm.Paramters = res.data.Datas;
        }
      });
    },

    //请求数据源接口
    getDatasoure() {
      const params = {
        condition: "",
        typeId: 0,
      };
      GetDataAll(params).then((res) => {
        res = res.data;

        if (res.code == 200 && res.data.Success) {
          const data = res.data.Datas;
          this.DIC.sql = data.map((ele) => {
            return {
              label: ele.CNAME,
              value: ele.CID,
            };
          });
          console.log(this.DIC.sql);
        }
      });
    },

    //请求数据集接口
    getdatacellect(value) {
      const params = {
        condition: `{CID:${value}}`,
      };

      GetSoureAll(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          const data = res.data.Datas;
          this.DIC.data = data.map((ele) => {
            return {
              label: ele.CNAME,
              value: ele.CID,
            };
          });
        }
      });
    },
    //请求数据模型

    getDatasourceModel(value) {
      const params = {
        condition: "",
        datasetId: value,
      };
      GetModelAll(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          const data = res.data.Datas;
          this.DIC.model = data.map((ele) => {
            return {
              label: ele.CCOLUMN_DESC || ele.CCOLUMN_NAME,
              value: ele.CCOLUMN_NAME,
            };
          });
        }
      });
    },

    handleCopyIndex() {
      this.$Clipboard({
        text: this.activeObj.index,
      })
        .then(() => {
          this.$message.success(`${this.$t('message.CopiedSuccessfully')}`);//"复制成功"
        })
        .catch(() => {
          this.$message.error(`${this.$t('message.CopyFailed')}`);//"复制失败"
        });
    },
    handleTabClick(tab) {
      // debugger
      if (tab.name == "1") {
        // 获取最新额全局数据源
        let dataList = getLocalStorageStore("globalDataSource")
        if (dataList) {
          this.DIC.globalDataSet = []
          dataList.forEach(item => {
            let newItem = {
              source: item.dataSource,
              label: item.dataSetLabel,
              value: item.dataSet
            }
            this.DIC.globalDataSet.push(newItem)
          })
        }

      }
    },
    handleImport(file, fileLis) {
      this.$Export.xlsx(file.raw).then((data) => {
        this.activeObj.data = data.results;
        this.$message.success(`${this.$t('message.ImportedSuccessfully')}`);//"导入成功"
        this.handleRes();
      });
    },
    handleRefresh(defaultParams={}) {
      return this.$refs.container.handleRefresh(defaultParams);
    },
   
    handleRes(tip = true,defaultParams={}) {
      this.isTestLoading = true
     setTimeout(() => {
      this.isTestLoading = false
     }, 3000)
      if (this.isSql) {
        this.$set(
          this.activeObj,
          "sql",
          crypto.encrypt(
            JSON.stringify({
              id: this.db,
              sql: this.sql,
            })
          )
        );
      }
      if (this.isApi) {
        this.$set(
          this.activeObj,
          "isApi",
          crypto.encrypt(
            JSON.stringify({
              dataQuery: this.activeObj.dataQuery
            })
          )
        );
      }

      if (this.isCustom || this.isGlobalDataSource) {
        console.log("this.APIForm.Paramters：", this.APIForm.Paramters);
        // debugger
        this.$set(
          this.activeObj,
          "isCustom",
          crypto.encrypt(
            JSON.stringify({
              paramters: this.APIForm.Paramters,
              headers: this.APIForm.domains,
            })
          )
        );

      }

      this.handleRefresh(defaultParams).then((res = {}) => {
         // debugger
        if (!this.validatenull(res)) {
          //debugger
          this.dataRes = JSON.stringify(res, null, 4);
        } else {
          this.dataRes = "";
        }
        if (tip) this.$message.success(`${this.$t('message.RequestForDataWasSuccessful')}`);//"请求数据成功"
      })
    },
    handleParams(type, obj) {
      const deepList = (list, flag) => {
        list.forEach((ele) => {
          ele[type] = flag;
          if (ele.children) deepList(ele.children, flag);
        });
      };
      if (obj) {
        obj[type] = !obj[type];
        deepList([obj], obj[type]);
      } else {
        this.active.forEach((ele) => {
          let { item } = this.findnav(ele);
          item[type] = !item[type];
          deepList([item], item[type]);
        });
      }
      this.handleInitActive();
    },
    handleSetting() {
      this.bindParamsWhenEdit()
      this.dataTabs = 0;
      this.dataRes = "";
      //  this.handleRes(false);
      this.show = true;
    },

    closeCode(value) {
      console.log(this.code.type, value);
      if (this.configData.includes(this.code.type)) {
        this.config[this.code.type] = value;
      } else {
        this.activeObj[this.code.type] = value;
      }
      this.handleRes(false);

    },
    openCode(type, title) {
      this.code.type = type;
      this.code.title = title;
      if (this.configData.includes(type)) {
        this.code.obj = this.config[type];
      } else {
        this.code.obj = this.activeObj[type];
      }
      this.code.box = true;
    },
    initFun() {
      ["setScale"].forEach((ele) => {
        this[ele] = this.$refs.container[ele];
      });
    },
    // 右键菜单
    handleContextMenu(item = {}, done) {
      if (!item.index || this.isKeysCtrl) return;
      else if (!this.isSelectActive) {
        this.active = [item.index];
        this.activeIndex = item.index;
      }
      done();
    },
    //监听键盘的按键
    listenKey() {
      let section = this.$refs.section;
      section.onkeydown = (e) => {
        const keyCode = e.keyCode;
        e.preventDefault();
        if (keyCode == 17) {
          this.keys.ctrl = true;
        } else if (keyCode == 76 && this.isKeysCtrl) {
          if (!this.activeObj) return;
          this.handleParams("lock");
          this.handleInitActive();
        } else if (keyCode == 67 && this.isKeysCtrl) {
          if (!this.activeObj) return;
          this.$refs.contentmenu.handleCopy((fn) => {
            this.cacheList.copy = fn;
            this.$message.success(`${this.$t('message.SuccessfullyCopiedTheComponent')}`);//"复制组件成功"
          });
        } else if (keyCode == 86 && this.isKeysCtrl) {
          if (!this.cacheList.copy) return;
          let active = [];
          this.cacheList.copy.forEach((ele) => {
            active = active.concat(ele());
          });
          setTimeout(() => this.selectNav(active));
          this.$message.success(`${this.$t('message.SuccessfullyPastedTheComponent')}`);//"粘贴组件成功"
        } else if (keyCode == 90 && this.isKeysCtrl) {
          this.editorUndo();
        } else if (keyCode == 89 && this.isKeysCtrl) {
          this.editorRedo();
        } else if (keyCode == 83 && this.isKeysCtrl) {
          this.$refs.headers.handleBuild();
        } else if (keyCode == 8 || keyCode == 46) {
          this.$refs.contentmenu.handleDel();
        }
      };
      section.onkeyup = (e) => {
        const keyCode = e.keyCode;
        if (keyCode == 17) {
          this.keys.ctrl = false;
        }
      };
    },
    setActive(val, result, fun) {
      const obj = this.$refs.container.getListRef(val);
      if (obj) obj[fun](result);
    },
    //批量成组
    handleFolder() {
      let folder = createFile();
      this.active.forEach((index) => {
        let { itemList, itemIndex } = this.findnav(index);
        let obj = itemList.splice(itemIndex, 1)[0];
        folder.children.push(obj);
      });
      this.nav.push(folder);
      this.handleInitActive();
    },
    //批量删除
    handleDeleteSelect() {
      this.$confirm(`${this.$t('message.BatchDeleteTheSelectedLayers')}`, `${this.$t('message.Prompt')}`, {
        confirmButtonText: `${this.$t('message.confirmButtonText')}`,
        cancelButtonText: `${this.$t('message.cancelButtonText')}`,
        type: "warning",
      }).then(() => {
        this.active.forEach((index) => {
          let { itemList, itemIndex } = this.findnav(index);
          itemList.splice(itemIndex, 1);
        });
        this.handleInitActive();
      });
    },
    validProp(name) {
      return this.dicOption[name].includes(this.activeComponent.prop);
    },
    formatTooltip(val) {
      return parseInt(val);
    },
    //打开图库
    handleOpenImg(item, type) {
      this.$refs.imglist.openImg(item, type);
    },
    //图库框回调赋值
    handleSetimg(val, type) {
      let params = type.split(".")[1];
      if (type.includes("config")) {
        this.config[params] = val;
      } else if (type.includes("activeObj.data.value")) {
        this.activeObj.data.value = val;
      } else if (type.includes("activeObj.data")) {
        this.activeObj.data = val;
      } else if (type.includes("activeObj")) {
        this.activeObj[params] = val;
      } else if (type.includes("activeOption")) {
        this.activeOption[params] = val;
      }
    },
    handleScroll() {
      this.$nextTick(() => {
        const screensRect = this.$refs.screensRef.getBoundingClientRect();
        const canvasRect = this.$refs.canvas.getBoundingClientRect();
        // 标尺开始的刻度
        const startX =
          (screensRect.left + this.thick - canvasRect.left) / this.scale;
        const startY =
          (screensRect.top + this.thick - canvasRect.top) / this.scale;
        this.startX = startX >> 0;
        this.startY = startY >> 0;
      });
    },
    // 控制缩放值
    handleWheel(e) {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        const nextScale = parseFloat(
          Math.max(0.2, this.scale - e.deltaY / 500).toFixed(2)
        );
        this.scale = nextScale;
      }
      this.$nextTick(() => {
        this.handleScroll();
      });
    },
    // 初始化标尺数值
    initSize() {
      const domW = this.$refs.section;
      let width = window.innerWidth - 520;
      let height = window.innerHeight - 80;
      domW.style.width = width + "px";
      domW.style.height = height + "px";
      this.width = width;
      this.height = height;
      // 画布阴影部分
      this.shadow = { x: 0, y: 0, width, height };
      // 滚动居中
      let dom = this.$refs.containerRef.getBoundingClientRect();
      this.$refs.screensRef.scrollLeft = dom.width / 2 - 10;
      this.$refs.screensRef.scrollTop = dom.height / 2 - 50;
    },
    // 图片点击事件
    imgClick() {
      this.isShowReferLine = !this.isShowReferLine;
    },
    selectNav(item) {
      if (this.isKeysCtrl) {
        if (Array.isArray(item)) {
          this.active = this.active.concat(item);
        } else {
          this.active.push(item);
        }
      }
      if (Array.isArray(item)) {
        this.active = item;
        this.activeIndex = item[item.length - 1];
      } else if (this.active.includes(item)) {
        this.activeIndex = item;
      } else {
        this.active = [item];
        this.activeIndex = item;
      }
    },
    recordHistoryCache(val) {
      const debounce = (func, delay) => {
        return () => {
          let context = this;
          let args = arguments;
          clearTimeout(this.cacheList.timer);
          this.cacheList.timer = setTimeout(() => {
            func.apply(context, args);
          }, delay);
        };
      };
      const callback = () => {
        let nav = JSON.stringify(val);
        if (nav != this.cacheList.nav) {
          this.cacheList.nav = nav;
          this.addHistoryCache(val);
        }
      };
      debounce(callback, 300)();
    },
    addHistoryCache(val) {
      if (this.currentHistoryIndex + 1 < this.cacheList.history.length) {
        this.cacheList.history.splice(this.currentHistoryIndex + 1);
      }
      this.cacheList.history.push({
        nav: this.deepClone(val),
      });
      this.cacheList.history.splice(100);
      this.currentHistoryIndex++;
    },
    editorUndo() {
      if (!this.canUndo) return;
      this.currentHistoryIndex--;
      this.recoveryHistoryCache();
    },
    editorRedo() {
      if (!this.canRedo) return;
      this.currentHistoryIndex++;
      this.recoveryHistoryCache();
    },
    recoveryHistoryCache() {
      const prevState = this.cacheList.history[this.currentHistoryIndex];
      this.nav = this.deepClone(prevState.nav);
      this.cacheList.nav = JSON.stringify(prevState.nav);
    },
  },
};
</script>
<style lang="scss">
@import "../styles/style.scss";
@import "../styles/common.scss";
@import "../styles/baseFlex.scss";

.refer-line-img {
  position: absolute;
  left: 0;
  z-index: 5;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
  }
}

#screens {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.screen-container {
  position: relative;
  width: 5000px;
  height: 3000px;
  background: url("/img/screen.png") repeat;
}

.dragghanle {
  cursor: pointer;
}

.canvas {
  position: absolute;
  top: 50%;
  left: 50%;
}

.section {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.el-radio-button__inner {
  width: 100%;
  border-radius: 0 !important;
  background: #1d1f26;
  border: 0;
  font-size: 16px;
}
.dialogH{
  height:calc(100vh - 120px);
  //border:1px solid red;
}
</style>
