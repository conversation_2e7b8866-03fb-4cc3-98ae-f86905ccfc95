<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件基础示例</title>
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        .demo-title {
            color: #fff;
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #69bfe7;
            padding-bottom: 10px;
        }
        .table-container {
            height: 400px;
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
        }
        .config-panel {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .config-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .config-item label {
            color: #fff;
            margin-right: 8px;
        }
        .config-item input, .config-item select {
            padding: 5px;
            border: 1px solid #555;
            background: #444;
            color: #fff;
            border-radius: 4px;
        }
        button {
            background: #69bfe7;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #5aa3d1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #fff; text-align: center;">表格插件演示</h1>
        
        <!-- 基础表格示例 -->
        <div class="demo-section">
            <div class="demo-title">基础表格示例</div>
            <div class="config-panel">
                <div class="config-item">
                    <label>显示行数:</label>
                    <input type="number" id="count" value="5" min="1" max="20">
                </div>
                <div class="config-item">
                    <label>开启滚动:</label>
                    <input type="checkbox" id="scroll" checked>
                </div>
                <div class="config-item">
                    <label>滚动速度:</label>
                    <input type="number" id="scrollSpeed" value="2" min="1" max="10">
                </div>
                <div class="config-item">
                    <label>滚动间隔(ms):</label>
                    <input type="number" id="scrollTime" value="2000" min="500" max="10000" step="500">
                </div>
                <div class="config-item">
                    <label>显示边框:</label>
                    <input type="checkbox" id="border">
                </div>
                <div class="config-item">
                    <label>圆角表格:</label>
                    <input type="checkbox" id="roundedTable">
                </div>
                <button onclick="updateTable()">更新配置</button>
                <button onclick="addData()">添加数据</button>
                <button onclick="clearData()">清空数据</button>
            </div>
            <div class="table-container" id="basicTable"></div>
        </div>

        <!-- 条件格式化示例 -->
        <div class="demo-section">
            <div class="demo-title">条件格式化示例</div>
            <div class="table-container" id="conditionalTable"></div>
        </div>

        <!-- 自定义格式化示例 -->
        <div class="demo-section">
            <div class="demo-title">自定义格式化示例</div>
            <div class="table-container" id="formatterTable"></div>
        </div>
    </div>

    <script src="../dist/lib/index.umd.min.js"></script>
    <script>
        // 模拟数据
        let basicData = [
            { id: 1, name: '张三', age: 25, department: '技术部', salary: 8000, status: '在职' },
            { id: 2, name: '李四', age: 30, department: '销售部', salary: 12000, status: '在职' },
            { id: 3, name: '王五', age: 28, department: '市场部', salary: 9500, status: '离职' },
            { id: 4, name: '赵六', age: 35, department: '技术部', salary: 15000, status: '在职' },
            { id: 5, name: '钱七', age: 26, department: '人事部', salary: 7000, status: '在职' },
            { id: 6, name: '孙八', age: 32, department: '财务部', salary: 11000, status: '在职' },
            { id: 7, name: '周九', age: 29, department: '技术部', salary: 13000, status: '在职' },
            { id: 8, name: '吴十', age: 27, department: '销售部', salary: 8500, status: '离职' }
        ];

        // 基础表格配置
        const basicTableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 5,
            scroll: true,
            scrollTime: 2000,
            scrollSpeed: 2,
            border: false,
            roundedTable: false,
            headerBackground: "#050e18",
            headerColor: "#69bfe7",
            headerFontSize: 16,
            headerTextAlign: "center",
            bodyColor: "#69bfe7",
            bodyFontSize: 14,
            bodyTextAlign: "center",
            nthColor: "#09192c",
            othColor: "#142a40",
            column: [
                { label: "姓名", prop: "name", width: 100 },
                { label: "年龄", prop: "age", width: 80 },
                { label: "部门", prop: "department", width: 120 },
                { label: "薪资", prop: "salary", width: 100 },
                { label: "状态", prop: "status", width: 80 }
            ]
        };

        // 条件格式化表格配置
        const conditionalTableConfig = {
            ...basicTableConfig,
            column: [
                { label: "姓名", prop: "name", width: 100 },
                { label: "年龄", prop: "age", width: 80 },
                { label: "部门", prop: "department", width: 120 },
                { 
                    label: "薪资", 
                    prop: "salary", 
                    width: 100,
                    condition: 1, // 大于
                    value: 10000,
                    cellbackground: "#67C23A",
                    cellfont: "#ffffff"
                },
                { 
                    label: "状态", 
                    prop: "status", 
                    width: 80,
                    condition: 3, // 等于
                    value: "离职",
                    cellbackground: "#F56C6C",
                    cellfont: "#ffffff"
                }
            ]
        };

        // 自定义格式化表格配置
        const formatterTableConfig = {
            ...basicTableConfig,
            column: [
                { label: "姓名", prop: "name", width: 100 },
                { 
                    label: "年龄", 
                    prop: "age", 
                    width: 80,
                    formatter: `(item, row) => {
                        if (row.age >= 30) {
                            return '<span style="color: #E6A23C;">🧑 ' + row.age + '</span>';
                        } else {
                            return '<span style="color: #67C23A;">👦 ' + row.age + '</span>';
                        }
                    }`
                },
                { label: "部门", prop: "department", width: 120 },
                { 
                    label: "薪资", 
                    prop: "salary", 
                    width: 100,
                    formatter: `(item, row) => {
                        return '¥' + row.salary.toLocaleString();
                    }`
                },
                { 
                    label: "状态", 
                    prop: "status", 
                    width: 80,
                    formatter: `(item, row) => {
                        if (row.status === '在职') {
                            return '<span style="color: #67C23A;">✓ 在职</span>';
                        } else {
                            return '<span style="color: #F56C6C;">✗ 离职</span>';
                        }
                    }`
                }
            ]
        };

        // 初始化表格
        function initTables() {
            // 这里应该调用实际的表格组件初始化方法
            // 由于没有实际的组件库，这里只是模拟
            console.log('初始化基础表格:', basicTableConfig);
            console.log('初始化条件格式化表格:', conditionalTableConfig);
            console.log('初始化自定义格式化表格:', formatterTableConfig);
            
            // 模拟表格渲染
            renderMockTable('basicTable', basicData, basicTableConfig);
            renderMockTable('conditionalTable', basicData, conditionalTableConfig);
            renderMockTable('formatterTable', basicData, formatterTableConfig);
        }

        // 模拟表格渲染
        function renderMockTable(containerId, data, config) {
            const container = document.getElementById(containerId);
            let html = '<div style="color: #69bfe7; padding: 20px; text-align: center;">';
            html += '<p>表格组件将在这里渲染</p>';
            html += '<p>配置: ' + JSON.stringify(config, null, 2).substring(0, 100) + '...</p>';
            html += '<p>数据行数: ' + data.length + '</p>';
            html += '</div>';
            container.innerHTML = html;
        }

        // 更新表格配置
        function updateTable() {
            basicTableConfig.count = parseInt(document.getElementById('count').value);
            basicTableConfig.scroll = document.getElementById('scroll').checked;
            basicTableConfig.scrollSpeed = parseInt(document.getElementById('scrollSpeed').value);
            basicTableConfig.scrollTime = parseInt(document.getElementById('scrollTime').value);
            basicTableConfig.border = document.getElementById('border').checked;
            basicTableConfig.roundedTable = document.getElementById('roundedTable').checked;
            
            renderMockTable('basicTable', basicData, basicTableConfig);
        }

        // 添加数据
        function addData() {
            const newId = basicData.length + 1;
            const names = ['新员工A', '新员工B', '新员工C', '新员工D'];
            const departments = ['技术部', '销售部', '市场部', '人事部', '财务部'];
            
            basicData.push({
                id: newId,
                name: names[Math.floor(Math.random() * names.length)],
                age: Math.floor(Math.random() * 20) + 22,
                department: departments[Math.floor(Math.random() * departments.length)],
                salary: Math.floor(Math.random() * 10000) + 5000,
                status: Math.random() > 0.8 ? '离职' : '在职'
            });
            
            renderMockTable('basicTable', basicData, basicTableConfig);
        }

        // 清空数据
        function clearData() {
            basicData = [];
            renderMockTable('basicTable', basicData, basicTableConfig);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTables();
        });
    </script>
</body>
</html>
