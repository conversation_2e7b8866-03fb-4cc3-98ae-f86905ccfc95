<template>
  <div class="filemanagement">
    <vxe-toolbar>
      <template #buttons>
        <el-button icon="el-icon-plus" type="primary" size="mini" @click="insertEvent">
          <!-- 新增 -->
          {{ $t('page.filemanagement.Add') }}
        </el-button>
        <el-upload style="margin: 0 10px;" :show-file-list="false" :httpRequest="httpRequest4" action="" ref="upload"
          accept=".json">
          <el-button icon="el-icon-upload2
" type="primary" size="mini" @click="import_part">
<!-- 导入 -->
{{ $t('page.filemanagement.Import') }}
</el-button>
        </el-upload>

        <el-button icon="el-icon-download" type="primary" size="mini" @click="export_part">
          <!-- 导出 -->
          {{ $t('page.filemanagement.Export') }}
        </el-button>
        <el-button class="pinBtnOnCla" icon="el-icon-tickets" type="primary" size="small" @click="synchronous()">
          <!-- 同步 -->
          {{ $t('page.filemanagement.Synchronize') }}
        </el-button>

      </template>
      <template #tools>
        <span style="color: #333; font-size: 12px">
          <!-- 关键字 -->
          {{ $t('page.filemanagement.Keyword') }}
        </span>
        <el-input style="width: 180px; margin: 0 10px" size="mini" @keyup.enter.native="onSubmit"
          v-model="formData.name" placeholder="关键字" :title="`${$t('page.filemanagement.Keyword')}`"></el-input>
        <el-button title="查询" :title="`${$t('page.filemanagement.Query')}`" size="mini" type="primary" icon="el-icon-search" @click="onSubmit">{{
          }}</el-button>
        <el-button title="重置" :title="`${$t('page.filemanagement.Reset')}`" size="mini" type="primary" icon="el-icon-refresh-right" @click="reset">{{
          }}</el-button>
      </template>
    </vxe-toolbar>

    <vxe-table ref="xTable" border align="center" :loading="loading" show-overflow keep-source resizable
      :data="tableData">
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column type="seq" width="60" title="序号" :title="`${$t('page.filemanagement.SerialNumber')}`"></vxe-column>
      <vxe-column field="CIMAGE_NAME" align="left" title="图片名称" :title="`${$t('page.filemanagement.PictureName')}`">
      </vxe-column>
      <vxe-column field="CIMAGE_TYPE" width="100" title="图片类型" :title="`${$t('page.filemanagement.PictureType')}`">
      </vxe-column>
      <vxe-column field="CIMAGE_PATH" align="left" title="图片路径" :title="`${$t('page.filemanagement.PicturePath')}`">
      </vxe-column>
      <vxe-column width="100" field="CIMAGE_PATH" title="图片预览" :title="`${$t('page.filemanagement.PicturePreview')}`">
        <template #default="{ row }">
          <el-image v-if="row.CIMAGE_PATH.includes('http')" style="width: 30px; height: 30px" :src="row.CIMAGE_PATH"
            :preview-src-list="[row.CIMAGE_PATH]">
          </el-image>
          <el-image v-else style="width: 30px; height: 30px" :src="serverUrl + row.CIMAGE_PATH.replace('/', '')"
            :preview-src-list="[serverUrl + row.CIMAGE_PATH.replace('/', '')]">
          </el-image>
        </template>
      </vxe-column>

      <vxe-column title="操作" :title="`${$t('page.filemanagement.Operation')}`" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="text" @click="EditData(row)">
            <!-- 编辑 -->
            {{ $t('page.filemanagement.Edit') }}
          </el-button>
          <el-button type="text" style="color: rgb(245, 108, 108)" @click="deleterevertData(row)">
            <!-- 删除 -->
            {{ $t('page.filemanagement.Delete') }}
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <vxe-pager align="right" size="small" @page-change="pageChange" :current-page.sync="page2.currentPage"
      :page-size.sync="page2.pageSize" :total="page2.totalResult" :layouts="[
          'PrevJump',
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total',
        ]">
    </vxe-pager>

    <vxe-modal v-model="updateFierl" width="400" @close="close1()">
      <template #title>
        <span>{{ title }}</span>
      </template>
      <div v-loading="true" element-loading-text="loading..." element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)">
        <el-upload class="upload-demo" :show-file-list="false" action="" accept="image/*" :httpRequest="httpRequest"
          drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            <!-- 将文件拖到此处， -->
            {{ $t('page.filemanagement.DragTheFileHere') }}
            <em>
              <!-- 点击上传 -->
              {{ $t('page.filemanagement.ClickToUpload') }}
            </em></div>
        </el-upload>
      </div>
    </vxe-modal>
  </div>
</template>
<script>
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import { GetPageListByQueryJson, UpdateImage, TestUpload, Delete, PushData, Import1, Export1 } from "./Api.js";
import BMF from "browser-md5-file";
import { mapGetters } from "vuex";
import { url as serverUrl } from "@/config";
export default {
  name: "filemanagement",

  data() {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      serverUrl: serverUrl,
      updateFierl: false,
      title: "",
      formData: {
        name: "",
      },
      tableData: [],
      addbln: false,
      loading: true,
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
      objRow: {
        CID: 0
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    this.getdatasoures();

  },

  methods: {
    //同步
    synchronous() {
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
      PushData(ids).then(res => {
        res = res.data
        if (res.code === 200 && res.data.Success) {
          this.$message.success(res.data.Content)
        } else {
          this.$message.error(res.data.Content)
        }
      })
    },
    httpRequest4(file) {
      const reader = new FileReader();
      // 异步处理文件数据
      reader.readAsText(file.file, 'UTF-8');
      // 处理完成后立马触发 onload
      reader.onload = fileReader => {
        const fileData = fileReader.target.result;
        if (fileData) {
          var text = JSON.parse(fileData)


          Import1(text).then(res => {
            res = res.data
            if (res.code === 200 && res.data.Success) {
              this.$message.success(res.data.Content)
              this.getdatasoures()

            } else {
              this.$message.error(res.data.Content)
            }
          })
        }

        // 上面的两个输出相同
      };

    },
    import_part() {
      console.log(111);
    },
    export_part() {
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
      Export1(ids).then(res => {
        res = res.data
        if (res.code === 200 && res.data.Success) {
          let link = document.createElement("a");
          link.download = "文件管理.json";
          link.href = "data:text/plain," + JSON.stringify(res.data.Datas);
          link.click();
        } else {
          this.$message.error(res.data.Content)
        }
      })

    },




    httpRequest(item) {


      return new Promise((resolve, reject) => {
        const bmf = new BMF();
        bmf.md5(
          item.file,
          (err, md5) => {
            this.jiamiText = md5;

            resolve(true); // 97027eb624f85892c69c4bcec8ab0f11
          },
          (progress) => {
            console.log("progress number:", progress);
          }
        );
      })
        .then(() => {


          const formData = new FormData();

          // 将文件对象添加到 FormData 中
          formData.append("File", item.file);

          formData.append("CID", this.objRow.CID);

          formData.append("MD5", this.jiamiText);
          UpdateImage(formData).then(res => {
            res = res.data;
            if (res.code === 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.getdatasoures();
              this.updateFierl = false;
            } else {
              this.$message({
                message: res.data.Content,
                type: "error",
              });
            }
          })



        })









      // return new Promise((resolve, reject) => {
      //   const bmf = new BMF();
      //   bmf.md5(
      //     item.file,
      //     (err, md5) => {
      //       this.jiamiText = md5;

      //       resolve(true); // 97027eb624f85892c69c4bcec8ab0f11
      //     },
      //     (progress) => {
      //       console.log("progress number:", progress);
      //     }
      //   );
      // })
      //   .then(() => {
      //     const stringValue =
      //       item.file.name +
      //       "¤" +
      //       this.jiamiText +
      //       "¤" +
      //       item.file.size +
      //       "¤" +
      //       item.file.type +
      //       (this.addbln ? "¤" + this.objRow.CID : "");

      //     var encoder = new TextEncoder("utf8");
      //     var byts = encoder.encode(stringValue);
      //     var bytslength = byts.length;
      //     var array1 = Array.from(byts);
      //     var array2 = this.IntToBytesLittleEndian(bytslength, 4);
      //     this.uploadArray = [...array2, ...array1];
      //   })
      //   .then(() => {
      //  var _this = this;
      // var reader = new FileReader();
      // reader.readAsArrayBuffer(item.file);
      // reader.onload = function () {
      //   var result = [];
      //   var byts = new Uint8Array(this.result);
      //   var array4 = Array.from(byts);
      //   result = [..._this.uploadArray, ...array4];

      //       UpdateImage(result).then((res) => {
      //         res = res.data;
      //         if (res.code === 200 && res.data.Success) {
      //           _this.$message({
      //             message: res.data.Content,
      //             type: "success",
      //           });
      //           _this.getdatasoures();
      //           _this.updateFierl = false;
      //         } else {
      //           _this.$message({
      //             message: res.data.Content,
      //             type: "error",
      //           });
      //         }
      //       });
      //     };
      //   });
    },
    //小端模式
    //number 要转换的整形数值
    //length 要转成什么byte数组，规定数组的长度
    //如uint16，则lenght=2表示两个字节，转成的byte数组长度是length=2
    //如uint32，则lenght=2表示两个字节，转成的byte数组长度是length=4
    IntToBytesLittleEndian(number, length) {
      var bytes = [];
      var i = 0;
      do {
        bytes[i++] = number & 255;
        number = number >> 8;
      } while (i < length);
      return bytes;
    },
    pageChange() {
      this.getdatasoures();
    },
    getList() {
      this.getdatasoures();
    },
    //请求地图接口
    getdatasoures() {
      const text = {
        condition: this.formData.name,
        start: this.page2.currentPage,
        length: this.page2.pageSize,
      };

      GetPageListByQueryJson(text).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.tableData = res.data.Datas;

          this.page2.totalResult = res.data.TotalRows;
        } else {
          this.$message({
            message: res.data.Content || res.message.msg,
            type: "error",
          });
        }
        this.loading = false;
      });
    },

    //新建
    insertEvent() {
      this.title = this.$t('page.filemanagement.Create')// "新建";
      this.addbln = false;
      this.updateFierl = true;
    },

    EditData(row) {
      this.title = this.$t('page.filemanagement.Edit')// "编辑";
      this.objRow = row;
      this.addbln = true;
      this.updateFierl = true;
    },
    deleterevertData(row) {
      const options = {
        content: "您确定要删除这条数据吗？",
        size: "small",
        status: "error",
        iconStatus: "vxe-icon-error-circle-fill",
      };
      this.$VXETable.modal.confirm(options).then((type) => {
        if (type == "confirm") {
          Delete(row).then((res) => {
            res = res.data;
            if (res.code == 200 && res.data.Success) {
              this.$message({
                message: `${this.$t('message.OperationSuccessful')}`,//"操作成功"
                type: "success",
              });
              this.getdatasoures();
            } else {
              this.$message({
                message: res.message.msg || res.data.Content,
                type: "error",
              });
            }
          });
        }
      });
    },

    onSubmit() {
      this.getdatasoures();
    },
    reset() {
      this.formData.name = "";
      this.getdatasoures();
    },
  },
};
</script>
<style lang="scss">
.filemanagement {
  height: calc(100vh - 140px);
  padding: 10px;
  margin: 10px;
  background: #fff;

  .vxe-table--body-wrapper {
    height: calc(100vh - 266px);
  }
}
</style>
