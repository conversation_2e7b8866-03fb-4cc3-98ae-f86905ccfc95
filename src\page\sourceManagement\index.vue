<template>
  <div class="sourceManagement">
    <div class="sourceManagement_left">
      <p class="title">数据源</p>
      <el-tree
      :expand-on-click-node="false"
        :data="datasourList"
        node-key="CID"
        default-expand-all
        @node-click="nodeClick"
        :props="defaultProps"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div>{{ node.label }}</div>
          <!-- <div v-if="data.children.length!==0">
            <el-button type="text" size="mini" @click.stop="() => append(data)">
              <i class="el-icon-plus"></i>
              添加
            </el-button>
          </div> -->
        </div>
      </el-tree>
    </div>
    <div class="sourceManagement_right">
      <div class="datasetManagement_right_table">
        <vxe-toolbar>
          <template #buttons>
            <el-button
              icon="el-icon-plus"
              type="primary"
              size="mini"
              @click="insertEvent"
              >新建</el-button
            >
          </template>
          <template #tools>
            <span style="color: #333; font-size: 12px">数据源</span>
            <el-input
              style="width: 180px; margin: 0 10px"
              size="mini"
              @keyup.enter.native="onSubmit"
              v-model="formData.WorkType"
              placeholder="请输入数据源"
            ></el-input>
            <el-button
              title="查询"
              size="mini"
              type="primary"
              icon="el-icon-search"
              @click="onSubmit"
              >{{
            }}</el-button>
            <el-button
              title="重置"
              size="mini"
              type="primary"
              icon="el-icon-refresh-right"
              @click="reset"
              >{{
            }}</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="xTable"
          border
          align="center"
          :loading="loading"
          show-overflow
          keep-source
          :data="tableData"
        
        >
          <vxe-column type="seq" width="60" title="序号"></vxe-column>
          <vxe-column field="CDATASOURCE_TYPE_NAME"  title="数据源类型">
          </vxe-column>

          <vxe-column field="CNAME"  title="数据源名称">
          </vxe-column><vxe-column field="CHOST"  title="主机名">
          </vxe-column><vxe-column field="CDB_NAME"  title="数据库名称">
          </vxe-column><vxe-column field="CUSER_NAME"  title="用户名">
          </vxe-column><vxe-column field="CDESC"  title="备注">
          </vxe-column>









        
          <vxe-column title="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="revertData(row)">详情</el-button>
              <el-button type="text" @click="EditData(row)">编辑</el-button>
              <el-button type="text" @click="deleterevertData(row)"
                >删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <vxe-pager
          align="right"
          background
          size="small"
          @page-change="pageChange"
          :current-page.sync="page2.currentPage"
          :page-size.sync="page2.pageSize"
          :total="page2.totalResult"
          :layouts="[
            'PrevJump',
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'NextJump',
            'Sizes',
            'FullJump',
            'Total',
          ]"
        >
        </vxe-pager>
      </div>
    </div>
    <ModelConfig ref="modelEvent" :title="title" :getList="getList" />
  </div>
</template>
<script>
import {
  GetDataType,
  DeleteDatasource,
  DataSourceGetPagelist,
} from "@/api/visual";
import { mapGetters } from "vuex";
import ModelConfig from "./modelConfig.vue";
//   import DatasetConfiguration from './datasetConfiguration.vue'
export default {
  name: "sourceManagement",
  components: {
    ModelConfig,
  },
  data() {
    return {
      title: "",
      formData: {
     
        WorkType: "",
      },
      CWORK_DATES: "",
      loading: true,
      dataTime: 0,
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
      projectoption: [],
      typeoptions: [
        { value: 0, name: "正常上班" },
        { value: 1, name: "工作日加班" },
        { value: 2, name: "周末加班" },
        { value: 3, name: "节假日加班" },
      ],
      useroptions: [],
      tableData: [],
      datasourList: [],
      defaultProps: {
        children: "children",
        label: "CNAME",
      },
      dataOptions:[],
      nodeID:0
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.loading=true
    this.getdatasoures();
    this.getSourcelList(this.nodeID);

  },

  methods: {
    //过滤数据源类型
    filterData(value){
      var text=''
       var arr=this.dataOptions.filter(item=>item.CID==value)
    //    console.log(arr);
     if(arr&&arr.length==0){
        text=arr[0]?.CNAME
     }
     return text
    },
    getList() {
      this.loading=true
      this.getSourcelList(this.nodeID);
    },
    //请求数据源接口
    getdatasoures() {
      const params={
        type:0
      }
      GetDataType(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.datasourList = this.delDepartTree(res.data.Datas);
           this.dataOptions=res.data.Datas
        } else {
          this.$message({
            message: res.message.msg,
            type: "error",
          });
        }
       
      });
    },
    nodeClick(node){
         this.nodeID=node.CID
          this.getSourcelList(node.CID)
    },
    getSourcelList(value,keywowd){
      const params={
        condition:keywowd||'',
        typeId:value,
        start:this.page2.currentPage,
        length:this.page2.pageSize
      }
      DataSourceGetPagelist(params).then(res=>{
            res=res.data
        if(res.code === 200 && res.data.Success){
              this.tableData=res.data.Datas
              this.page2.totalResult=res.data.TotalRows
        }else{
          this.$message({
            message: res.message.msg,
            type: "error",
          });
        }
        this.loading = false;
      })
    },
    
    //树形结构转化
    delDepartTree(list) {
      // 1. 定义两个中间变量
      const treeList = [], // 最终要产出的树状数据的数组
        map = {}; // 存储映射关系

      // 2. 建立一个映射关系，并给每个元素补充children属性.
      // 映射关系: 目的是让我们能通过id快速找到对应的元素
      // 补充children：让后边的计算更方便
      list.forEach((item) => {
        item.children = [];
        map[item.CID] = item;
      });
      // 3. 循环
      list.forEach((item) => {
        // 对于每一个元素来说，先找它的上级
        //    如果能找到，说明它有上级，则要把它添加到上级的children中去
        //    如果找不到，说明它没有上级，直接添加到 treeList
        const parent = map[item.CPARENT_ID];
        if (parent) {
          parent.children.push(item);
        } else {
          treeList.push(item);
        }
      });
      // 4. 返回出去
      return treeList;
    },
    //新建数据集
    insertEvent() {
      this.title = "创建数据源";
      this.$refs.modelEvent.addshowEdit();
    },
  
    revertData(row) {
      this.title = "数据源详情";
      this.$refs.modelEvent.detailsData(true,row);
    },
    EditData(row){
      this.title = "编辑数据源";
      this.$refs.modelEvent.appendshowEdit(true,row);
    },
    deleterevertData(row) {
      const options = {
        content: "您确定要删除这条数据吗？",
        size: "small",
        status: "error",
        iconStatus: "vxe-icon-error-circle-fill",
      };
      this.$VXETable.modal.confirm(options).then((type) => {
        if (type == "confirm") {
          const params = {
            CID: row.CID,
          };
          DeleteDatasource(params).then((res) => {
            res = res.data;
            if (res.code == 200 && res.data.Success) {
              this.$message({
                message: res.data.Content,
                type: "success",
              });
              this.loading = true;
              this.getSourcelList(this.nodeID);
            } else {
              this.$message({
                message: res.message.msg,
                type: "error",
              });
            }
          });
        }
      });
    },
   
    onSubmit() {
      this.loading=true
       this.getSourcelList(this.nodeID,this.formData.WorkType);
    },
    reset() {
      
      this.formData.WorkType = "";
      this.getSourcelList(this.nodeID,this.formData.WorkType);
    },

    pageChange(value) {
      this.loading = true;
        this.page2.currentPage= value.currentPage;
        this.page2.pageSize=value.pageSize;
        this.getSourcelList(this.nodeID,this.formData.WorkType);
    },
  },
};
</script>
<style lang="scss">
.sourceManagement {
  margin: 10px;
  display: flex;

  .sourceManagement_left {
    min-height: calc(100vh - 126px);
    .title {
      border-bottom: 1px solid #f1f1f1;
      font-size: 16px;
      height: 48px;
      line-height: 48px;
      padding-left: 20px;
      margin-bottom: 15px;
    }
    background: #fff;
    width: 250px;
    margin-right: 10px;
    .custom-tree-node {
      display: flex;
      justify-content: space-between;
      line-height: 26px;
      width: 100%;
      padding-right: 8px;
      color: #666666;
    }
  }

  .sourceManagement_right {
    flex: 1;
    overflow-y: auto;
    .vxe-table--body-wrapper {
      height: calc(100vh - 269px);
    }
    .datasetManagement_right_table {
      padding: 5px 10px 0px 10px;
      background: #fff;
    }
  }
  .el-button {
    height: 32px;
  }
}
</style>
