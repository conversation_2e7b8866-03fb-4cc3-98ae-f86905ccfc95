import { url ,urls} from '@/config';
import { config } from '@/option/config'
import request from '../../axios'



//分页获取看板图片
export const GetPageListByQueryJson = (params) => request({
    url: url + 'api/kanban/kanbanImage/GetAll',
    method: 'get',
    params:params
  });

//添加看板地图
  export const UpdateImage = (data) => request({
    url: url + 'api/kanban/kanbanImage/BytePost/UpdateImage',
    method: 'post',
    data:data
  });
  

  //删除看板地图
  export const Delete = (data) => request({
    url: url + 'api/kanban/kanbanImage/Delete',
    method: 'post',
    data:data
  });
  



