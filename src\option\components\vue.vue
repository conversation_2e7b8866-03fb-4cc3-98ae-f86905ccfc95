<!-- 通用形配置 -->
<template>
  <div>
    <el-form-item label="组件" :label="`${$t('page.build.Component')}`">
      <el-button size="small"
                 type="primary"
                 @click="openCode">
                 <!-- 编辑 -->
                 {{ $t('page.build.Edit') }}
                </el-button>
      <el-button type="primary"
                 size="small"
                 @click="api.box=true"
                 icon="el-icon-search"></el-button>
    </el-form-item>
    <el-form-item label1="组件库" :label="`${$t('components.common.ComponentLibrary')}`">
    
    <el-button size="small"
             type="primary"
             @click="AddNewComponent">
             <!-- 加入组件库 -->
             {{ $t('components.common.ComponentLibrary') }}
            </el-button>         
</el-form-item>
    <codeedit @submit="codeClose"
              title1="配置列表"
               :title="`${$t('page.build.ConfigurationList')}`"
              :rules="false"
              v-model="code.obj"
              v-if="code.box"
              :type="code.type"
              :visible.sync="code.box"></codeedit>

    <el-dialog title="组件库" 
               :title="`${$t('components.common.ComponentLibrary')}`"
               :visible.sync="api.box"
               width="80%">
      <component-list v-if="api.box"
                     :activeName="0"
                      @change="handleApiChange"
                      :menu="false"></component-list>
      <!-- <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="handleApiSubmit()"
                   size="small">确 定</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>

<script>
import codeedit from '../../page/group/code';
import componentList from '../../page/list/components'
// import { getObj } from '@/api/components'
import { UploadFile,getList, getObj, addObj, delObj, updateObj } from '@/api/components'
export default {
  name: 'vue',
  inject: ["main"],
  components: {
    componentList,
    codeedit
  },
  data () {
    return {
      api: {
        box: false,
        item: {}
      },
      code: {
        type: 'content',
        box: false,
        obj: {},
      }
    }
  },
  methods: {
    handleApiSubmit () {
      getObj(this.api.item.CID).then(res => {
        //debugger
        const data = res.data.data;
        this.main.activeOption[this.code.type] = data.Datas.CCONTEXT
        this.api.box = false;
        this.api.item = {}
      })
    },
    handleApiChange (val) {
      //debugger
      this.api.item = val
      this.handleApiSubmit()
    },
    codeClose (value) {
      this.main.activeOption[this.code.type] = value;
    },
    openCode () {
      this.code.obj = this.main.activeOption[this.code.type];
      this.code.box = true;
    },
    AddNewComponent(){
      let codeType = 'content';
      let codeObj = this.main.activeOption[codeType];
      let _title = this.main.config.name
      let randomName = Math.floor(Math.random()*1000)
      let params ={
          "IS_UPDATE": false,
          "CNAME":_title+'_'+randomName,
          "CCONTEXT": codeObj,
          "CTYPE": 0,
          "CREMARK": "新组件",
          "CIMG_URL": "/img/components-default.png"
       }
       addObj(params).then(res=>{
         this.$message.success(`${this.$t('message.AddedSuccessfully')}`)//"添加成功"
       })
    }
  }
}
</script>

<style>
</style>