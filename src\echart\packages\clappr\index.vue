<template>
  <div :class="[b(),className]"
       :style="styleSizeName"
       @click="handleClick">
         <!-- <span style="color: white;"> andy videoUrlFormat:>>>>> {{ urlFormat }}/////dataChart:{{ dataChart }}</span>
   -->
    <div :id="hid"
         v-if="reload"
         :style="styleChartName"></div>
  </div>
</template>

<script>
import { uuid } from '@/utils/utils';
import create from "../../create";
export default create({
  name: "clapper",
  data () {
    return {
      hid: 'main_' + uuid(),
      reload: true,
      config: {}
    }
  },
  computed: {
    urlFormat () {
      let _optionData = (Array.isArray(this.dataChart)?this.dataChart[0]:this.dataChart)
      return _optionData
    },
    autoplay () {
      return this.option.autoplay
    }
  },
  watch: {
    dataChart: {
      handler () {
        this.reload = false;
        this.$nextTick(() => {
          this.reload = true;
          setTimeout(() => {
            new Clappr.Player({
              parentId: '#' + this.hid,
              source: this.urlFormat.value,
              autoPlay: this.autoplay,
              mute: true,
              height: '100%',
              width: '100%'
            });
          })
        })
      },
      deep: true
    }
  },
  methods: {
    handleClick () {
      this.clickFormatter && this.clickFormatter({
        data: this.dataChart
      }, this.getItemRefs());
    }
  }
});
</script>



