<!-- 通用配置-->
<template>
  <div>
    <!-- 折叠公共配置 -->
    <el-collapse accordion>
      <!-- 标题设置 -->
      <template v-if="main.validProp('titleList')">
        <el-collapse-item title1="标题设置" :title="`${$t('components.main.TitleSettings')}`">
          <el-form-item label1="标题显示" :label="`${$t('components.main.TitleDisplay')}`">
            <avue-switch v-model="main.activeOption.titleShow"></avue-switch>
          </el-form-item>
          <el-form-item label1="标题" :label="`${$t('components.main.Title')}`">
            <avue-input v-model="main.activeOption.title"></avue-input>
          </el-form-item>
          <el-form-item label1="字体颜色" :label="`${$t('components.main.FontColor')}`">
            <avue-input-color v-model="main.activeOption.titleColor"></avue-input-color>
          </el-form-item>
          <el-form-item label1="字体大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.titleFontSize"></avue-input-number>
          </el-form-item>
          <el-form-item label1="字体位置" :label="`${$t('components.main.FontPosition')}`">
            <avue-select v-model="main.activeOption.titlePosition"
                         :dic="dicOption.textAlign">
            </avue-select>
          </el-form-item>
          <el-form-item label1="副标题" :label="`${$t('components.main.Subtitle')}`">
            <avue-input v-model="main.activeOption.subtext"></avue-input>
          </el-form-item>
          <el-form-item label1="字体颜色" :label="`${$t('components.main.FontColor')}`">
            <avue-input-color v-model="main.activeOption.subTitleColor"></avue-input-color>
          </el-form-item>
          <el-form-item label1="字体大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.subTitleFontSize">
            </avue-input-number>
          </el-form-item>
        </el-collapse-item>
      </template>
      <!-- 轴设置 -->
      <template v-if="main.validProp('barList')">
        <el-collapse-item title1="X轴设置" :title="`${$t('components.main.X-axisSettings')}`">
          <el-form-item label1="名称" :label="`${$t('components.main.Name')}`">
            <avue-input v-model="main.activeOption.xAxisName">
            </avue-input>
          </el-form-item>
            <!-- <el-form-item label="多X柱" >
              <avue-switch v-model="main.activeOption.isDataModelXMultiple"></avue-switch>
            </el-form-item>
            <el-form-item label="其它X柱" v-show="!!main.activeOption.isDataModelXMultiple">
              <avue-select multiple   v-model="main.activeOption.dataMulModelX"></avue-select>
            </el-form-item> -->
          <el-form-item label1="显示" :label="`${$t('components.main.Display')}`">
            <avue-switch v-model="main.activeOption.xAxisShow">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="显示网格线" :label="`${$t('components.main.DisplayGridLine')}`">
            <avue-switch v-model="main.activeOption.xAxisSplitLineShow">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="标签间距" :label="`${$t('components.main.LabelSpacing')}`">
            <avue-switch v-model="main.activeOption.xAxisInterval">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="文字角度" :label="`${$t('components.main.TextAngle')}`">
            <avue-input-number v-model="main.activeOption.xAxisRotate">
            </avue-input-number>
          </el-form-item>
          <el-form-item label1="轴反转" :label="`${$t('components.main.AxisInversion')}`">
            <avue-switch v-model="main.activeOption.xAxisInverse">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="文字大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.xAxisFontSize">
            </avue-input-number>
          </el-form-item>
          <el-form-item label1="文字颜色" :label="`${$t('components.main.FontColor')}`">
            <avue-input-color v-model="main.activeOption.xAxisColor">
            </avue-input-color>
          </el-form-item>
          <el-form-item label1="轴线颜色" :label="`${$t('components.main.AxisColor')}`">
            <avue-input-color v-model="main.activeOption.xAxisLineColor"></avue-input-color>
          </el-form-item>
        </el-collapse-item>
        <el-collapse-item title1="Y轴设置" :title="`${$t('components.main.Y-axisSettings')}`">
          <el-form-item label1="名称" :label="`${$t('components.main.Name')}`">
            <avue-input v-model="main.activeOption.yAxisName">
            </avue-input>
          </el-form-item>
          <el-form-item label1="显示" :label="`${$t('components.main.Display')}`">
            <avue-switch v-model="main.activeOption.yAxisShow">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="轴网格线" :label="`${$t('components.main.AxisGridLines')}`">
            <avue-switch v-model="main.activeOption.yAxisSplitLineShow">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="反转" :label="`${$t('components.main.Inversion')}`">
            <avue-switch v-model="main.activeOption.yAxisInverse">
            </avue-switch>
          </el-form-item>
          <el-form-item label1="文字大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.yAxisFontSize">
            </avue-input-number>
          </el-form-item>
          <el-form-item label1="文字颜色" :label="`${$t('components.main.FontColor')}`">
            <avue-input-color v-model="main.activeOption.yAxisColor">
            </avue-input-color>
          </el-form-item>
          <el-form-item label1="轴线颜色" :label="`${$t('components.main.AxisColor')}`">
            <avue-input-color v-model="main.activeOption.yAxisLineColor"></avue-input-color>
          </el-form-item>
        </el-collapse-item>
      </template>
      <!-- 数值设置 -->
      <template v-if="main.validProp('labelList')">
        <el-collapse-item title1="数值设置" :title="`${$t('components.main.ValueSettings')}`">
          <el-form-item label1="显示" :label="`${$t('components.main.Display')}`">
            <avue-switch v-model="main.activeOption.labelShow">
            </avue-switch>
          </el-form-item>
          <el-form-item v-show="!!main.activeOption.labelShow" label="显示格式" :label="`${$t('components.main.DisplayFormat')}`">
            <avue-select :multiple="true" v-model="main.activeOption.labelShowFormat"
                         :dic="labelShowFormatList">
            </avue-select>
          </el-form-item>
          <el-form-item v-show="!!main.activeOption.labelShow" label="显示位置" :label="`${$t('components.main.DisplayPosition')}`">
            <avue-select  v-model="main.activeOption.labelShowPosition"
                         :dic="dicOption.labelShowPositionList">
            </avue-select>
          </el-form-item>
          <el-form-item label1="字体大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.labelShowFontSize">
            </avue-input-number>
          </el-form-item>
          <!-- </el-form-item> -->
          <el-form-item label1="字体颜色" :label="`${$t('components.main.FontColor')}`"
                        v-if="labelShowColorAuto!=true">
            <avue-input-color v-model="main.activeOption.labelShowColor">
            </avue-input-color>
          </el-form-item>
          <el-form-item label1="字体粗细" :label="`${$t('components.main.FontWeight')}`">
            <avue-select v-model="main.activeOption.labelShowFontWeight"
                         :dic="dicOption.fontWeight">
            </avue-select>
          </el-form-item>
        </el-collapse-item>
      </template>
      <!-- 提示语设置  -->
      <template v-if="main.validProp('tipList')">
        <el-collapse-item title="提示语设置" :title="`${$t('components.main.PromptSettings')}`">
          <el-form-item label="显示" :label="`${$t('components.main.Display')}`">
            <avue-switch v-model="main.activeOption.tipShow"></avue-switch>
          </el-form-item>
          <el-form-item label="字体大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.tipFontSize"></avue-input-number>
          </el-form-item>
          <el-form-item label="字体颜色" :label="`${$t('components.main.FontColor')}`">
            <avue-input-color v-model="main.activeOption.tipColor"></avue-input-color>
          </el-form-item>
          <el-form-item label="背景颜色" :label="`${$t('components.main.BackgroundColor')}`">
            <avue-input-number v-model="main.activeOption.tipBackgroundColor"></avue-input-number>
          </el-form-item>
        </el-collapse-item>
      </template>
      <!-- 轴距离设置 -->
      <template v-if="main.validProp('positionList')">
        <el-collapse-item title1="坐标轴边距设置" :title="`${$t('components.main.AxisMarginSettings')}`">
          <el-form-item label-width="150px" label1="左边距(像素)" :label="`${$t('components.main.LeftMargin')}`">
            <avue-slider v-model="main.activeOption.gridX"
                         :max="400"></avue-slider>
          </el-form-item>
          <el-form-item label-width="150px" label1="顶边距(像素)" :label="`${$t('components.main.TopMargin')}`">
            <avue-slider v-model="main.activeOption.gridY"
                         :max="400"></avue-slider>
          </el-form-item>
          <el-form-item label-width="150px" label1="右边距(像素)" :label="`${$t('components.main.RightMargin')}`">
            <avue-slider v-model="main.activeOption.gridX2"
                         :max="400"></avue-slider>
          </el-form-item>
          <el-form-item label-width="150px" label1="底边距(像素)" :label="`${$t('components.main.BottomMargin')}`">
            <avue-slider v-model="main.activeOption.gridY2"
                         :max="400"></avue-slider>
          </el-form-item>
        </el-collapse-item>
      </template>
      <!-- 图例设置 -->
      <template v-if="main.validProp('legendList')">
        <el-collapse-item title1="图例操作" :title="`${$t('components.main.LegendOperation')}`">
          <el-form-item label1="图例" :label="`${$t('components.main.Legend')}`">
            <avue-switch v-model="main.activeOption.legend"></avue-switch>
          </el-form-item>
          <el-form-item label1="独立颜色" :label="`${$t('components.main.IndependentColor')}`">
            <avue-switch v-model="main.activeOption.eachColor"></avue-switch>
          </el-form-item>
          <el-form-item label1="位置" :label="`${$t('components.main.Position')}`">
            <avue-select v-model="main.activeOption.legendPosition"
                         :dic="dicOption.textAlign">
            </avue-select>
          </el-form-item>
          <el-form-item label1="相对位置" :label="`${$t('components.main.RelativePosition')}`">
            <avue-select v-model="main.activeOption.legendRelativePosition"
                         :dic="dicOption.legendAlign">
            </avue-select>
          </el-form-item>
          <el-form-item label1="布局朝向" :label="`${$t('components.main.LayoutOrientation')}`">
            <avue-select v-model="main.activeOption.legendOrient"
                         :dic="dicOption.orientList">
            </avue-select>
          </el-form-item>
          <el-form-item label1="字体大小" :label="`${$t('components.main.FontSize')}`">
            <avue-input-number v-model="main.activeOption.legendFontSize">
            </avue-input-number>
          </el-form-item>
        </el-collapse-item>
      </template>
      <!-- 颜色设置 -->
      <template v-if="main.validProp('colorList')">
        <el-collapse-item title1="自定义配色/备注/类型" :title="`${$t('components.main.CustomColor')}`">
          <avue-crud :option="colorOption"
                     :data="main.activeOption.barColor||colorDataList"
                     @row-save="rowSave"
                     @row-del="rowDel"
                     @row-update="rowUpdate"></avue-crud>
        </el-collapse-item>
      </template>
    </el-collapse>
  </div>
</template>

<script>
import { dicOption, colorOption } from '@/option/config'
export default {
  name: 'main',
  inject: ["main"],
  data () {
    return {
      dicOption: dicOption,
      colorOption: colorOption,
      colorDataList:[],
    }
  },
  computed:{
    // 显示格式
    labelShowFormatList(){
      let dicData= [
          {
            label: '标题',
            value: '1'
          }, {
            label: '数字',
            value: '2'
          }, {
            label: '比例',
            value: '3'
          }]
      return dicData    
    }
  },
  methods: {
    rowSave (row, done) {
      let _self = this
      if(!Array.isArray(_self.main.activeOption.barColor)){
            _self.main.activeOption.barColor =[]
        }
     this.colorDataList = this.deepClone(this.main.activeOption.barColor)
      setTimeout(() => {
          _self.main.activeOption.barColor.push(_self.deepClone(row));
          _self.colorDataList.push(_self.deepClone(row))
          done()
        }, 100);
     },
    rowDel (row, index) {
      this.main.activeOption.barColor.splice(index, 1);
    },
    rowUpdate (row, index, done) {
      this.main.activeOption.barColor.splice(index, 1, this.deepClone(row));
      done()
    },
  }
}
</script>

<style>
</style>