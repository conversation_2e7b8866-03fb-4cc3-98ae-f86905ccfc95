import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    isGlobalfetching:false, // 全局数据源是否正在获取中
    lasGlobaltFetchTime:null, // 全局数据源上次获取时间
     // 设置全局 条件列表，控件之间共享数据
    globalConditionList:[],
    // 设置全局数据源，控件之间共享数据
    globalDataSource: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":dataList
      }
    },
     // 设置全局数据源，时间控制器
     globalDataTimer: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":dataList
      }
    },
 
  },
  mutations: {
     // 设置全局 全局数据源是否正在获取中
     set_isGlobalfetching(state, data) {
      state.isGlobalfetching = data
    },
     // 设置全局 全局数据源上次获取时间
     set_lasGlobaltFetchTime(state, data) {
      state.lasGlobaltFetchTime = data
    },
     // 设置全局 条件列表，控件之间共享数据
     set_globalConditionList(state, data) {
      state.globalConditionList = data
    },
    // 设置全局数据源，控件之间共享数据
    set_globalDataSource(state, data) {
      if(data.key){
        state.globalDataSource.key = Math.floor(Math.random() * 10000 + 1)
        state.globalDataSource.value[data.key] = data.value
      }
    
    },
      // 设置全局数据源，控件之间共享数据
      set_globalDataTimer(state, data) {
        if(data.key){
          //debugger
          state.globalDataTimer.key = Math.floor(Math.random() * 10000 + 1)
          if(data.value ==0){
            state.globalDataTimer.value[data.key] =0
          }else{
            state.globalDataTimer.value[data.key] =Number(state.globalDataTimer.value[data.key]?state.globalDataTimer.value[data.key]:0)+1 
          }
        
        }
       
      },
  },
  actions: {

  },
  modules: {
  }
})