<!-- 柱状图配置 -->
<template>
  <div>
    <el-form-item label1="竖展示" :label="`${$t('components.bar.VerticalDisplay')}`">
      <avue-switch type="textarea"
                   v-model="main.activeOption.category"></avue-switch>
    </el-form-item>
    <el-collapse accordion>
      <el-collapse-item title1="柱体设置" :title="`${$t('components.bar.BarSettings')}`">
        <el-form-item label1="最大宽度" :label="`${$t('components.bar.MaxWidth')}`">
          <avue-slider v-model="main.activeOption.barWidth">
          </avue-slider>
        </el-form-item>
        <el-form-item label1="圆角" :label="`${$t('components.bar.RoundCorner')}`">
          <avue-slider v-model="main.activeOption.barRadius">
          </avue-slider>
        </el-form-item>
        <el-form-item label1="最小高度" :label="`${$t('components.bar.MinHeight')}`">
          <avue-slider v-model="main.activeOption.barMinHeight">
          </avue-slider>
        </el-form-item>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'bar',
  inject: ["main"]
}
</script>

<style>
</style>