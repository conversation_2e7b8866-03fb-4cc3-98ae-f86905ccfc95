<template>
  <div class="mapManagement">
    <vxe-toolbar>
      <template #buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="insertEvent"
          >
          <!-- 新增 -->
          {{ $t('page.mapManagement.Add') }}
          </el-button
        >
        <el-upload
           
           style="margin: 0 10px;"
           :show-file-list="false"
           :httpRequest="httpRequest4"
           action=""
           ref="upload"
           accept=".json"
         >
         <el-button
         icon="el-icon-upload2
"
         type="primary"
         size="mini"
         @click="import_part"
         >
         <!-- 导入 -->
         {{ $t('page.mapManagement.Import') }}
         </el-button
       >
         </el-upload>

       <el-button
         icon="el-icon-download"
         type="primary"
         size="mini"
         @click="export_part"
         >
         <!-- 导出 -->
         {{ $t('page.mapManagement.Export') }}
         </el-button
       >  
              <el-button
              class="pinBtnOnCla"
              icon="el-icon-tickets"
              type="primary"
              size="small"
           
              @click="synchronous()"
            >
              <!-- 同步 -->
              {{ $t('page.mapManagement.Synchronize') }}
            </el-button>
           
      </template>
      <template #tools>
        <span style="color: #333; font-size: 12px">
          <!-- 关键字 -->
          {{ $t('page.mapManagement.Keyword') }}
        </span>
        <el-input
          style="width: 180px; margin: 0 10px"
          size="mini"
          @keyup.enter.native="onSubmit"
          v-model="formData.name"
          placeholder1="请输入关键字"
          :placeholder="`${$t('page.mapManagement.Keyword')}`"
        ></el-input>
        <el-button
          title="查询"
          :title="`${$t('page.mapManagement.Query')}`"
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click="onSubmit"
          >{{
        }}</el-button>
        <el-button
          title="重置"
          :title="`${$t('page.mapManagement.Reset')}`"
          size="mini"
          type="primary"
          icon="el-icon-refresh-right"
          @click="reset"
          >{{
        }}</el-button>
      </template>
    </vxe-toolbar>

    <vxe-table
      ref="xTable"
      border
      align="center"
      :loading="loading"
      show-overflow
      keep-source
      :data="tableData"
    >
    <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column type="seq" width="60" title="序号" :title="`${$t('page.mapManagement.SerialNumber')}`"></vxe-column>
      <vxe-column field="CNAME" title="地图名称" :title="`${$t('page.mapManagement.MapName')}`">
      </vxe-column>

   

      <vxe-column title="操作" :title="`${$t('page.mapManagement.Operation')}`" width="250" fixed="right">
        <template #default="{ row }">
         
          <el-button type="text" @click="EditData(row)">
            <!-- 编辑 -->
            {{ $t('page.mapManagement.Edit') }}
          </el-button>
          <el-button type="text"   style="color: rgb(245, 108, 108)" @click="deleterevertData(row)">
            <!-- 删除 -->
            {{ $t('page.mapManagement.Delete') }}
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>

    <Model ref="modelEvent" :title="title" :getList="getList" />
  </div>
</template>
<script>
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import {
 
  GetPageListByQueryJson,
  Update,
  Delete,
  Add,
  PushData,Import1,Export1
} from "./Api.js";
import { mapGetters } from "vuex";
import Model from "./model.vue";
//   import DatasetConfiguration from './datasetConfiguration.vue'
export default {
  name: "mapManagement",
  components: {
    Model,
  },
  data() {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      title: "",
      formData: {
        name: "",
      },
      CWORK_DATES: "",
      loading: true,
      dataTime: 0,
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
      projectoption: [],
      typeoptions: [
        { value: 0, name: "正常上班" },
        { value: 1, name: "工作日加班" },
        { value: 2, name: "周末加班" },
        { value: 3, name: "节假日加班" },
      ],
      useroptions: [],
      tableData: [],
      datasourList: [],
      defaultProps: {
        children: "children",
        label: "CNAME",
      },
      dataOptions: [],
      nodeID: 0,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    this.getdatasoures();
    
  },

  methods: {
     //同步
     synchronous(){
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
      PushData(ids).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
        }else{
          this.$message.error(res.data.Content)
        }
      })
    },
    httpRequest4(file){
   const reader = new FileReader();
 // 异步处理文件数据
 reader.readAsText(file.file, 'UTF-8');
 // 处理完成后立马触发 onload
 reader.onload = fileReader => {
     const fileData = fileReader.target.result;
     if(fileData){
      var text=JSON.parse(fileData)


      Import1(text).then(res=>{
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
          this.getdatasoures()

        }else{
          this.$message.error(res.data.Content)
        }
      })
     }
    
     // 上面的两个输出相同
 };

    },
    import_part() {
     // console.log(111);
    },
    export_part() {
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
     Export1(ids).then(res=>{
          res=res.data
        if(res.code===200&&res.data.Success){
        let link = document.createElement("a");
        link.download = "地图管理.json";
        link.href = "data:text/plain," + JSON.stringify(res.data.Datas);
        link.click();
        }else{
          this.$message.error(res.data.Content)
        }
     })
     
    },




    getList(){
        this.getdatasoures();
    },
    //请求地图接口
    getdatasoures() {
      this.loading=true
    
      
      const text = {
        condition:this.formData.name,
        start:1,
        length:10000
      };
    
      GetPageListByQueryJson(text).then((res) => {
          res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.tableData = res.data.Datas;
          this.loading=false
        } else {
          this.$message({
            message: res.message.msg||res.data.Content,
            type: "error",
          });
          this.loading=false
        }
     
      });
    },
   
    //新建
    insertEvent() {
      this.title = this.$t('page.mapManagement.CreateMap')//"新建地图";
      this.$refs.modelEvent.addshowEdit();
    },

  
    EditData(row) {
      this.title = this.$t('page.mapManagement.EditMap')//"编辑地图";
      this.$refs.modelEvent.appendshowEdit(true, row);
    },
    deleterevertData(row) {
      const options = {
        content: "您确定要删除这条数据吗？",
        size: "small",
        status: "error",
        iconStatus: "vxe-icon-error-circle-fill",
      };
      this.$VXETable.modal.confirm(options).then((type) => {
        if (type == "confirm") {
          const params = {
            CID: row.CID,
          };
          Delete(params).then((res) => {
            res = res.data;
            if (res.code == 200 && res.data.Success) {
              this.$message({
                message: `${this.$t('message.OperationSuccessful')}`,//'操作成功',
                type: "success",
              });
              this.getdatasoures();
            } else {
              this.$message({
                message: res.message.msg||res.data.Content,
                type: "error",
              });
            }
          });
        }
      });
    },

    onSubmit() {
    
      this.getdatasoures();
    },
    reset() {
      this.formData.name = "";
      this.getdatasoures();
    },

  },
};
</script>
<style lang="scss">
.mapManagement {
  height: calc(100vh - 140px);
  padding: 10px;
  margin: 10px;
  background: #fff;
  .vxe-table--body-wrapper {
    height: calc(100vh - 230px);
  }
}
</style>
