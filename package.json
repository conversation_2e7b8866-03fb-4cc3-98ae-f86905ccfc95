{"name": "avue-data", "version": "2.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "npm run lib && vue-cli-service build", "build:jar": "npm run lib && vue-cli-service build --mode web", "lib": "vue-cli-service build --target lib --name avueData --dest public/lib --filename index --formats umd-min ./src/page/view.vue", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@smallwei/avue": "^2.8.24", "axios": "^0.19.0", "browser-md5-file": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "core-js": "^3.4.4", "mitt": "^3.0.0", "crypto-js": "^4.0.0", "css-loader": "^3.4.1", "dayjs": "^1.10.5", "element-ui": "^2.13.0", "mockjs": "^1.1.0", "monaco-editor": "0.21.3", "vue": "^2.6.11", "vue-i18n": "^8.0.0", "vue-router": "3.1.0", "vue-seamless-scroll": "^1.1.23", "vue-sketch-ruler": "^1.0.3", "vuedraggable": "^2.23.2", "vuex": "^3.6.2", "vxe-table": "3.6.6", "xe-utils": "3.5.20"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-service": "^4.1.0", "babel-eslint": "^10.0.3", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "sass": "^1.45.2", "sass-loader": "8.0.0", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "browserslist": ["> 1%", "last 2 versions"]}