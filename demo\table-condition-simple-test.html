<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件 - 条件判断简单测试</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .title {
            color: #fff;
            text-align: center;
            font-size: 28px;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section-title {
            color: #fff;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #667eea;
            border-radius: 2px;
        }
        .table-wrapper {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .test-data {
            background: rgba(0,0,0,0.5);
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            color: #ccc;
            font-size: 14px;
        }
        .test-data h4 {
            color: #667eea;
            margin: 0 0 10px 0;
        }
        .data-item {
            margin: 5px 0;
            padding: 5px 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            display: inline-block;
            margin-right: 10px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .status-demo {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: #fff;
            font-size: 12px;
            margin: 2px;
        }
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-busy { background: #f39c12; }
        .status-idle { background: #95a5a6; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧪 表格插件条件判断功能测试</h1>
        
        <div class="controls">
            <button class="btn" onclick="runConditionTest()">🔬 运行条件测试</button>
            <button class="btn" onclick="showTestData()">📊 显示测试数据</button>
            <button class="btn" onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <!-- 测试结果显示区域 -->
        <div class="test-section">
            <div class="section-title">📋 条件判断测试结果</div>
            <div class="table-wrapper" id="testResults">
                <div style="color: #667eea; text-align: center; padding: 40px;">
                    点击"运行条件测试"开始测试
                </div>
            </div>
        </div>

        <!-- 测试数据展示 -->
        <div class="test-section">
            <div class="section-title">📊 测试数据说明</div>
            <div class="test-data">
                <h4>测试场景：</h4>
                <div>
                    <span class="data-item">分数 >= 90: 绿色背景</span>
                    <span class="data-item">分数 < 60: 红色背景</span>
                    <span class="data-item">状态 = "在线": 绿色字体</span>
                    <span class="data-item">状态 = "离线": 红色字体</span>
                    <span class="data-item">姓名包含"张": 蓝色背景</span>
                </div>
                
                <h4>状态示例：</h4>
                <div>
                    <span class="status-demo status-online">在线</span>
                    <span class="status-demo status-offline">离线</span>
                    <span class="status-demo status-busy">忙碌</span>
                    <span class="status-demo status-idle">空闲</span>
                </div>
            </div>
        </div>

        <!-- 多条件测试 -->
        <div class="test-section">
            <div class="section-title">🔀 多条件优先级测试</div>
            <div class="test-data">
                <h4>测试逻辑：</h4>
                <p>1. 主条件：分数 >= 90 显示绿色</p>
                <p>2. 扩展条件1：分数 < 60 显示红色（优先级更高）</p>
                <p>3. 扩展条件2：分数 >= 80 显示橙色（优先级最高）</p>
                <p><strong>预期结果：</strong>后面的条件会覆盖前面的条件样式</p>
            </div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 测试数据
        const testData = [
            { id: 1, name: '张三', score: 95, status: '在线', department: '技术部' },
            { id: 2, name: '李四', score: 85, status: '离线', department: '销售部' },
            { id: 3, name: '王五', score: 45, status: '在线', department: '市场部' },
            { id: 4, name: '张六', score: 92, status: '忙碌', department: '技术部' },
            { id: 5, name: '赵七', score: 78, status: '离线', department: '人事部' },
            { id: 6, name: '张八', score: 55, status: '在线', department: '财务部' },
            { id: 7, name: '孙九', score: null, status: '', department: '测试部' },
            { id: 8, name: '', score: '88', status: undefined, department: '运营部' }
        ];

        // 运行条件测试
        function runConditionTest() {
            console.log('🔬 开始运行条件判断测试...');
            
            // 模拟条件判断逻辑测试
            const testResults = [];
            
            testData.forEach((row, index) => {
                const result = {
                    rowIndex: index + 1,
                    name: row.name || '空值',
                    score: row.score,
                    status: row.status || '空值',
                    tests: []
                };
                
                // 测试分数条件
                if (row.score !== null && row.score !== undefined && !isNaN(row.score)) {
                    const score = parseFloat(row.score);
                    if (score >= 90) {
                        result.tests.push('分数>=90: ✅ 绿色背景');
                    } else if (score < 60) {
                        result.tests.push('分数<60: ✅ 红色背景');
                    } else {
                        result.tests.push('分数正常: ⚪ 无特殊样式');
                    }
                } else {
                    result.tests.push('分数异常: ⚠️ 空值或非数值');
                }
                
                // 测试状态条件
                if (row.status === '在线') {
                    result.tests.push('状态在线: ✅ 绿色字体');
                } else if (row.status === '离线') {
                    result.tests.push('状态离线: ✅ 红色字体');
                } else {
                    result.tests.push('状态其他: ⚪ 默认样式');
                }
                
                // 测试包含条件
                if (row.name && typeof row.name === 'string' && row.name.includes('张')) {
                    result.tests.push('姓名包含张: ✅ 蓝色背景');
                } else {
                    result.tests.push('姓名不含张: ⚪ 默认背景');
                }
                
                testResults.push(result);
            });
            
            displayTestResults(testResults);
        }

        // 显示测试结果
        function displayTestResults(results) {
            let html = '<div style="color: #fff;">';
            html += '<h3 style="color: #667eea; margin-bottom: 20px;">🧪 条件判断测试结果</h3>';
            
            results.forEach(result => {
                html += `<div style="background: rgba(255,255,255,0.1); margin: 10px 0; padding: 15px; border-radius: 5px;">`;
                html += `<h4 style="color: #667eea; margin: 0 0 10px 0;">第${result.rowIndex}行: ${result.name} (分数: ${result.score}, 状态: ${result.status})</h4>`;
                html += '<ul style="margin: 0; padding-left: 20px;">';
                result.tests.forEach(test => {
                    html += `<li style="margin: 5px 0;">${test}</li>`;
                });
                html += '</ul>';
                html += '</div>';
            });
            
            html += '</div>';
            document.getElementById('testResults').innerHTML = html;
            
            console.log('✅ 条件判断测试完成');
        }

        // 显示测试数据
        function showTestData() {
            console.log('📊 测试数据:', testData);
            alert('测试数据已输出到控制台，请按F12查看');
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = 
                '<div style="color: #667eea; text-align: center; padding: 40px;">测试结果已清空</div>';
            console.clear();
            console.log('🧪 表格插件条件判断功能测试');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 表格插件条件判断简单测试页面已加载');
            console.log('📊 测试数据已准备就绪，共', testData.length, '条记录');
        });
    </script>
</body>
</html>
