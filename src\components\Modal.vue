<template>
  <div class="hello">
    <vxe-modal
    
      style="overflow-y: auto"
      v-model="showEdit"
      :title="selectRow ? `${$t('page.classification.EditSave')}` : `${$t('page.classification.AddSave')}`"
      width="600"
      min-height="300"
      resize
      destroy-on-close
    >
      <template #default>
        <el-form
          size="small"
          class="el_form"
          :rules="formRules"
          ref="form"
          :model="formData"
          label-width="80px"
        >
  <el-form-item label="父级分组"  :label="`${$t('page.classification.ParentGrouping')}`">
        <el-select
              class="main-select-tree"
              ref="selectTree"
              v-model="formData.CPARENT_ID"
              style="width: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.CID"
                :label="item.CNAME"
                :value="item.CID"
                style="display: none"
              />
              <el-tree
                class="main-select-el-tree"
                ref="selecteltree"
                :data="modalData"
                node-key="CID"
                highlight-current
                :props="defaultProps"
                @node-click="handleNodeClick"
                :current-node-key="true"
                default-expand-all
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <i class=""></i> {{ data.CNAME }}</span
                >
              </el-tree>
            </el-select>
  </el-form-item>
          <el-form-item label="分组名称" :label="`${$t('page.classification.GroupingName')}`" prop="CNAME">
            <el-input
              v-model="formData.CNAME"
              placeholder="请输入分组名称"
              :placeholder="`${$t('page.classification.PleaseEnterGroupingName')}`"
            ></el-input>
          </el-form-item>
          <el-form-item label="分组描述" :label="`${$t('page.classification.GroupingDescription')}`">
            <el-input
              v-model="formData.CNO"
              placeholder="请输入分组描述"
               :placeholder="`${$t('page.classification.PleaseEnterGroupingDescription')}`"
            ></el-input>
          </el-form-item>
       
          <el-form-item style="text-align: right">
            <el-button @click="reset('form')">
              <!-- 取消 -->
              {{ $t('page.classification.Cancel') }}
            </el-button>
            <el-button type="primary" @click="submitEvent('form')"
              >
              <!-- 确定 -->
              {{ $t('page.classification.Confirm') }}
              </el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </vxe-modal>
  </div>
</template>

<script>

import { updateObj,kanbanGroupAddObj,getCategory } from "@/api/visual";
export default {
  name: "Modal",
  
  props: ['childEvent','treeToArray'],
  data() {
    return {
      selectRow: null,
      showEdit: false,
    
      formData: {
        CPARENT_ID:'',
        CNAME:'',
        CNO:''
      },
      formRules: {
        
        CNAME: [
          {
            required: true,
            message: "请输入名称",
            trigger: "blur",
          },
        ],
      },
      modalData:[],
      defaultProps: {
        children: "CHILDS",
        label: "CNAME",
      },
      options:[],
    };
  },
  mounted(){
    this.formRules= {
        CNAME: [
          {
            required: true,
            message: this.$t('rules.PleaseEnterName'),//"请输入名称",
            trigger: "blur",
          },
        ],
      }
  },
  methods: {
    traverse(node) {
  // 将当前节点添加到结果数组中
  result.push(node);

  // 遍历当前节点的子节点
  if (node.CHILDS) {
    for (let child of node.CHILDS) {
      // 设置当前节点的CSEQ
      child.CSEQ = result.length;

      // 设置当前节点的CPATH和CID_PATH
      child.CPATH = `${node.CPATH}/${child.CNAME}`;
      child.CID_PATH = `${node.CID_PATH}/${child.CID}`;

      // 递归遍历子节点
      traverse(child);
    }
  }
},
    
    getlist(){
      var params = {
          queryJson: "",
        };
       getCategory(params).then((res) => {
        res=res.data
          if (res.code === 200 && res.data.Success) {
           
              
            this.modalData = res.data.Datas;
           
            this.options = this.treeToArray
        
          }
        });
    },
   
    //获得焦点
    reset(formName){
      this.$refs[formName].resetFields();
      this.showEdit = false;
    },
    editEvent(row, bln) {
        console.log(row);
      this.getlist()    
      if(bln){
        this.formData = row
      }else{
        this.formData= {
        CPARENT_ID:'',
        CNAME:'',
        CNO:''
      }
    }
     
      this.selectRow = bln;
      this.showEdit = true;
    },
    handleNodeClick(node){
      this.formData.CPARENT_ID = node.CID;
      this.$refs.selectTree.blur();
    },
    submitEvent(formName) {
      if(this.formData.CPARENT_ID==''){
        this.formData.CPARENT_ID=0
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
         if(this.selectRow) {

          updateObj(this.formData).then(res=>{
            res=res.data
            if(res.code===200&&res.data.Success){
              this.childEvent()
              this.$message({
            message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
            type: "success",
          });
        
          this.showEdit = false;
         
            }else{
              this.$message({
            message: res.data.Content,
            type: "error",
          }); 
            }
          })
         }else{
          kanbanGroupAddObj(this.formData).then(res=>{
            res=res.data
            if(res.code===200&&res.data.Success){
              this.childEvent()
              this.$message({
            message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
            type: "success",
          });
            this.showEdit = false;
         
          
            }else{
              this.$message({
            message: res.data.Content,
            type: "error",
          }); 
            }
          })
         }
    
        
       
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">

.hello{
  .el-form-item__error{
    top:67%;

  }

.el_form{
  width: 500px;
  padding:0 30px;
}




}
</style>
