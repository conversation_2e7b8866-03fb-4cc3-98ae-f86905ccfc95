<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件 - Parameters和Headers绑定测试</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .header {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            color: #fff;
            backdrop-filter: blur(10px);
            border-radius: 12px;
            margin-bottom: 20px;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section-title {
            color: #69bfe7;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #69bfe7;
            border-radius: 2px;
        }
        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .control-group {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
        }
        .control-group h4 {
            color: #fff;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        .form-item {
            margin-bottom: 10px;
        }
        .form-item label {
            display: block;
            color: #ccc;
            font-size: 12px;
            margin-bottom: 5px;
        }
        .form-item input, .form-item textarea, .form-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: #fff;
            border-radius: 4px;
            font-size: 12px;
            box-sizing: border-box;
        }
        .form-item textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(45deg, #69bfe7, #5aa3d1);
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(105, 191, 231, 0.4);
        }
        .btn-success { background: linear-gradient(45deg, #67C23A, #5aa83a); }
        .btn-warning { background: linear-gradient(45deg, #E6A23C, #d1923c); }
        .btn-danger { background: linear-gradient(45deg, #F56C6C, #e85a5a); }
        .log-container {
            background: rgba(0,0,0,0.5);
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            color: #ccc;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .log-success { color: #67C23A; }
        .log-error { color: #F56C6C; }
        .log-warning { color: #E6A23C; }
        .log-info { color: #69bfe7; }
        .table-container {
            height: 400px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #67C23A; }
        .status-error { background: #F56C6C; }
        .status-warning { background: #E6A23C; }
        .status-info { background: #69bfe7; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Parameters和Headers绑定测试</h1>
        <p>测试修复后的参数和请求头绑定功能</p>
    </div>

    <!-- API数据源测试 -->
    <div class="test-section">
        <div class="section-title">🌐 API数据源 - Headers测试</div>
        <div class="test-controls">
            <div class="control-group">
                <h4>API配置</h4>
                <div class="form-item">
                    <label>API URL:</label>
                    <input type="text" id="apiUrl" value="https://jsonplaceholder.typicode.com/posts" placeholder="输入API地址">
                </div>
                <div class="form-item">
                    <label>请求方法:</label>
                    <select id="apiMethod">
                        <option value="get">GET</option>
                        <option value="post">POST</option>
                        <option value="put">PUT</option>
                        <option value="delete">DELETE</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>Headers (JSON格式):</label>
                    <textarea id="apiHeaders" placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'></textarea>
                </div>
                <div class="form-item">
                    <label>Parameters (JSON格式):</label>
                    <textarea id="apiParams" placeholder='{"page": 1, "limit": 10}'></textarea>
                </div>
                <button class="btn btn-success" onclick="testApiDataSource()">测试API数据源</button>
                <button class="btn btn-warning" onclick="updateApiConfig()">更新配置</button>
            </div>
            
            <div class="control-group">
                <h4>测试状态</h4>
                <div id="apiStatus">
                    <p><span class="status-indicator status-info"></span>等待测试...</p>
                </div>
                <button class="btn" onclick="clearApiLogs()">清空日志</button>
                <button class="btn btn-danger" onclick="simulateApiError()">模拟错误</button>
            </div>
        </div>
        
        <div class="log-container" id="apiLogs">
            <div class="log-entry log-info">[INFO] API测试日志将在这里显示...</div>
        </div>
    </div>

    <!-- 自定义数据源测试 -->
    <div class="test-section">
        <div class="section-title">🔧 自定义数据源 - Parameters测试</div>
        <div class="test-controls">
            <div class="control-group">
                <h4>自定义数据源配置</h4>
                <div class="form-item">
                    <label>数据集ID:</label>
                    <input type="number" id="customDataSetId" value="123" placeholder="输入数据集ID">
                </div>
                <div class="form-item">
                    <label>Parameters配置 (JSON数组):</label>
                    <textarea id="customParams" placeholder='[{"key": "userId", "value": "123"}, {"key": "status", "value": "active"}]'></textarea>
                </div>
                <button class="btn btn-success" onclick="testCustomDataSource()">测试自定义数据源</button>
                <button class="btn btn-warning" onclick="updateCustomConfig()">更新参数</button>
            </div>
            
            <div class="control-group">
                <h4>测试状态</h4>
                <div id="customStatus">
                    <p><span class="status-indicator status-info"></span>等待测试...</p>
                </div>
                <button class="btn" onclick="clearCustomLogs()">清空日志</button>
                <button class="btn btn-danger" onclick="simulateCustomError()">模拟参数错误</button>
            </div>
        </div>
        
        <div class="log-container" id="customLogs">
            <div class="log-entry log-info">[INFO] 自定义数据源测试日志将在这里显示...</div>
        </div>
    </div>

    <!-- 全局数据源测试 -->
    <div class="test-section">
        <div class="section-title">🌍 全局数据源 - Parameters测试</div>
        <div class="test-controls">
            <div class="control-group">
                <h4>全局数据源配置</h4>
                <div class="form-item">
                    <label>数据集ID:</label>
                    <input type="number" id="globalDataSetId" value="456" placeholder="输入数据集ID">
                </div>
                <div class="form-item">
                    <label>Parameters配置 (JSON数组):</label>
                    <textarea id="globalParams" placeholder='[{"key": "region", "value": "china"}, {"key": "type", "value": "report"}]'></textarea>
                </div>
                <div class="form-item">
                    <label>缓存时间 (毫秒):</label>
                    <input type="number" id="globalCacheTime" value="5000" placeholder="缓存时间">
                </div>
                <button class="btn btn-success" onclick="testGlobalDataSource()">测试全局数据源</button>
                <button class="btn btn-warning" onclick="updateGlobalConfig()">更新参数</button>
            </div>
            
            <div class="control-group">
                <h4>测试状态</h4>
                <div id="globalStatus">
                    <p><span class="status-indicator status-info"></span>等待测试...</p>
                </div>
                <button class="btn" onclick="clearGlobalLogs()">清空日志</button>
                <button class="btn btn-danger" onclick="clearGlobalCache()">清空缓存</button>
            </div>
        </div>
        
        <div class="log-container" id="globalLogs">
            <div class="log-entry log-info">[INFO] 全局数据源测试日志将在这里显示...</div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 日志记录函数
        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        // 更新状态指示器
        function updateStatus(statusId, message, type = 'info') {
            const statusElement = document.getElementById(statusId);
            const indicator = `<span class="status-indicator status-${type}"></span>`;
            statusElement.innerHTML = `<p>${indicator}${message}</p>`;
        }

        // API数据源测试
        function testApiDataSource() {
            addLog('apiLogs', '开始测试API数据源...', 'info');
            updateStatus('apiStatus', '正在测试API数据源...', 'warning');
            
            const url = document.getElementById('apiUrl').value;
            const method = document.getElementById('apiMethod').value;
            const headers = document.getElementById('apiHeaders').value;
            const params = document.getElementById('apiParams').value;
            
            // 验证配置
            if (!url) {
                addLog('apiLogs', '错误: API URL不能为空', 'error');
                updateStatus('apiStatus', 'API URL不能为空', 'error');
                return;
            }
            
            // 验证Headers格式
            let parsedHeaders = {};
            if (headers.trim()) {
                try {
                    parsedHeaders = JSON.parse(headers);
                    addLog('apiLogs', `Headers解析成功: ${JSON.stringify(parsedHeaders)}`, 'success');
                } catch (e) {
                    addLog('apiLogs', `Headers解析失败: ${e.message}`, 'error');
                    updateStatus('apiStatus', 'Headers格式错误', 'error');
                    return;
                }
            }
            
            // 验证Parameters格式
            let parsedParams = {};
            if (params.trim()) {
                try {
                    parsedParams = JSON.parse(params);
                    addLog('apiLogs', `Parameters解析成功: ${JSON.stringify(parsedParams)}`, 'success');
                } catch (e) {
                    addLog('apiLogs', `Parameters解析失败: ${e.message}`, 'error');
                    updateStatus('apiStatus', 'Parameters格式错误', 'error');
                    return;
                }
            }
            
            // 模拟API请求
            addLog('apiLogs', `发送${method.toUpperCase()}请求到: ${url}`, 'info');
            addLog('apiLogs', `请求头: ${JSON.stringify(parsedHeaders)}`, 'info');
            addLog('apiLogs', `请求参数: ${JSON.stringify(parsedParams)}`, 'info');
            
            // 模拟请求成功
            setTimeout(() => {
                addLog('apiLogs', 'API请求成功，数据绑定正常', 'success');
                updateStatus('apiStatus', 'API数据源测试通过', 'success');
            }, 1000);
        }

        function updateApiConfig() {
            addLog('apiLogs', '更新API配置...', 'warning');
            // 模拟配置更新
            setTimeout(() => {
                addLog('apiLogs', 'API配置更新成功，参数重新绑定', 'success');
            }, 500);
        }

        function clearApiLogs() {
            document.getElementById('apiLogs').innerHTML = '<div class="log-entry log-info">[INFO] 日志已清空</div>';
        }

        function simulateApiError() {
            addLog('apiLogs', '模拟API错误: 请求头格式不正确', 'error');
            updateStatus('apiStatus', 'Headers绑定失败', 'error');
        }

        // 自定义数据源测试
        function testCustomDataSource() {
            addLog('customLogs', '开始测试自定义数据源...', 'info');
            updateStatus('customStatus', '正在测试自定义数据源...', 'warning');
            
            const dataSetId = document.getElementById('customDataSetId').value;
            const params = document.getElementById('customParams').value;
            
            if (!dataSetId) {
                addLog('customLogs', '错误: 数据集ID不能为空', 'error');
                updateStatus('customStatus', '数据集ID不能为空', 'error');
                return;
            }
            
            // 验证Parameters格式
            let parsedParams = [];
            if (params.trim()) {
                try {
                    parsedParams = JSON.parse(params);
                    if (!Array.isArray(parsedParams)) {
                        throw new Error('Parameters必须是数组格式');
                    }
                    addLog('customLogs', `Parameters解析成功: ${JSON.stringify(parsedParams)}`, 'success');
                    
                    // 验证每个参数项
                    parsedParams.forEach((item, index) => {
                        if (!item.key || item.key.trim() === '') {
                            addLog('customLogs', `警告: 参数项[${index}]的key为空，将被跳过`, 'warning');
                        } else {
                            addLog('customLogs', `参数项[${index}]: ${item.key} = ${item.value}`, 'info');
                        }
                    });
                } catch (e) {
                    addLog('customLogs', `Parameters解析失败: ${e.message}`, 'error');
                    updateStatus('customStatus', 'Parameters格式错误', 'error');
                    return;
                }
            }
            
            // 模拟数据源请求
            addLog('customLogs', `请求数据集ID: ${dataSetId}`, 'info');
            addLog('customLogs', `发送参数: ${JSON.stringify(parsedParams)}`, 'info');
            
            setTimeout(() => {
                addLog('customLogs', '自定义数据源请求成功，参数绑定正常', 'success');
                updateStatus('customStatus', '自定义数据源测试通过', 'success');
            }, 1000);
        }

        function updateCustomConfig() {
            addLog('customLogs', '更新自定义数据源参数...', 'warning');
            setTimeout(() => {
                addLog('customLogs', '参数更新成功，重新绑定完成', 'success');
            }, 500);
        }

        function clearCustomLogs() {
            document.getElementById('customLogs').innerHTML = '<div class="log-entry log-info">[INFO] 日志已清空</div>';
        }

        function simulateCustomError() {
            addLog('customLogs', '模拟参数错误: 参数格式不正确', 'error');
            updateStatus('customStatus', 'Parameters绑定失败', 'error');
        }

        // 全局数据源测试
        function testGlobalDataSource() {
            addLog('globalLogs', '开始测试全局数据源...', 'info');
            updateStatus('globalStatus', '正在测试全局数据源...', 'warning');
            
            const dataSetId = document.getElementById('globalDataSetId').value;
            const params = document.getElementById('globalParams').value;
            const cacheTime = document.getElementById('globalCacheTime').value;
            
            if (!dataSetId) {
                addLog('globalLogs', '错误: 数据集ID不能为空', 'error');
                updateStatus('globalStatus', '数据集ID不能为空', 'error');
                return;
            }
            
            // 验证Parameters格式
            let parsedParams = [];
            if (params.trim()) {
                try {
                    parsedParams = JSON.parse(params);
                    if (!Array.isArray(parsedParams)) {
                        throw new Error('Parameters必须是数组格式');
                    }
                    addLog('globalLogs', `Parameters解析成功: ${JSON.stringify(parsedParams)}`, 'success');
                } catch (e) {
                    addLog('globalLogs', `Parameters解析失败: ${e.message}`, 'error');
                    updateStatus('globalStatus', 'Parameters格式错误', 'error');
                    return;
                }
            }
            
            // 模拟全局数据源请求
            addLog('globalLogs', `请求全局数据集ID: ${dataSetId}`, 'info');
            addLog('globalLogs', `缓存时间: ${cacheTime}ms`, 'info');
            addLog('globalLogs', `发送参数: ${JSON.stringify(parsedParams)}`, 'info');
            
            setTimeout(() => {
                addLog('globalLogs', '全局数据源请求成功，参数绑定正常', 'success');
                addLog('globalLogs', '数据已缓存，后续请求将使用缓存', 'info');
                updateStatus('globalStatus', '全局数据源测试通过', 'success');
            }, 1000);
        }

        function updateGlobalConfig() {
            addLog('globalLogs', '更新全局数据源参数...', 'warning');
            addLog('globalLogs', '清除旧缓存...', 'info');
            setTimeout(() => {
                addLog('globalLogs', '参数更新成功，缓存已清除', 'success');
            }, 500);
        }

        function clearGlobalLogs() {
            document.getElementById('globalLogs').innerHTML = '<div class="log-entry log-info">[INFO] 日志已清空</div>';
        }

        function clearGlobalCache() {
            addLog('globalLogs', '手动清除全局缓存...', 'warning');
            setTimeout(() => {
                addLog('globalLogs', '全局缓存已清除', 'success');
            }, 300);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Parameters和Headers绑定测试页面已加载');
            
            // 设置默认值
            document.getElementById('apiHeaders').value = '{\n  "Authorization": "Bearer test-token",\n  "Content-Type": "application/json",\n  "X-Custom-Header": "test-value"\n}';
            document.getElementById('apiParams').value = '{\n  "page": 1,\n  "limit": 10,\n  "search": "test"\n}';
            document.getElementById('customParams').value = '[\n  {"key": "userId", "value": "123"},\n  {"key": "status", "value": "active"},\n  {"key": "department", "value": "IT"}\n]';
            document.getElementById('globalParams').value = '[\n  {"key": "region", "value": "china"},\n  {"key": "type", "value": "report"},\n  {"key": "year", "value": "2024"}\n]';
            
            addLog('apiLogs', '页面初始化完成，可以开始测试', 'success');
            addLog('customLogs', '页面初始化完成，可以开始测试', 'success');
            addLog('globalLogs', '页面初始化完成，可以开始测试', 'success');
        });
    </script>
</body>
</html>
