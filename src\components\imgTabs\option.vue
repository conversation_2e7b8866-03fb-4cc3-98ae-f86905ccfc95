<!-- 自定义配置 -->
<template>
  <div>
    <el-form-item label="选项卡时间" :label="`${$t('components.imgList.TabTime')}`">
      <avue-input-number v-model="main.activeOption.time"></avue-input-number>
    </el-form-item>
    <el-form-item label="自动播放" :label="`${$t('components.imgList.Autoplay')}`">
      <avue-switch v-model="main.activeOption.autoplay"></avue-switch>
    </el-form-item>
    <el-form-item label="走马灯时间" :label="`${$t('components.imgList.CarouselTime')}`">
      <avue-input-number v-model="main.activeOption.interval"></avue-input-number>
    </el-form-item>
    <el-form-item label="走马灯方向" :label="`${$t('components.imgList.CarouselDirection')}`">
      <el-select v-model="main.activeOption.direction">
        <el-option key="horizontal"
                   label1="水平"
                   :label="`${$t('components.imgList.Horizontal')}`"
                   value="horizontal"></el-option>
        <el-option key="vertical"
                   label1="垂直"
                   :label="`${$t('components.imgList.Vertical')}`"
                   value="vertical"></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'imgTabs',
  inject: ["main"]
}
</script>

<style>
</style>