 <!-- 象形图配置 -->
<template>
  <div>
    <el-form-item label1="标题颜色" :label="`${$t('components.pictorialBar.TitleColor')}`">
      <avue-input-color v-model="main.activeOption.color"></avue-input-color>
    </el-form-item>
    <el-form-item label1="标题大小" :label="`${$t('components.pictorialBar.TitleSize')}`">
      <avue-input-number v-model="main.activeOption.fontSize"></avue-input-number>
    </el-form-item>
    <el-form-item label1="数值颜色" :label="`${$t('components.pictorialBar.ValueColor')}`">
      <avue-input-color v-model="main.activeOption.labelColor"></avue-input-color>
    </el-form-item>
    <el-form-item label1="数值大小" :label="`${$t('components.pictorialBar.ValueSize')}`">
      <avue-input-color v-model="main.activeOption.labelFontSize"></avue-input-color>
    </el-form-item>
    <el-form-item label1="图标" :label="`${$t('components.pictorialBar.Icon')}`">
      <img v-if="main.activeOption.symbol"
           :src="main.activeOption.symbol"
           alt=""
           width="100%" />
      <el-input v-model="main.activeOption.symbol">
        <div @click="main.handleOpenImg('activeOption.symbol')"
             slot="append">
          <i class="iconfont icon-img"></i>
        </div>
      </el-input>
    </el-form-item>
    <el-form-item label1="图标大小" :label="`${$t('components.pictorialBar.IconSize')}`">
      <avue-input-number v-model="main.activeOption.symbolSize"></avue-input-number>
    </el-form-item>
    <el-form-item label1="间距" :label="`${$t('components.pictorialBar.Spacing')}`">
      <avue-slider v-model="main.activeOption.split"></avue-slider>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'pictorialbar',
  inject: ["main"]
}
</script>

<style>
</style>