<!-- 地图配置 -->
<template>
  <div>
    <el-form-item label1="地图选择" :label="`${$t('components.map.MapSelection')}`">
      <avue-select :dic="main.DIC.MAP"
                   v-model="main.activeOption.mapData"
                   placeholder=""></avue-select>
    </el-form-item>
    <el-form-item label1="地图比例" :label="`${$t('components.map.MapScale')}`">
      <avue-slider v-model="main.activeOption.zoom"
                   :max="5"
                   :step="0.1"></avue-slider>
    </el-form-item>
    <el-form-item label1="地图缩放" :label="`${$t('components.map.MapZoom')}`">
      <avue-switch v-model="main.activeOption.roam"></avue-switch>
    </el-form-item>
    <el-collapse accordion>
      <el-collapse-item title1="轮播设置" :title="`${$t('components.map.CarouselSettings')}`">
        <el-form-item label="开启轮播" :label="`${$t('components.map.TurnonCarousel')}`">
          <avue-switch v-model="main.activeOption.banner"></avue-switch>
        </el-form-item>
        <template v-if="main.activeOption.banner">
          <el-form-item label1="轮播时间" :label="`${$t('components.map.CarouselTime')}`">
            <avue-input v-model="main.activeOption.bannerTime"></avue-input>
          </el-form-item>
        </template>
      </el-collapse-item>
      <el-collapse-item title1="基本设置" :title="`${$t('components.map.BasicSettings')}`">
        <el-form-item label="字体大小" :label="`${$t('components.map.FontSize')}`">
          <avue-input-number v-model="main.activeOption.fontSize"></avue-input-number>
        </el-form-item>
        <el-form-item label1="字体高亮颜色" :label="`${$t('components.map.FontHighlightColor')}`">
          <avue-input-color v-model="main.activeOption.empColor"></avue-input-color>
        </el-form-item>
        <el-form-item label1="字体颜色" :label="`${$t('components.map.FontColor')}`">
          <avue-input-color v-model="main.activeOption.color"></avue-input-color>
        </el-form-item>
        <el-form-item label1="边框颜色" :label="`${$t('components.map.BorderColor')}`">
          <avue-input-color v-model="main.activeOption.borderColor"></avue-input-color>
        </el-form-item>
      </el-collapse-item>
      <el-collapse-item title1="区域设置" :title="`${$t('components.map.RegionSettings')}`">
        <el-form-item label1="区域线" :label="`${$t('components.map.RegionLine')}`">
          <avue-input-number v-model="main.activeOption.borderWidth"></avue-input-number>
        </el-form-item>
        <el-form-item label1="区域颜色" :label="`${$t('components.map.RegionColor')}`">
          <avue-input-color v-model="main.activeOption.areaColor"></avue-input-color>
        </el-form-item>
        <el-form-item label1="区域高亮颜色" :label="`${$t('components.map.RegionHighlightColor')}`">
          <avue-input-color v-model="main.activeOption.empAreaColor"></avue-input-color>
        </el-form-item>
      </el-collapse-item>
      <el-collapse-item title1="提示设置" :title="`${$t('components.map.TipSettings')}`">
        <el-form-item label1="背景色" :label="`${$t('components.map.BackgroundColor')}`">
          <avue-input-color v-model="main.activeOption.tipBackgroundColor"></avue-input-color>
        </el-form-item>
        <el-form-item label1="文字颜色" :label="`${$t('components.map.TextColor')}`">
          <avue-input-color v-model="main.activeOption.tipColor"></avue-input-color>
        </el-form-item>
        <el-form-item label1="文字大小" :label="`${$t('components.map.TextSize')}`">
          <avue-input-color v-model="main.activeOption.tipFontSize"></avue-input-color>
        </el-form-item>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'map',
  inject: ["main"]
}
</script>

<style>
</style>