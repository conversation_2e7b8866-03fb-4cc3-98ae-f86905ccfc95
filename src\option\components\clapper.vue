<!-- clapper配置 -->
<template>
  <div>
    <el-form-item label="地址" :label="`${$t('components.video.Address')}`">
      <avue-input type="textarea"
                  v-model="main.activeObj.data.value"></avue-input>
    </el-form-item>
    <el-form-item label="自动播放" :label="`${$t('components.video.Autoplay')}`">
      <avue-switch v-model="main.activeOption.autoplay"></avue-switch>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'clapper',
  inject: ["main"]
}
</script>

<style>
</style>