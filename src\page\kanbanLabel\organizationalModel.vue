<template>
    <div class="datatypeModel">
      <vxe-modal v-model="showEdit" width="510" @close="close('form')">
        <template #title>
          <span>{{ title }}</span>
        </template>
        <template #default>
          <el-form
            size="small"
            class="el_form"
            :rules="rules"
            ref="form"
            :model="form"
            label-width="90px"
          >
            <!-- <el-form-item label="所属上级">
              <el-select 
                :disabled="form.CPARENT_ID == null" 
                class="main-select-tree"
                clearable filterable
                :filter-method="filterMethod"
                ref="selectTree"
                v-model="form.CPARENT_ID"
                style="width: 100%" 
              >
                <el-option
                  v-for="item in formOptions"
                  :key="item.CID"
                  :label="item.CNAME"
                  :value="item.CID"
                  style="display: none"
                />
                <el-tree
                  class="main-select-el-tree"
                  ref="selecteltree"
                  :data="delDepartTree(formOptions)"
                  node-key="CID"
                  highlight-current
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  :current-node-key="value"
                  :expand-on-click-node="expandOnClickNode"
                  :filter-node-method="filterNode"
                  default-expand-all
                >
                  <span class="custom-tree-node" slot-scope="{ node, data }">
                    <i class=""></i> {{ data.CNAME }}</span
                  >
                </el-tree>
              </el-select>
            </el-form-item> -->
            <el-form-item label="标签编号" :label="`${$t('page.kanbanLabel.LabelCode')}`" prop="CCODE">
              <el-input
                v-model="form.CCODE"
                placeholder="请输入标签编号"
                :placeholder="`${$t('page.kanbanLabel.PleaseEnterLabelNumber')}`"
              ></el-input>
            </el-form-item>
            <el-form-item label="标签名称" :label="`${$t('page.kanbanLabel.LabelName')}`" prop="CNAME">
              <el-input
                v-model="form.CNAME"
                placeholder="请输入标签名称"
                :placeholder="`${$t('page.kanbanLabel.PleaseEnterLabelName')}`"
              ></el-input>
            </el-form-item>

            <el-form-item label="标签备注" :label="`${$t('page.kanbanLabel.LabelRemarks')}`">
              <el-input
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 6 }"
                placeholder="请输入标签名称"
                :placeholder="`${$t('page.kanbanLabel.PleaseEnterLabelName')}`"
                v-model="form.CDESC"
              >
              </el-input>
            </el-form-item>
            
            <el-form-item style="text-align: right">
              <el-button @click="cancel('form')">
                <!-- 取消 -->
                {{ $t('page.kanbanLabel.Cancel') }}
              </el-button>
              <el-button type="primary" @click="submitForm('form')">
                <!-- 确定 -->
                {{ $t('page.kanbanLabel.Confirm') }}
              </el-button>
            </el-form-item>
          </el-form>
        </template>
      </vxe-modal>
    </div>
  </template>
  
  <script>
  import { kanbanLabelAdd, kanbanLabelUpdate } from "@/api/kanban";
  export default {
    name: "datatypeModel",
    props: ["childEvent", "title", "formOptions"],
    data() {
      return {
        showEdit: false,
        value: "",
        isEdit: null,
        expandOnClickNode: false,
        form:{
          "CCODE": "",
          "CNAME": "",
         // "CPARENT_ID": "",
          "CDESC": "",
          "CPATH":"",
          "CNAME_PATH":""
        },
        rules: {
          CCODE: [
          {
            required: true,
            message: "请输入编号",
            trigger: "blur",
          },
        ],
        CNAME: [
          {
            required: true,
            message: "请输入名称",
            trigger: "blur",
          },
        ]
        },
      
        defaultProps: {
          children: "children",
          label: "CNAME",
        },
        optionModule: [],
      };
    },
    mounted() {
      this.rules= {
          CCODE: [
          {
            required: true,
            message: this.$t('rules.PleaseEnterNumber'),//"请输入编号",
            trigger: "blur",
          },
        ],
        CNAME: [
          {
            required: true,
            message: this.$t('rules.PleaseEnterName'),//"请输入名称",
            trigger: "blur",
          },
        ]
        }
    },
    methods: {
      
      // 搜索逻辑
      filterMethod(value) {
        this.$refs.selecteltree.filter(value)
      },
      // tree节点过滤
      filterNode(value, data) {
          if (!value) return true
          return data.CNAME.indexOf(value) !== -1
      },
      // 请求企业组织类型
      // getType() {
      //   GetOrgLevelCode().then((res) => {
      //     if (res.code === 200 && res.data.Success) {
      //       this.optionModule = res.data.Datas;
      //     }
      //   });
      // },
      editEvent(value, row) {
        this.isEdit = value
        if (value) {
          this.form = JSON.parse(JSON.stringify(row));
          console.log(this.form);
          // if(this.form.CPARENT_ID === 0) this.form.CPARENT_ID = ""
        } else {
          this.form = {
            CCODE: "",
            CREMARK: "",
            CNAME: "",
          //  CPARENT_ID: "",
          };
        }
        this.showEdit = true;
      },
      handleNodeClick(node) {
        this.form.CNAME_PATH = '';
      //  this.form.CPARENT_ID = node.CID;
        if(node.CPATH != null) {
          if(node.CNAME_PATH == "") {
            this.form.CPATH = node.CID;
            this.form.CNAME_PATH = node.CNAME;
          }
          else{
            this.form.CPATH = node.CPATH+"\\" + node.CID;
            this.form.CNAME_PATH = node.CNAME_PATH+"\\" + node.CNAME;
          }
        }
        this.$refs.selectTree.blur();
      },
      // 取消
      cancel(formName) {
        this.$refs[formName].resetFields();
        this.showEdit = false;
      },
      close(formName) {
        this.$refs[formName].resetFields();
        this.showEdit = false;
      },
      //将数据处理成树状数据
      // delDepartTree(list) {
      //     // 1. 定义两个中间变量
      //     const treeList = [], // 最终要产出的树状数据的数组
      //     map = {}; // 存储映射关系
      //     list.forEach((item) => {
      //         item.children = [];
      //         map[item.CID] = item;
      //     });
      //     list.forEach(item => {
      //         const parent = map[item.CPARENT_ID]
      //         if(parent) {
      //             parent.children.push(item)
      //         } else {
      //             treeList.push(item)
      //         }
      //     })
  
      //     return treeList
      // },
      // delDepartTree(data) {
      //   const obj = {}; // 重新存储数据
      //   const res = []; // 存储最后结果
      //   const len = data.length;
      //   const id = "CID";
      //   const parentId = "CPARENT_ID";
      //   const children = "children";
      //   console.log(children);
      //   // 遍历原始数据data，构造obj数据，键名为id，值为数据
      //   for (let i = 0; i < len; i++) {
         
      //     obj[data[i][id]] = data[i];
      //   }
      //   // console.log(obj);
      //   // 遍历原始数据
      //   for (let j = 0; j < len; j++) {
      //     const list = data[j];
      //     // 通过每条数据的 pid 去obj中查询
      //     const parentList = obj[list[parentId]];
      //     console.log('22222',parentList[children]);
      //     if (parentList) {
      //       // 根据 pid 找到的是父节点，list是子节点，
      //       if (!parentList[children]) {
              
      //         parentList[children] = [];
      //       } 
      //       // 将子节点插入 父节点的 children 字段中
           
  
      //     } else {
      //       // pid 找不到对应值，说明是根结点，直接插到根数组中
      //       res.push(list);
      //     }
      //   }
      //   console.log(res);
      //   return res;
      // },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.form.CPARENT_ID = 0
              // if(JSON.parse(window.localStorage.userInfo)) {
              //   this.form.CENTERPRISE_CODE = JSON.parse(window.localStorage.userInfo).CENTERPRISE_CODE
              //   console.log( this.form.CENTERPRISE_CODE);
              // }
              if(this.isEdit) {
                kanbanLabelUpdate(this.form).then((res) => {
                  res=res.data
                  if (res.code == 200 && res.data.Success) {
                    this.$message({
                      message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
                      type: "success",
                    });
                    this.childEvent();
                    this.showEdit = false;
                  } else {
                    this.$message({
                      message: res.data.Content,
                      type: "error",
                    });
                  }
                });
              }else {
                kanbanLabelAdd(this.form).then((res) => {
                  res=res.data
                  if (res.code == 200 && res.data.Success) {
                    this.$message({
                      message: "新增成功",
                      type: "success",
                    });
                    this.childEvent();
                    this.showEdit = false;
                  } else {
                    this.$message({
                      message: res.data.Content,
                      type: "error",
                    });
                  }
                });
              }
         
            
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
    }
  };
  </script>
  
  <!-- Add "scoped" attribute to limit CSS to this component only -->
  <style lang="scss">
  .datatypeModel {
    .el_form {
      // padding: 0 40px;
      .el-form-item__error {
        top: 67%;
      }
      .el-input-group__append {
        padding: 0;
        span {
          padding: 0 15px;
          display: inline-block;
        }
      }
    }
  
    .main-select-el-tree {
      .custom-tree-node {
        font-size: 14px;
      }
    }
  }
  </style>
  