<template>
  <div>
    <el-container class="list">
      <div class="content_aside">
        <div class="moban" style="font-size: 14px">
          <!-- 看板分组 -->
           {{ $t('page.largescreendesign.KanbanGrouping') }}
        </div>
        <el-aside width="250px">
          <el-tree check-strictly style="height: 100%; background: #fff; transition: none" ref="selectTree"
            :data="dataTypeList" node-key="CID" highlight-current :props="defaultProps" @check="handleNodeClick"
            show-checkbox :getCheckedNodes="true" default-expand-all>
          </el-tree>
        </el-aside>
      </div>
      <el-container>
        <el-main class="contents">
          <div style="
              width: 100%;
              height: 40px;
              line-height: 39px;
              display: flex;
              justify-content: space-between;
            ">
            <div>
              &nbsp; &nbsp;<span style="color: #1989fa; cursor: pointer">
                <!-- 选择下面的方式进行创建 -->
                {{ $t('page.largescreendesign.Selectthefollowingmethodstocreate') }}
              </span>
            </div>
            <div style="display: flex;">
              <el-upload style="margin: 0 10px;" :show-file-list="false" :httpRequest="httpRequest4" action=""
                ref="upload" accept=".json">
                <el-button icon="el-icon-upload2
" type="primary" size="mini" @click="import_part">
<!-- 导入 -->
{{ $t('page.largescreendesign.Import') }}
</el-button>
              </el-upload>

              <el-button style="height: 27.8px;margin-top: 7px;line-height: 17px;" icon="el-icon-download"
                type="primary" size="mini" @click="export_part">
                <!-- 导出 -->
                {{ $t('page.largescreendesign.Export') }}
              </el-button>
              <el-button style="height: 27.8px;margin-top: 7px;" class="pinBtnOnCla" icon="el-icon-tickets"
                type="primary" size="small" @click="synchronous()">
                <!-- 同步 -->
                {{ $t('page.largescreendesign.Synchronize') }}
              </el-button>
              <span style="color: #333; font-size: 14px">&nbsp;&nbsp;
                <!-- 看板名称 -->
                {{ $t('page.largescreendesign.KanbanName') }}
                &nbsp; </span>
              <el-input placeholder1="请搜索模板名称" :placeholder="`${$t('page.largescreendesign.SearchTemplateName')}`" size="mini" @keyup.enter.native="searchs" style="width: 200px"
                v-model="search" clearable @clear="searchs">
              </el-input>
              &nbsp;
              <el-button style="height: 27.8px;margin-top: 7px;" type="primary" title1="查询" :title="`${$t('page.largescreendesign.Search')}`" size="mini"
                icon="el-icon-search" @click="searchs"></el-button>
              <el-button style="height: 27.8px;margin-top: 7px;" title1="重置" type="primary" :title="`${$t('page.largescreendesign.Reset')}`" size="mini"
                icon="el-icon-refresh-right" @click="reset"></el-button>
              &nbsp;
            </div>
          </div>
          <div class="content__box" style="height: calc(100vh - 207px)">
            <div @click="handleAdd" class="content__items"
              style="background: #ebeef5; text-align: center; margin-top: 0px">
              <i class="el-icon-plus" style="
                  font-size: 90px;
                  margin-top: 40px;
                  margin-bottom: 10px;
                  color: #c0c4cc;
                "></i>
              <div>
                <!-- 新建大屏 -->
                {{ $t('page.largescreendesign.CreateNewBigScreen') }}
              </div>
            </div>
            <div class="content__item" v-for="(item, index) in list" :key="index" @mouseover="item._menu = true"
              @mouseout="item._menu = false">
              <div class="content__info">
                <!-- img/bg.jpg -->
                <img v-if="item.Kanban.CBACKGROUND_URL"
                  :src="getDefaultImg(item)"
                  alt="" />
                <div class="content__menu" v-show="item._menu">
                  <div class="content__right">
                    <el-tooltip content="预览" :content="`${$t('page.largescreendesign.Preview')}`">
                      <i class="el-icon-view" @click="handleViews(item, index)"></i>
                    </el-tooltip>
                    <el-tooltip content="分享" :content="`${$t('page.largescreendesign.Share')}`">
                      <i class="el-icon-share" @click="handleUpdate(item, index)"></i>
                    </el-tooltip>
                  </div>

                  <div class="content__list">
                    <!-- <div class="content__btn" @click="handleEdit(item)">设计</div> -->
                    <div class="content__btns">
                      <i class="el-icon-rank" @click="handleEdit(item)" style="font-size: 25px"></i><br />
                      <span>
                        <!-- 设计 -->
                        {{ $t('page.largescreendesign.Design') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i style="font-size: 25px" class="el-icon-delete" @click="handleDel(item, index)"></i><br />
                      <span>
                        <!-- 删除 -->
                        {{ $t('page.largescreendesign.Delete') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i class="el-icon-edit" @click="handleupdate(item, index)" style="font-size: 25px"></i><br />
                      <span>
                        <!-- 编辑 -->
                        {{ $t('page.largescreendesign.Edit') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i style="font-size: 25px" class="el-icon-copy-document" @click="handleCopy(item, index)"></i>
                      <br /><span>
                        <!-- 复制 -->
                        {{ $t('page.largescreendesign.Copy') }}
                      </span>
                    </div>
                    <div class="content__btns">
                      <i style="font-size: 25px" class="el-icon-plus" @click="handleAddMoban(item, index)"></i>
                      <br /><span>
                        <!-- 添加模板 -->
                        {{ $t('page.largescreendesign.AddTemplate') }}
                      </span>
                    </div>
                    <!-- <el-tooltip content="修改">
                      <i
                        class="el-icon-edit"
                        @click="handleUpdate(item, index)"
                      ></i>
                    </el-tooltip> -->
                    <!-- <el-tooltip content="删除">
                      <i
                        class="el-icon-delete"
                        @click="handleDel(item, index)"
                      ></i>
                    </el-tooltip> -->
                    <!-- <el-tooltip content="复制">
                      <i
                        class="el-icon-copy-document"
                        @click="handleCopy(item, index)"
                      ></i>
                    </el-tooltip> -->
                  </div>
                </div>
              </div>
              <div class="content__main">
                <span :title="item.Kanban.CTITLE" class="content__name" style="width: 200px;">
                  <el-checkbox-group style="width:100%" v-model="checkList" @change="checkchang">
                    <el-checkbox :label="item.Kanban.CID">{{ item.Kanban.CTITLE }}</el-checkbox>

                  </el-checkbox-group>

                </span>
                <div class="content__menulist">
                  <div class="content__view"></div>
                  <span style="color: #666" class="content__status" :class="{ 'content__status--active': item.status }">
                    <!-- {{ item.status == 1 ? "已发布" : "未发布" }} -->
                  </span>
                </div>
              </div>
            </div>
            <div class="page_vxe">
              <vxe-pager align="right" background @page-change="pageChange" :current-page.sync="page.page"
                :page-size.sync="page.size" :total="page.total" :layouts="[
              'PrevJump',
              'PrevPage',
              'JumpNumber',
              'NextPage',
              'NextJump',
              'Sizes',
              'FullJump',
              'Total',
            ]"></vxe-pager>
            </div>
          </div>
        </el-main>
      </el-container>

      <el-dialog title="分享大屏" :title="`${$t('page.largescreendesign.ShareBigScreen')}`" width="50%" :close-on-click-modal="false" :visible.sync="box">
        <el-form style="width: 100%; margin-top: 8px" :model="ruleForm" ref="ruleForm" label-width="100px"
          class="demo-ruleForm">
          <el-form-item label="大屏名称" :label="`${$t('page.largescreendesign.BigScreenName')}`" prop="CTITLE">
            <el-input v-model="ruleForm.CTITLE"></el-input>
          </el-form-item>

          <el-form-item label="所属分组" :label="`${$t('page.largescreendesign.BelongingGroup')}`" prop="CGROUP_ID">
            <el-select filterable ref="selecteltree" style="width: 100%" v-model="ruleForm.CGROUP_ID" clearable
              placeholder="请选择所属模板" :placeholder="`${$t('page.largescreendesign.PleaseSelecttheBelongingTemplate')}`">
              <el-option style="display: none" v-for="item in optionList" :key="item.CID" :label="item.CNAME"
                :value="item.CID"></el-option>
              <el-tree class="main-select-el-tree" :data="dataTypeList" node-key="CID" highlight-current
                :props="defaultProps" @node-click="handleNodeClicks" :current-node-key="value"
                :expand-on-click-node="expandOnClickNode" default-expand-all>
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <i class=""></i> {{ data.CNAME }}</span>
              </el-tree>
            </el-select>
          </el-form-item>
          <el-form-item label="分享链接" :label="`${$t('page.largescreendesign.ShareLink')}`" prop="CTITLE">
            <el-input v-model="ruleForm.link" type="text" id="input" readonly></el-input>
            <!-- <span style=" cursor: pointer;" @click="handleViews()">查看大屏</span>  -->
            <span @click="onCopy" style="cursor: pointer; color: #1989fa">
              <!-- 复制链接 -->
               {{ $t('page.largescreendesign.CopyLink') }}
            </span>
          </el-form-item>
          <el-form-item label="分享密码" :label="`${$t('page.largescreendesign.SharePassword')}`">
            <el-input v-model="ruleForm.CPASSWORD"></el-input>
          </el-form-item>
          <el-form-item align="center">
            <el-button size="small" type="primary" icon="el-icon-check"
              @click="onsubmit">
              <!-- 分享大屏 -->
              {{ $t('page.largescreendesign.ShareBigScreen') }}
            </el-button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-container>
    <vxe-modal v-model="showbox" width="600" @close="cancel()">
      <template #title>
        <!-- <span>{{ titles }}</span> -->
        <span>{{ $t(titles) }}</span>
      </template>
      <template #default>
        <el-form style="width: 95%; margin-top: 8px" :model="ruleForms" ref="ruleForms" label-width="100px"
          :rules="rules" class="demo-ruleForm1s">
          <el-form-item label="大屏名称" :label="`${$t('page.largescreendesign.BigScreenName')}`" prop="CTITLE">
            <el-input v-model="ruleForms.CTITLE" placeholder="请输入大屏名称" :placeholder="`${$t('page.largescreendesign.PleaseEnterBigScreenName')}`"></el-input>
          </el-form-item>

          <el-form-item label="所属分组" :label="`${$t('page.largescreendesign.BelongingGroup')}`" prop="CGROUP_ID">
            <el-select filterable style="width: 100%" v-model="ruleForms.CGROUP_ID" clearable ref="selecteltree"
              placeholder="请选择所属分组" :placeholder="`${$t('page.largescreendesign.PleaseSelectBelongingGroup')}`">
              <el-option style="display: none" v-for="item in optionList" :key="item.CID" :label="item.CNAME"
                :value="item.CID"></el-option>

              <el-tree class="main-select-el-tree" :data="dataTypeList" node-key="CID" highlight-current
                :props="defaultProps" @node-click="handleNodeClicks" :current-node-key="value"
                :expand-on-click-node="expandOnClickNode" default-expand-all>
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <i class=""></i> {{ data.CNAME }}</span>
              </el-tree>
            </el-select>
          </el-form-item>
          <el-form-item label="大屏宽度" :label="`${$t('page.largescreendesign.BigScreenWidth')}`">
            <el-input v-model="ruleForms.CWIDTH" placeholder="请输入大屏宽度" :placeholder="`${$t('page.largescreendesign.PleaseEnterBigScreenWidth')}`"></el-input>
          </el-form-item>
          <el-form-item label="大屏高度" :label="`${$t('page.largescreendesign.BigScreenHeight')}`">
            <el-input v-model="ruleForms.CHEIGHT" placeholder="请输入大屏高度" :placeholder="`${$t('page.largescreendesign.PleaseEnterBigScreenHeight')}`"></el-input>
          </el-form-item>

          <el-form-item align="right">
            <el-button size="small" @click="() => {
                showbox = false;
              }
              ">
              <!-- 取消 -->
              {{ $t('page.largescreendesign.Cancel') }}
            </el-button>&nbsp;<el-button size="small" type="primary" @click="ONOK('ruleForms')">
              <!-- 确认 -->
              {{ $t('page.largescreendesign.Confirm') }}
            </el-button>
          </el-form-item>
        </el-form>
      </template>
    </vxe-modal>
    <vxe-modal v-model="showboban" width="600" @close="cancel()">
      <template #title>
        <span>
          <!-- 添加模板 -->
          {{ $t('page.largescreendesign.AddTemplate') }}
        </span>
      </template>

      <template #default>
        <el-form style="width: 95%; margin-top: 8px" :model="dataForm" ref="ruleForms1" label-width="100px"
          :rules="datarules" class="demo-ruleForm1s">
          <el-form-item label="大屏名称" :label="`${$t('page.largescreendesign.BigScreenName')}`" prop="CTITLE">
            <el-input v-model="dataForm.CTITLE" placeholder="请输入大屏名称" :placeholder="`${$t('page.largescreendesign.PleaseEnterBigScreenName')}`"></el-input>
          </el-form-item>

          <el-form-item label="所属标签" :label="`${$t('page.largescreendesign.BelongingLabel')}`" prop="CKANBAN_LABEL_ID">
            <el-select filterable style="width: 100%" v-model="dataForm.CKANBAN_LABEL_ID" placeholder="请选择标签" :placeholder="`${$t('page.largescreendesign.PleaseSelectLabel')}`">
              <el-option v-for="item in labeList" :key="item.CID" :label="item.CNAME"
                :value="item.CID"></el-option></el-select>
          </el-form-item>
          <el-form-item label="大屏宽度" :label="`${$t('page.largescreendesign.BigScreenWidth')}`">
            <el-input v-model="dataForm.CWIDTH" placeholder="请输入大屏宽度" :placeholder="`${$t('page.largescreendesign.PleaseEnterBigScreenWidth')}`"></el-input>
          </el-form-item>
          <el-form-item label="大屏高度" :label="`${$t('page.largescreendesign.BigScreenHeight')}`">
            <el-input v-model="dataForm.CHEIGHT" placeholder="请输入大屏高度" :placeholder="`${$t('page.largescreendesign.PleaseEnterBigScreenHeight')}`"></el-input>
          </el-form-item>

          <el-form-item align="right">
            <el-button size="small" @click="() => {
                showboban = false;
              }
              ">
              <!-- 取消 -->
              {{ $t('page.largescreendesign.Cancel') }}
            </el-button>&nbsp;<el-button size="small" type="primary"
              @click="mobanONOK('ruleForms')">
              <!-- 确认 -->
              {{ $t('page.largescreendesign.Confirm') }}
            </el-button>
          </el-form-item>
        </el-form>
      </template>
    </vxe-modal>
  </div>
</template>
<script>
import {
  getList,
  updateObj,
  delObj,
  getCategory,
  addObj,
  getall,
  AddTemplate,
  GetPageListByQueryJson,
  updateaddObj,
  PushData2, Import3, Export3
} from "@/api/visual";
import { url as serverUrl } from "@/config";
//  import { setLocalStorageStore, getLocalStorageStore,getCookie } from '@/utils/setStore'
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import { getObj } from "@/api/visual";
import { url } from "@/config";
export default {
  name: "list",
  data() {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      serverUrl: serverUrl,
      showboban: false,
      titles: "",
      showbox: false,
      ruleForms: {
        CTITLE: "",

        CGROUP_ID: "",

        CWIDTH: "1920",
        CHEIGHT: "1080",
      },
      dataForm: {
        CTITLE: "",
        CKANBAN_LABEL_ID: "",
        CGROUP_ID: "",
        CWIDTH: "1920",
        CHEIGHT: "1080",
      },
      datarules: {
        CTITLE: [
          {
            required: true,
            message: "请输入大屏名称",
            trigger: "blur",
          },
        ],
        CKANBAN_LABEL_ID: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      rules: {
        CTITLE: [
          {
            required: true,
            message: "请输入大屏名称",
            trigger: "blur",
          },
        ],
        CGROUP_ID: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      ruleForm: {
        CTITLE: "",
        CPASSWORD: "",
        CGROUP_ID: "",
        CBACKGROUND_URL: "",
        CWIDTH: "1920",
        CHEIGHT: "1080",
        link: "",
      },
      search: "",
      expandOnClickNode: false,
      page2: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
      dataTypeList: [],
      defaultProps: {
        label: "CNAME",
        children: "CHILDS",
      },
      typeList: [],
      box: false,

      page: {
        page: 1,
        size: 20,
        total: 0,
      },
      form: {},
      activeName: "",
      checkList: [],
      list: [],
      optionList: [],
      mobanconfig: "",
      itemConfig: null
    };
  },
  mounted() {
    this.useLanguageSettingFn.setLanguage()
    this.getAll();
    this.$eventBus.$on("updateKanBanList", () => {
      this.getList();
    });
    ///////
     this.datarules= {
        CTITLE: [
          {
            required: true,
            message: this.$t('rules.PleaseEnterBigScreenName'),//"请输入大屏名称",
            trigger: "blur",
          },
        ],
        CKANBAN_LABEL_ID: [
          {
            required: true,
            message: this.$t('rules.PleaseSelect'),//"请选择",
            trigger: "change",
          },
        ],
      }
      this.rules= {
        CTITLE: [
          {
            required: true,
            message: this.$t('rules.PleaseEnterBigScreenName'),//"请输入大屏名称",
            trigger: "blur",
          },
        ],
        CGROUP_ID: [
          {
            required: true,
            message: this.$t('rules.PleaseSelect'),//"请选择",
            trigger: "change",
          },
        ],
      }
  },
  created() {
    this.getCategory();
  },

  methods: {
    // 设置默认的语言
    // setDefaultLanguage(){
    //   let _language= getLocalStorageStore('language')
    //   if(_language=='en'){
    //       this.$i18n.locale = 'en'
    //   }else{
    //      this.$i18n.locale = 'zh'
    //   }
    // },
    getDefaultImg(item){
      let _url = item.Kanban.CBACKGROUND_URL.indexOf('http') > -1 ? item.Kanban.CBACKGROUND_URL : (serverUrl + item.Kanban.CBACKGROUND_URL.replace('/', ''))
      if(item.Kanban.CBACKGROUND_URL.includes('img/bg.jpg')){
        _url =item.Kanban.CBACKGROUND_URL
      }
      return _url
    },
    //同步
    synchronous() {

      PushData2(this.checkList).then(res => {
        res = res.data
        if (res.code === 200 && res.data.Success) {
          this.$message.success(res.data.Content)
        } else {
          this.$message.error(res.data.Content)
        }
      })
    },
    httpRequest4(file) {
      const reader = new FileReader();
      // 异步处理文件数据
      reader.readAsText(file.file, 'UTF-8');
      // 处理完成后立马触发 onload
      reader.onload = fileReader => {
        const fileData = fileReader.target.result;
        if (fileData) {
          var text = JSON.parse(fileData)


          Import3(text).then(res => {
            res = res.data
            if (res.code === 200 && res.data.Success) {
              this.$message.success(res.data.Content)
              this.getList()

            } else {
              this.$message.error(res.data.Content)
            }
          })
        }

        // 上面的两个输出相同
      };

    },
    import_part() {
   //   console.log(111);
    },
    export_part() {

      Export3(this.checkList).then(res => {
        res = res.data
        if (res.code === 200 && res.data.Success) {
          let link = document.createElement("a");
          link.download = "看板设计.json";
          link.href = "data:text/plain," + JSON.stringify(res.data.Datas);
          link.click();
        } else {
          this.$message.error(res.data.Content)
        }
      })

    },
    //添加组件
    mobanONOK() {
      this.mobanconfig.Kanban = this.dataForm;
      AddTemplate(this.mobanconfig).then((res) => {
        res = res.data;
        if (res.code == 200 && res.data.Success) {
          this.$message({
            message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
            type: "success",
          });
          this.showboban = false;
          this.$eventBus.$emit("updateMoBanList", "更新模板列表");
        } else {
          this.$message({
            message: res.data.Content || res.message.msg,
            type: "error",
          });
        }
      });
    },
    //获取模板库标签
    getAll() {
      getall({
        start: 1,
        length: 1000,
        condition: "",
      }).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.labeList = [
            {
              CID: 0,
              CNAME: "全部",
            },
            ...res.data.Datas,
          ];
        }
      });
    },
    cancel() {
    //  console.log(111);
      this.showbox = false;
      this.showboban = false;
    },
    //看板编辑
    handleupdate(item, index) {
      this.titles = "编辑大屏";
      this.ruleForms = item.Kanban;
      this.showbox = true;
    },
    onsubmit() {
      this.$message.success(`${this.$t('message.SharedSuccessfully')}`);//"分享成功"
      this.box = false;
    },
    //分享复制
    onCopy() {
      document.getElementById("input").select();
      document.execCommand("Copy");
      document.getElementById("input").blur();
    },
    //添加模板
    handleAddMoban(item) {
      this.mobanconfig = JSON.parse(JSON.stringify(item));
      this.dataForm = JSON.parse(JSON.stringify(item.Kanban));
      this.showboban = true;
    },
    pageChange(val) {
      //console.log(val);
      this.page.page = val.currentPage;
      this.page.size = val.pageSize;
      this.getList();
    },
    //搜索看板列表
    searchs() {
      this.page.page = 1 // 重置分页为1 查询时
      var params = {
        queryJson: { keyword: this.search, ids: this.$refs.selectTree.getCheckedKeys() },
        start: this.page.page,
        length: this.page.size,
        type: 0,
      };

      GetPageListByQueryJson(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.list = res.data.Datas;
          this.page.total = res.data.TotalRows;
          this.initData();
        }
      });
    },
    //重置
    reset() {
      this.search = "";
      this.page.page = 1;
      this.getList();
    },
    handleSelect(key) {
      this.activeName = key;
      this.page.page = 1;
      this.getList();
    },
    TreeToArray(tree) {
      // 判断 tree 是否有值，无返回 []
      if (!Array.isArray(tree) || !tree.length) return [];
      let res = [];
      tree.forEach((v) => {
        // tree的每个元素都 放入到 res里面
        res.push(v);
        if (v.CHILDS) {
          // 有children 就把 children数据递归 返回  依次放到 res里面
          res.push(...this.TreeToArray(v.CHILDS));
        }
      });
      return res;
    },

    //下拉树模板
    handleNodeClicks(node) {
      // console.log(node);
      this.ruleForm.CGROUP_ID = node.CID;
      this.ruleForms.CGROUP_ID = node.CID;
      this.$refs.selecteltree.blur();
    },
    //根据模板id筛选
    handleNodeClick(currentObj, treeStatus) {
      /////////////////////
        //debugger
        // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
        // let selected = treeStatus.checkedKeys.indexOf(currentObj.CID)
        //不等于-1当前是选中状态
        // if (selected !== -1) {
        //   this.selectedChildren(currentObj, true)
        //   // this.selectedParent(currentObj)
        //   // //统一处理子节点为相同的勾选状态
        //   // this.uniteChildSame(currentObj, true)
        // }else {
        //   //等于 -1 是取消选中状态
        //   //this.uniteChildSame(currentObj, false)
        //   this.selectedChildren(currentObj, false)
        // }
      /////////////////////
      this.page.page = 1 // 重置分页为1 查询时
      var params = {
        queryJson: { keyword: this.search, ids: this.$refs.selectTree.getCheckedKeys() },
        start: this.page.page,
        length: this.page.size,
        type: 0,
      };

      GetPageListByQueryJson(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.list = res.data.Datas;
          this.page.total = res.data.TotalRows;

          this.initData();
        }
      });
    },
    //////////////////////////
    uniteChildSame(treeList, isSelected) {
      //console.log(isSelected);
      this.$refs["selectTree"].setChecked(treeList.id, isSelected)
      if (treeList.children) {
        for (let i = 0; i < treeList.children.length; i++) {
          this.uniteChildSame(treeList.children[i], isSelected)
        }
      }
    },
    selectedParent(currentObj) {
      //debugger
      let currentNode = this.$refs["selectTree"].getNode(currentObj)
      //console.log(currentNode);
      if (currentNode.parent.key !== undefined) {
        this.$refs["selectTree"].setChecked(currentNode.parent, true)
        this.selectedParent(currentNode.parent)
      }
    },
    selectedChildren(currentObj,isSelected,isInner=false) {
      //debugger
      if(!isInner){
        let currentNode = this.$refs["selectTree"].getNode(currentObj)
        if (currentNode.childNodes  && currentNode.childNodes.length > 0) {
          for (let i = 0; i < currentNode.childNodes.length; i++) {
            let _nodeItem = currentNode.childNodes[i]
            _nodeItem.checked = isSelected
            if(_nodeItem.childNodes && _nodeItem.childNodes.length > 0){
              this.selectedChildren(_nodeItem.childNodes,isSelected,true)
            }
          }
        }
      }else{
        if(currentObj && currentObj.length > 0){
            for (let j = 0; j < currentObj.length; j++) {
              let _nodeItem = currentObj[j]
              _nodeItem.checked = isSelected
            }
        }
      }
     
    },
    //////////////////////////
    //编辑保存
    ONOK(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.titles == "编辑大屏") {
            updateaddObj(this.ruleForms).then((res) => {
              res = res.data;
              if (res.code == 200 && res.data.Success) {
                this.$message({
                  message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
                  type: "success",
                });
                this.getList();
                this.showbox = false;
              } else {
                this.$message({
                  message: res.data.Content || res.message.msg,
                  type: "error",
                });
              }
            });
          } else {

            addObj(this.ruleForms, this.itemConfig).then((res) => {
              res = res.data;
              if (res.code == 200 && res.data.Success) {
                this.$message({
                  message: `${this.$t('message.SavedSuccessfully')}`,//"保存成功"
                  type: "success",
                });
                this.getList();
                this.showbox = false;
              } else {
                this.$message({
                  message: res.data.Content || res.message.msg,
                  type: "error",
                });
              }
            });
          }
        } else {
        //  console.log("error submit!!");
          return false;
        }
      });
    },
    vaildData(id) {
      const list = [];
      for (var i = 0; i < 20; i++) {
        list.push(i);
      }
      return list.includes(id);
    },
    //获得模板数据
    getCategory() {
      const params = {
        queryJson: "",
      };
      getCategory(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.dataTypeList = res.data.Datas;
          this.optionList = this.TreeToArray(res.data.Datas);
         // console.log(this.optionList);

          //this.option.column[0].dicData = res.data.Datas || [];
        }
        this.getList();
      });
    },

    // handleExport(item) {
    //   getObj(item.id).then((res) => {
    //     // this.$message.success("大屏导出成功");
    //     this.$message.success("大屏分享成功");
    //     const data = res.data.data;
    //     let mode = {
    //       detail: JSON.parse(data.config.detail),
    //       component: JSON.parse(data.config.component),
    //     };
    //     var zip = new window.JSZip();
    //     zip.file("view.js", `const option =${JSON.stringify(mode, null, 4)}`);
    //     zip.file(
    //       "说明.txt",
    //       `把view.js替换index.zip中文件即可,运行index.html即可看到效果`
    //     );
    //     zip.generateAsync({ type: "base64" }).then((content) => {
    //       this.downFile("data:application/zip;base64," + content, "view.zip");
    //       // setTimeout(() => {
    //       //   location.href = "/index.zip";
    //       // }, 1000);
    //     });
    //   });
    // },
    handleCopy(item) {
      // if (this.$website.isDemo) {
      //   this.$message.error(this.$website.isDemoTip)
      //   return
      // }handleEdit
    //  console.log(item);
      this.titles = "复制大屏";
      this.ruleForms = item.Kanban;
      this.itemConfig = JSON.parse(JSON.stringify(item.KanbanConfig))
      this.showbox = true;
      // copyObj(item.Kanban.CID).then((res) => {
      //       res = res.data;
      //       this.$message.success("复制成功");
      //       const id = res.data.data;
      //       this.handleEdit(item);
      //     });
      // this.$confirm("确认复制当前大屏", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      //   .then(() => {
      //     copyObj(item.Kanban.CID).then((res) => {
      //       res = res.data;
      //       this.$message.success("复制成功");
      //       const id = res.data.data;
      //       this.handleEdit(item);
      //     });
      //   })
      //   .catch(() => {});
    },
    //删除
    handleDel(item, index) {
      this.$confirm(`${this.$t('message.PermanentDeletion')}`, `${this.$t('message.Prompt')}`, {
        confirmButtonText: `${this.$t('message.confirmButtonText')}`,
        cancelButtonText: `${this.$t('message.cancelButtonText')}`,
        // type: "error",
        iconClass: "el-icon-error",
      })
        .then(() => {
          delObj({ CID: item.Kanban.CID }).then((res) => {
            res = res.data;
            if (res.code === 200 || res.data.Success) {
              this.$message.success(`${this.$t('message.DeletedSuccessfully')}`);//"删除成功"
              this.page.page = 1;
              this.getList();
            } else {
              this.$message.error(res.message.msg);
            }
          });
        })
        .catch(() => { });
    },
    //新建大屏
    handleAdd() {
      this.$router.push({
        path: "/create",
        query: {
          category: this.activeName,
        },
      });
    },
    //分享
    handleUpdate(item, index) {
      let routeUrl = this.$router.resolve({
        path: "/view/" + item.Kanban.CID,
      });
      this.ruleForm = item.Kanban;
      this.ruleForm.link = window.__POWERED_BY_QIANKUN__
        ? window.location.origin + `/subapp${routeUrl.href}`
        : window.location.origin + routeUrl.href;
      this.box = true;
    },
    //大屏设计
    handleEdit(item) {
      let routeUrl = this.$router.resolve({
        path: "/build/" + item.Kanban.CID,
      });
      window.__POWERED_BY_QIANKUN__
        ? window.open(`/subapp${routeUrl.href}`, "_blank")
        : window.open(routeUrl.href, "_blank");
    },
    handleViews(item) {
      let routeUrl = this.$router.resolve({
        path: "/view/" + item.Kanban.CID,
      });
      window.__POWERED_BY_QIANKUN__
        ? window.open(`/subapp${routeUrl.href}`, "_blank")
        : window.open(routeUrl.href, "_blank");
      // window.open(routeUrl.href, "_blank");
    },
    handleSave(form, done) {
      updateObj({
        id: this.form.id,
        category: this.form.category,
        password: this.form.password,
        status: this.form.status,
        title: this.form.title,
      }).then(() => {
        done();
        this.$message.success(`${this.$t('message.ModifiedSuccessfully')}`);//"修改成功"
        this.getList();
      });
    },

    //请求大屏列表数据
    getList() {
      const params = {
        //queryJson: "",
        queryJson: { keyword: this.search, ids: this.$refs.selectTree.getCheckedKeys() },
        start: this.page.page,
        length: this.page.size,
        type: 0,
      };
      this.list = [];
      GetPageListByQueryJson(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.list = res.data.Datas;
          this.page.total = res.data.TotalRows;

          this.initData();
        }
      });
    },
    initData() {
      this.list.forEach((ele, index) => {
        this.$set(this.list[index], "_menu", false);
      });
    },
  },
};
</script>
<style lang="scss">
@import "@/styles/list.scss";

.demo-ruleForm1s .el-form-item__error {
  line-height: 5px;
}

.content_aside {
  margin: 10px;

  background: #fff;
  height: calc(100vh - 174px);
}

.el-tree-node__label {
  height: 20px !important;
  line-height: 20px !important;
  width: 200px;
}

.page_vxe {
  position: absolute;
  bottom: 12px;
  right: 10px;
  width: 72.4%;
  padding-right: 10px;
  background: #fff;
}

.moban {
  line-height: 48px;
  height: 48px;
  padding-left: 10px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 2px solid #f1f1f1;
}

.collapse-transition {
  transition: none; //权重稍微高一点覆盖掉组件本身的动画效果
}

.content__btns {
  display: inline-block;
  text-align: center;
  margin: 0 2px;

  i {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;

    border-radius: 50%;
    background: #419efb;
  }

  span {
    color: #fff;
    display: inline-block;
    margin-top: 10px;
    font-size: 12px;
  }
}
</style>
