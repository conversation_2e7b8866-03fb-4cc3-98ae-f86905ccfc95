<!--折线图配置 -->
<template>
  <div>
    <el-collapse accordion>
      <el-collapse-item title="折线设置" :title="`${$t('components.line.LineChartSettings')}`">
        <el-form-item label1="平滑曲线" :label="`${$t('components.line.SmoothCurve')}`">
          <avue-switch v-model="main.activeOption.smooth">
          </avue-switch>
        </el-form-item>
        <el-form-item label1="面积堆积" :label="`${$t('components.line.AreaStacking')}`">
          <avue-switch v-model="main.activeOption.areaStyle"></avue-switch>
        </el-form-item>
        <el-form-item label1="线条宽度" :label="`${$t('components.line.LineWidth')}`">
          <avue-slider v-model="main.activeOption.lineWidth">
          </avue-slider>
        </el-form-item>
        <el-form-item label1="圆点" :label="`${$t('components.line.CircleDot')}`">
          <avue-switch v-model="main.activeOption.symbolShow">
          </avue-switch>
        </el-form-item>
        <el-form-item label1="点的大小" :label="`${$t('components.line.SizeoftheDot')}`"
                      v-if="main.activeOption.symbolShow">
          <avue-slider v-model="main.activeOption.symbolSize">
          </avue-slider>
        </el-form-item>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'line',
  inject: ["main"]
}
</script>

<style>
</style>