// 多条件逻辑测试脚本
// 用于验证修复后的条件判断逻辑

console.log('🧪 开始多条件逻辑测试...');

// 模拟修复后的条件处理逻辑
function processAllConditions(item, row, typeName = 'cellbackground') {
    let finalColor = null;
    let appliedStyles = {
        rowbackground: null,
        rowfont: null,
        cellfont: null,
        cellbackground: null
    };
    
    try {
        // 收集所有条件（主条件 + 扩展条件）
        const allConditions = [];
        
        // 1. 添加主条件
        if (item.condition && item.value !== undefined && item.value !== null) {
            allConditions.push({
                ...item,
                conditionIndex: 0,
                conditionType: '主条件'
            });
        }
        
        // 2. 添加扩展条件
        if (item.editableTabsFormJSON && item.editableTabsFormJSON.length > 0) {
            item.editableTabsFormJSON.forEach((tabItem, index) => {
                if (tabItem.condition && tabItem.value !== undefined && tabItem.value !== null) {
                    allConditions.push({
                        ...tabItem,
                        prop: item.prop, // 确保使用相同的字段
                        conditionIndex: index + 1,
                        conditionType: `扩展条件${index + 1}`
                    });
                }
            });
        }
        
        console.log(`🔍 处理列 ${item.prop} 的所有条件:`, {
            totalConditions: allConditions.length,
            conditions: allConditions.map(c => ({
                type: c.conditionType,
                condition: c.condition,
                value: c.value
            }))
        });
        
        // 3. 按顺序检查所有条件，后面的条件优先级更高
        allConditions.forEach((conditionItem) => {
            const currentColor = getColorByType(conditionItem, row, typeName);
            
            if (currentColor) {
                // 如果当前条件满足，更新最终颜色和样式
                finalColor = currentColor;
                
                // 记录当前条件应用的样式
                if (conditionItem.rowbackground) appliedStyles.rowbackground = conditionItem.rowbackground;
                if (conditionItem.rowfont) appliedStyles.rowfont = conditionItem.rowfont;
                if (conditionItem.cellfont) appliedStyles.cellfont = conditionItem.cellfont;
                if (conditionItem.cellbackground) appliedStyles.cellbackground = conditionItem.cellbackground;
                
                console.log(`✅ ${conditionItem.conditionType}满足:`, {
                    condition: conditionItem.condition,
                    value: conditionItem.value,
                    cellValue: row[conditionItem.prop],
                    appliedColor: currentColor,
                    typeName: typeName
                });
            } else {
                console.log(`❌ ${conditionItem.conditionType}不满足:`, {
                    condition: conditionItem.condition,
                    value: conditionItem.value,
                    cellValue: row[conditionItem.prop]
                });
            }
        });

    } catch (error) {
        console.error('处理所有条件出错:', error, { item, row, typeName });
    }
    
    return finalColor;
}

// 模拟条件判断逻辑
function getColorByType(item, row, typeName = 'cellbackground') {
    let color = null;
    
    if (!item || !row || !item.prop) {
        return color;
    }

    if (!item.condition || item.value === undefined || item.value === null) {
        return color;
    }

    try {
        const cellValue = row[item.prop];
        const conditionValue = item.value;
        let conditionMet = false;

        // 数据类型检查和条件判断
        switch (parseInt(item.condition)) {
            case 1: // 大于
                if (isNumeric(cellValue) && isNumeric(conditionValue)) {
                    conditionMet = parseFloat(cellValue) > parseFloat(conditionValue);
                }
                break;

            case 5: // 大于等于
                if (isNumeric(cellValue) && isNumeric(conditionValue)) {
                    conditionMet = parseFloat(cellValue) >= parseFloat(conditionValue);
                }
                break;

            // 其他条件类型...
        }

        // 如果条件满足，返回颜色
        if (conditionMet) {
            color = item[typeName];
        }

    } catch (error) {
        console.error('条件判断出错:', error, { item, row, typeName });
    }

    return color;
}

// 数值检查辅助方法
function isNumeric(value) {
    if (value === null || value === undefined || value === '') {
        return false;
    }
    return !isNaN(value) && !isNaN(parseFloat(value));
}

// 测试数据
const testData = [
    { id: 1, name: '测试1', value: 50 },   // 不满足任何条件
    { id: 2, name: '测试2', value: 150 },  // 满足条件1（大于111）
    { id: 3, name: '测试3', value: 250 },  // 满足条件1和条件2
    { id: 4, name: '测试4', value: 300 }   // 满足条件1和条件2
];

// 测试配置
const testConfig = {
    label: "数值",
    prop: "value",
    // 主条件：大于111显示蓝色
    condition: 1, // 大于
    value: 111,
    cellbackground: "#3498db",
    cellfont: "#ffffff",
    // 扩展条件：大于等于222显示红色（优先级更高）
    editableTabsFormJSON: [
        {
            condition: 5, // 大于等于
            value: 222,
            cellbackground: "#e74c3c",
            cellfont: "#ffffff"
        }
    ]
};

// 运行测试
console.log('\n🎯 开始测试多条件处理逻辑...\n');

testData.forEach((row, index) => {
    console.log(`\n--- 测试 ${index + 1}: ${row.name} (值: ${row.value}) ---`);
    
    const result = processAllConditions(testConfig, row, 'cellbackground');
    
    console.log(`最终结果: ${result || '无样式'}`);
    console.log('---');
});

console.log('\n✅ 多条件逻辑测试完成！');
console.log('\n📋 测试总结:');
console.log('- 值 = 50: 不满足任何条件 → 无样式');
console.log('- 值 = 150: 满足条件1 → 蓝色背景');
console.log('- 值 = 250: 满足条件1和条件2 → 红色背景（条件2优先）');
console.log('- 值 = 300: 满足条件1和条件2 → 红色背景（条件2优先）');
console.log('\n🎉 修复验证：条件2现在可以正确覆盖条件1！');
