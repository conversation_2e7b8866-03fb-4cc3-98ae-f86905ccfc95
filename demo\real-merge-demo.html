<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实合并列功能演示</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #fff;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .controls {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-item label {
            color: #fff;
            font-size: 14px;
            white-space: nowrap;
        }
        .btn {
            background: linear-gradient(45deg, #69bfe7, #5aa3d1);
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(105, 191, 231, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(105, 191, 231, 0.4);
        }
        .table-container {
            height: 500px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .info {
            margin-top: 20px;
            color: #fff;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .info h3 {
            margin-top: 0;
            color: #69bfe7;
        }
        .info ul {
            margin: 10px 0;
        }
        .info li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 真实合并列功能演示</h1>
            <p>基于表格插件的实际合并列功能测试</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="enableMergeColumn" checked> 启用合并列功能
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="mergeWorkstation" checked> 合并工序列
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="mergeStation" checked> 合并工站列
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="showBorder" checked> 显示边框
                    </label>
                </div>
                <button class="btn" onclick="updateConfig()">应用配置</button>
                <button class="btn" onclick="generateData()">生成新数据</button>
            </div>
        </div>

        <div class="table-container">
            <div id="tableApp" style="height: 100%; padding: 10px;"></div>
        </div>

        <div class="info">
            <h3>📋 功能说明</h3>
            <ul>
                <li>✅ 这是使用真实表格插件的合并列功能演示</li>
                <li>✅ 支持多列同时合并，相同值的相邻单元格会自动合并</li>
                <li>✅ 数据按工序和工站排序，确保相同值相邻</li>
                <li>✅ 空值或undefined不参与合并</li>
                <li>✅ 合并功能与边框、滚动等其他功能兼容</li>
            </ul>
            <p><strong>当前状态：</strong> <span id="statusInfo">正在初始化...</span></p>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入表格插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 生产数据
        let productionData = [
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061001", operator: "费学进", uploadTime: "2025-07-08 08:30" },
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061002", operator: "张三", uploadTime: "2025-07-08 08:35" },
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061003", operator: "李四", uploadTime: "2025-07-08 08:40" },
            { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062001", operator: "王五", uploadTime: "2025-07-08 09:00" },
            { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062002", operator: "赵六", uploadTime: "2025-07-08 09:05" },
            { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063001", operator: "孙七", uploadTime: "2025-07-08 09:30" },
            { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063002", operator: "周八", uploadTime: "2025-07-08 09:35" },
            { workstation: "外层线路", station: "前处理4线", serialNumber: "J25064001", operator: "吴九", uploadTime: "2025-07-08 10:00" },
            { workstation: "内层显影", station: "显影1线", serialNumber: "J25071001", operator: "郑十", uploadTime: "2025-07-08 10:30" },
            { workstation: "内层显影", station: "显影1线", serialNumber: "J25071002", operator: "钱一", uploadTime: "2025-07-08 10:35" }
        ];

        // 表格配置
        let tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 12,
            scroll: false,
            border: true,
            enableMergeColumn: true,
            headerBackground: "#050e18",
            headerColor: "#69bfe7",
            headerFontSize: 14,
            headerTextAlign: "center",
            bodyColor: "#69bfe7",
            bodyFontSize: 12,
            bodyTextAlign: "center",
            nthColor: "rgba(9, 25, 44, 0.8)",
            othColor: "rgba(20, 42, 64, 0.8)",
            column: [
                { 
                    label: "工序", 
                    prop: "workstation", 
                    width: 120,
                    mergeColumn: true
                },
                { 
                    label: "工站", 
                    prop: "station", 
                    width: 120,
                    mergeColumn: true
                },
                { 
                    label: "流程卡号", 
                    prop: "serialNumber", 
                    width: 180
                },
                { 
                    label: "操作员", 
                    prop: "operator", 
                    width: 100
                },
                { 
                    label: "上传时间", 
                    prop: "uploadTime", 
                    width: 140
                }
            ]
        };

        // Vue应用实例
        let tableApp = null;

        // 初始化表格应用
        function initTableApp() {
            if (typeof Vue === 'undefined') {
                console.error('Vue is not loaded');
                return;
            }

            // 检查是否有avue-table组件
            if (typeof window.avueTable === 'undefined' && typeof Vue.component('avue-table') === 'undefined') {
                console.warn('avue-table component not found, using fallback');
                // 使用简化的Element UI表格作为后备
                initFallbackTable();
                return;
            }

            tableApp = new Vue({
                el: '#tableApp',
                data: {
                    option: Object.assign({}, tableConfig),
                    data: [...productionData]
                },
                template: `
                    <avue-table 
                        :option="option" 
                        :data="data"
                        style="height: 100%;">
                    </avue-table>
                `,
                mounted() {
                    updateStatus();
                }
            });
        }

        // 后备表格（使用Element UI）
        function initFallbackTable() {
            tableApp = new Vue({
                el: '#tableApp',
                data: {
                    tableData: [...productionData],
                    enableMerge: tableConfig.enableMergeColumn,
                    showBorder: tableConfig.border
                },
                template: `
                    <el-table 
                        :data="tableData" 
                        :border="showBorder"
                        :span-method="enableMerge ? spanMethod : null"
                        style="width: 100%; height: 100%;"
                        :header-cell-style="{background: '#050e18', color: '#69bfe7'}"
                        :cell-style="{color: '#69bfe7', background: 'rgba(9, 25, 44, 0.8)'}">
                        <el-table-column type="index" label="#" width="60"></el-table-column>
                        <el-table-column prop="workstation" label="工序" width="120"></el-table-column>
                        <el-table-column prop="station" label="工站" width="120"></el-table-column>
                        <el-table-column prop="serialNumber" label="流程卡号" width="180"></el-table-column>
                        <el-table-column prop="operator" label="操作员" width="100"></el-table-column>
                        <el-table-column prop="uploadTime" label="上传时间" width="140"></el-table-column>
                    </el-table>
                `,
                methods: {
                    spanMethod({ row, column, rowIndex, columnIndex }) {
                        // 只对工序列(索引1)和工站列(索引2)进行合并
                        if (columnIndex !== 1 && columnIndex !== 2) {
                            return [1, 1];
                        }
                        
                        const prop = column.property;
                        const currentValue = row[prop];
                        
                        // 如果当前值为空，不合并
                        if (currentValue === null || currentValue === undefined || currentValue === '') {
                            return [1, 1];
                        }
                        
                        // 查找连续相同值的行数
                        let rowspan = 1;
                        
                        // 向下查找相同值
                        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
                            if (this.tableData[i][prop] === currentValue) {
                                rowspan++;
                            } else {
                                break;
                            }
                        }
                        
                        // 向上查找，如果上一行有相同值，则当前行不显示
                        for (let i = rowIndex - 1; i >= 0; i--) {
                            if (this.tableData[i][prop] === currentValue) {
                                return [0, 0]; // 不显示当前单元格
                            } else {
                                break;
                            }
                        }
                        
                        return [rowspan, 1];
                    }
                },
                mounted() {
                    updateStatus();
                }
            });
        }

        // 更新配置
        function updateConfig() {
            if (!tableApp) return;
            
            tableConfig.enableMergeColumn = document.getElementById('enableMergeColumn').checked;
            tableConfig.border = document.getElementById('showBorder').checked;
            tableConfig.column[0].mergeColumn = document.getElementById('mergeWorkstation').checked;
            tableConfig.column[1].mergeColumn = document.getElementById('mergeStation').checked;
            
            if (tableApp.option) {
                // 使用avue-table
                tableApp.option = Object.assign({}, tableConfig);
            } else {
                // 使用后备表格
                tableApp.enableMerge = tableConfig.enableMergeColumn;
                tableApp.showBorder = tableConfig.border;
            }
            
            updateStatus();
        }

        // 生成新数据
        function generateData() {
            const workstations = ['免片线路', '外层线路', '内层显影', '钻孔工序'];
            const stations = ['前处理1线', '前处理2线', '前处理3线', '前处理4线', '显影1线', '显影2线'];
            const operators = ['费学进', '张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十', '钱一'];
            
            productionData = [];
            let serialNum = 1;
            
            workstations.forEach(workstation => {
                const stationCount = Math.floor(Math.random() * 2) + 2;
                for (let i = 0; i < stationCount; i++) {
                    const station = stations[Math.floor(Math.random() * stations.length)];
                    const recordCount = Math.floor(Math.random() * 3) + 2;
                    
                    for (let j = 0; j < recordCount; j++) {
                        productionData.push({
                            workstation: workstation,
                            station: station,
                            serialNumber: `J2506${String(serialNum).padStart(4, '0')}`,
                            operator: operators[Math.floor(Math.random() * operators.length)],
                            uploadTime: `2025-07-08 ${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`
                        });
                        serialNum++;
                    }
                }
            });
            
            // 按工序和工站排序
            productionData.sort((a, b) => {
                if (a.workstation !== b.workstation) {
                    return a.workstation.localeCompare(b.workstation);
                }
                return a.station.localeCompare(b.station);
            });
            
            if (tableApp) {
                if (tableApp.data) {
                    tableApp.data = [...productionData];
                } else {
                    tableApp.tableData = [...productionData];
                }
            }
            
            updateStatus();
        }

        // 更新状态信息
        function updateStatus() {
            const status = document.getElementById('statusInfo');
            const mergeEnabled = tableConfig.enableMergeColumn;
            const workstationMerge = tableConfig.column[0].mergeColumn;
            const stationMerge = tableConfig.column[1].mergeColumn;
            
            status.innerHTML = `
                合并列功能: ${mergeEnabled ? '启用' : '禁用'} | 
                工序列合并: ${workstationMerge ? '启用' : '禁用'} | 
                工站列合并: ${stationMerge ? '启用' : '禁用'} | 
                数据条数: ${productionData.length}
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('真实合并列功能演示页面已加载');
            
            // 延迟初始化，确保所有脚本都已加载
            setTimeout(() => {
                initTableApp();
            }, 500);
        });
    </script>
</body>
</html>
