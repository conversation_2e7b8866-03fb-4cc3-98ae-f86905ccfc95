<template>
  <div class="modelConfig">
    <vxe-modal
      style="overflow-y: auto"
      v-model="showEdit"
      :title="title"
      width="600"
      min-height="300"
      resize
      destroy-on-close
    >
      <template #default>
        <el-form
          size="small"
          class="el_form"
          :rules="formRules"
          ref="forms"
          :model="formData"
          label-width="100px"
        >
          <el-form-item label="数据源" prop="CDATASOURCE_ID">
            <el-select
            filterable 
              class="main-select-tree"
          
              v-model="formData.CDATASOURCE_ID"
              style="width: 100%"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.CID"
                :label="item.CNAME"
                :value="item.CID"
             
              />
              <!-- <el-tree
                class="main-select-el-tree"
                ref="selecteltree"
                :data="modalData"
                node-key="CID"
                highlight-current
                :props="defaultProps"
                @node-click="handleNodeClick"
                :current-node-key="true"
                default-expand-all
                
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    {{node.label}}</span
                >
              </el-tree> -->
            </el-select>
          </el-form-item>
          <el-form-item label="数据集编码">
            <el-input
              v-model="formData.CCODE"
              placeholder="请输入数据集编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="数据集名称" prop="CNAME">
            <el-input
              v-model="formData.CNAME"
              placeholder="请输入数据源名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="标签名称" >
          
            <el-select
          
          class="main-select-tree"
       
          v-model="formData.CDATASET_LABEL_ID"
          style="width: 100%"
          placeholder="请选择标签名称"
        >
          <el-option
            v-for="item in option1"
            :key="item.CID"
            :label="item.CNAME"
            :value="item.CID"
          
          /></el-select>
          </el-form-item>
          <el-form-item label="发布状态" >
            <el-switch
              v-model="formData.CPUBLISH_STATUS"
              :active-value="10"
              :inactive-value="0"
            ></el-switch>
          </el-form-item>
        
        
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="formData.CDESC"
              :autosize="{ minRows: 5 }"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>

          <el-form-item style="text-align: right">
            <!-- <el-button
            :loading="loading"
              style="margin-right: 170px"
              type="primary"
              @click="testLink('form')"
              >测试连接</el-button
            > -->
            <el-button @click="reset('forms')">取消</el-button>
            <el-button type="primary" @click="submitEvent('forms')"
              >确定</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import {
  GetDataSources,
  GetDataSetLabels,
  kanbanGroupAddObj,
  ConnectionTest,
  AddDataSet
} from "@/api/visual";
export default {
  name: "Modal",

  props: ["modelEvent", "title"],
  data() {
    return {
        loading:false,
      selectRow: null,
      showEdit: false,
      sexList: [
        { label: "女", value: "0" },
        { label: "男", value: "1" },
      ],
      formData: {
        CCODE:'',
       CNAME:"",//数据集名称
    CDATASOURCE_ID:'',//数据源ID(来自数据源的CID)
    CDATASET_LABEL_ID:'',//数据集标签(来自数据集标签的CID)
    CPUBLISH_STATUS:0,//发布状态(0:未发布,10:已发布)
  //  CHEALTH_STATUS:"",//健康状态(0:正常,10:异常)
    CDATASET_CONFIG:"",//数据集配置（JSON格式）
    CDESC:""//备注'
      },
      formRules: {

        CDATASOURCE_ID: [
          { required: true, message: "请选择数据源", trigger: "change" },
        ],
        CNAME: [
          { required: true, message: "请输入数据源名称", trigger: "blur" },
        ],
      
     
     
      },
      modalData: [],
      defaultProps: {
        children: "children",
        label: "CNAME",
      },
      options: [],
      option1:[]
    };
  },
  mounted() {
    this.getLables()
    this.getdatasoures()
   
  },
  methods: {
    //新建
    addshowEdit() {
      this.showEdit = true;
    },
    //快速新建
    qucikshowEdit() {
      this.showEdit = true;
    },
    //添加
    appendshowEdit() {
      this.showEdit = true;
    },
   //获取数据源类型
   getdatasoures() {
   
      GetDataSources().then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.options=res.data.Datas
          this.modalData = this.delDepartTree(res.data.Datas);
        }
      });
    },
  //获取数据源标签
  getLables(){
    GetDataSetLabels().then(res=>{
      res=res.data
      if(res.code === 200 && res.data.Success){
this.option1=res.data.Datas
      }
    })
  },
  
  //树形结构转化
  delDepartTree(list) {
      // 1. 定义两个中间变量
      const treeList = [], // 最终要产出的树状数据的数组
        map = {}; // 存储映射关系

      // 2. 建立一个映射关系，并给每个元素补充children属性.
      // 映射关系: 目的是让我们能通过id快速找到对应的元素
      // 补充children：让后边的计算更方便
      list.forEach((item) => {
        item.children = [];
        map[item.CID] = item;
      });
      // 3. 循环
      list.forEach((item) => {
        // 对于每一个元素来说，先找它的上级
        //    如果能找到，说明它有上级，则要把它添加到上级的children中去
        //    如果找不到，说明它没有上级，直接添加到 treeList
        const parent = map[item.CDATASOURCE_TYPE];
        if (parent) {
          parent.children.push(item);
        } else {
          treeList.push(item);
        }
      });
      // 4. 返回出去
      return treeList;
    },

 //测试链接
//  testLink(formName){
//     this.$refs[formName].validate((valid) => {
//         if (valid) {
//             this.loading=true
//             const params={
//                 CIS_SSL:this.formData.SSL,
//               //  CPORT:this.formData.CPORT
//             }
//             this.formData.CHOST= this.formData.CHOST+':'+this.formData.CPORT
//             this.formData.CCONTENT=JSON.stringify(params)
//             ConnectionTest(this.formData).then(res=>{
//                  res=res.data
//                 if(res.code===200&&res.data.Success){
//                 this.$message({
//                   message: res.data.Content,
//                   type: "success",
//                 }); 
//                 this.showEdit = false;
//                 this.getdatasoures()
//                 }else{
//                     this.$message({
//                   message: res.data.Content,
//                   type: "error",
//                 }); 
//                 }
//                 this.loading=false
//             })
          
//         } else {
//           console.log("error submit!!");
//           return false;
//         }
//       });
//  },




    //获得焦点
    reset() {
      this.formData= {
        CCODE:'',
       CNAME:"",//数据集名称
    CDATASOURCE_ID:'',//数据源ID(来自数据源的CID)
    CDATASET_LABEL_ID:'',//数据集标签(来自数据集标签的CID)
    CPUBLISH_STATUS:0,//发布状态(0:未发布,10:已发布)
  //  CHEALTH_STATUS:"",//健康状态(0:正常,10:异常)
    CDATASET_CONFIG:"",//数据集配置（JSON格式）
    CDESC:""//备注'
      },
      this.showEdit = false;
    },
   
    handleNodeClick(node) {
      this.formData.CDATASOURCE_TYPE = node.CID;
      this.$refs.selectTree.blur();
    },
    submitEvent(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
            AddDataSet(this.formData).then(res=>{
                res=res.data;
                if(res.code===200&&res.data.Success){
                this.$message({
                  message: res.data.Content,
                  type: "success",
                }); 
                this.reset()
                this.showEdit = false;
                this.$emit('func')
            }else{
                this.$message({
                  message: res.data.Content,
                  type: "error",
                }); 
                
            }
            })
          


        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.modelConfig {
  .el-form-item__error {
    top: 67%;
  }

  .el_form {
    width: 500px;
    padding: 0 30px;
  }
}
</style>
