// GlobalDataManager.js
class GlobalDataManager {
    constructor({cacheDuration= 10000}) {
        this.cache = null;
        this.lastFetchTime = null;
        this.fetching = false;
        this.subscribers = new Set();
        this.cacheDuration = cacheDuration; // 10秒缓存时间
    }

    // 订阅数据更新
    subscribe(callback) {
        this.subscribers.add(callback);
        // 如果已有缓存数据，立即通知新订阅者
        if (this.cache) {
            callback(this.cache);
        }
        return () => this.subscribers.delete(callback);
    }

    // 获取数据
    async getData() {
        // 如果正在获取数据，等待结果
        if (this.fetching) {
            return new Promise(resolve => {
                const unsubscribe = this.subscribe(data => {
                    unsubscribe();
                    resolve(data);
                });
            });
        }

        // 检查缓存是否有效
        const now = Date.now();
        if (this.cache && this.lastFetchTime && 
            (now - this.lastFetchTime) < this.cacheDuration) {
            return this.cache;
        }

        // 开始获取新数据
        this.fetching = true;
        try {
            const response = await fetch('/api/your-endpoint');
            const data = await response.json();
            
            this.cache = data;
            this.lastFetchTime = now;
            
            // 通知所有订阅者
            this.subscribers.forEach(callback => callback(data));
            
            return data;
        } finally {
            this.fetching = false;
        }
    }

    // 启动定时刷新
    startAutoRefresh() {
        setInterval(() => {
            this.getData();
        }, this.cacheDuration);
    }
}
// 创建单例实例
//export const dataManager = new GlobalDataManager();