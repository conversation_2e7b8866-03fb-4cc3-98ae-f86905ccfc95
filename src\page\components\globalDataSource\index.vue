<template>
    <el-collapse @change="changeCollapse" accordion>
        <el-collapse-item title1="全局数据源" :title="`${$t('page.build.GlobalData')}`">
            <div class="flex flex-col">
                <div style="padding: 5px 20px;" class="flex w-full  justify-between">
                    <div class="text-white">
                        <!-- {{ config }} -->
                    </div>
                    <div>
                        <!-- <el-button
                        size="small"
                        type="primary"
                        class="block"
                        @click="globShow = true"
                        >添加</el-button> -->
                    </div>
                </div>
                <div style="padding: 5px 20px;" class="flex justify-start  w-full">
                    <div class="flex justify-between w-full">
                        <div class="text-white">
                            <!-- 数据源 -->
                            {{ $t('page.build.DataSource') }}
                        </div>
                        <avue-select filterable v-if="globalData && globalData.length > 0 && DIC.sql.length > 0"
                            class="border-white" @change="datasoureChange($event, 0)" :dic="DIC.sql"
                            v-model="globalData[0].dataSource"></avue-select>
                    </div>
                </div>
                <div style="padding: 5px 20px;" class="flex mt-5 justify-start  w-full">
                    <div class="flex justify-between w-full">
                        <div class="text-white">
                            <!-- 数据集 -->
                            {{ $t('page.build.Dataset') }}
                        </div>
                        <avue-select filterable v-if="globalData && globalData.length > 0 && DIC.data.length > 0"
                            class="border-white" :dic="DIC.data" v-model="globalData[0].dataSet"
                            @change="datasetChange($event, 0)"></avue-select>
                    </div>
                </div>
                <div style="padding: 5px 20px;" class="flex mt-5 justify-start  w-full">
                    <div class="flex justify-between w-full">
                        <div class="text-white">
                            <!-- 刷新时间 -->
                            {{ $t('page.build.RefreshTime') }}
                        </div>
                        <avue-input-number v-if="globalData && globalData.length > 0" :min="1000"
                            @blur="changeTime($event, 0)" style="width: 198.6px;" class="border-white"
                            v-model="globalData[0].time" placeholder="0"></avue-input-number>
                    </div>
                </div> 
                 <!-- 首次获取将在(ms)后 -->
                <!-- <div style="padding: 5px 20px;" class="flex mt-5 justify-start  w-full">
                    <div class="flex justify-between w-full">
                        <div class="text-white">
                          
                            {{ $t('page.build.FirstLoad') }}
                        </div>
                        <avue-input-number v-if="globalData && globalData.length > 0" @blur="changeTime($event, 0)"
                            style="width: 198.6px;" class="border-white" v-model="globalData[0].timeOfFirstGetData"
                            placeholder="0"></avue-input-number>
                    </div>
                </div> -->
                <div style="padding: 5px 20px;" class="flex mt-5 justify-start  w-full">
                    <div class="flex justify-between w-full">
                        <div class="text-white">
                            <el-button :loading="isTestLoading" size="small" type="primary" class="block"
                                @click="testHandleRes()">
                                <!--andy 请求数据 首页-->
                                {{ $t('page.build.requestData') }}
                            </el-button>
                            <el-button style="margin-top: 10px;" :loading="isTestLoading" size="small" type="primary" class="block"
                                @click="cleanTestResData()">
                                <!-- andy 请求数据 首页-->
                                重置
                            </el-button>
                        </div>
                        <div  style="border:1px solid #fff;height: 200px;width: 100%;overflow: auto;color: #fff;">
                                {{ globalDataRes }}
                            <!-- <monaco-editor class="border-red" v-model="globalDataRes" language="javascript" disabled  height="400"></monaco-editor> -->
                        </div>

                    </div>
                </div>
            </div>
        </el-collapse-item>
    </el-collapse>


</template>

<script>
import {
    GetDataAll,
    GetSoureAll,
    //   GetModelAll,
    //   GetInputParameterByDataSetId,
    GetListByDataSetId,
} from "@/api/kanban";
import MonacoEditor from "@/page/components/editor";
import { setLocalStorageStore, getLocalStorageStore } from '@/utils/setStore'
export default {
    name: 'GlobalDataSource',
    components: {
        MonacoEditor
    },
    props: {
        config: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            isTestLoading: false,
            globalDataRes: '',
            globalData: [
                {
                    dataSource: '',
                    dataSet: '',
                    dataSetLabel: '',
                    time: '',
                    timeOfFirstGetData: '10000'
                }
            ],
            DIC: {
                sql: [], // 数据源
                data: [],  // 数据集
            }
        }
    },
    watch: {
        // config:{
        //     handler(newVal,oldVal){
        //         this.init()
        //     },
        //     deep:true
        // }
    },
    created() {
        this.getDatasoure();
    },
    mounted() {
        setTimeout(() => {
            this.init()
        }, 1000)
    },
    methods: {
        cleanTestResData(){
            this.globalDataRes = ""
        },
        // 测试请求数据
        testHandleRes() {
            this.isTestLoading = true
            setTimeout(() => {
                this.isTestLoading = false
            }, 3000)
            let _self = this
            const params = {
                Parameter: {},
                Id: this.globalData[0].dataSet,
            };
            GetListByDataSetId(params).then((res) => {
                this.isTestLoading = false
                if (res.data.code === 200 && res.data.data.Success) {
                    _self.globalDataRes = res.data.data.Datas;
                } else {
                    _self.$message({
                        message: res.data.data.content,
                        type: "error",
                    });
                }
            }).catch(error => {
                _self.$message({
                    message: error,
                    type: "error",
                });
            });
        },
        changeCollapse(val) {
            this.init()
        },
        init() {
            // debugger
            try {
                this.globalData[0].dataSource = this.config.globalDataSource[0].dataSource
                this.globalData[0].dataSet = this.config.globalDataSource[0].dataSet
                this.globalData[0].time = this.config.globalDataSource[0].time
                this.globalData[0].dataSetLabel = this.config.globalDataSource[0].dataSetLabel
                this.globalData[0].timeOfFirstGetData = this.config.globalDataSource[0].timeOfFirstGetData
                setLocalStorageStore('globalDataSource', this.globalData)
            } catch (error) {

            }

        },
        changeTime(e, index) {
            let _self = this;
            let { value } = e;
            this.$nextTick(() => {
                if (value) {
                    _self.$emit('change', _self.globalData)
                }
            })
            setLocalStorageStore('globalDataSource', this.globalData)
        },
        //数据源改变
        datasoureChange(e, index) {
            let _self = this;
            let { value } = e;
            this.$nextTick(() => {
                if (value) {
                    _self.$emit('change', _self.globalData)
                }
            })
            if (value) {
                this.getdatacellect(value);
            } else {
                this.getdatacellect(this.globalData[index].dataSource);
            }
            setLocalStorageStore('globalDataSource', this.globalData)
        },
        datasetChange(e, index) {
            let _self = this;
            let { value } = e;
            this.globalData[index].dataSetLabel = this.DIC.data.find(ele => ele.value == value).label
            this.$nextTick(() => {
                if (value) {
                    _self.$emit('change', _self.globalData)
                }

            })
            setLocalStorageStore('globalDataSource', this.globalData)
        },
        //请求数据源接口
        getDatasoure() {
            const params = {
                condition: "",
                typeId: 0,
            };
            GetDataAll(params).then((res) => {
                res = res.data;

                if (res.code == 200 && res.data.Success) {
                    const data = res.data.Datas;
                    this.DIC.sql = data.map((ele) => {
                        return {
                            label: ele.CNAME,
                            value: ele.CID,
                        };
                    });
                }
            });
        },

        //请求数据集接口
        getdatacellect(value) {
            const params = {
                condition: `{CID:${value}}`,
            };

            GetSoureAll(params).then((res) => {
                res = res.data;
                if (res.code === 200 && res.data.Success) {
                    const data = res.data.Datas;
                    this.DIC.data = data.map((ele) => {
                        return {
                            label: ele.CNAME,
                            value: ele.CID,
                        };
                    });
                }
            });
        },
    }

}
</script>

<style lang="scss">
@import "@/styles/baseFlex.scss";
</style>