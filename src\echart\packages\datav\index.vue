<template>
  <div :class="[b(),className]"
       :style="styleSizeName"
       @click="handleClick">
    <component :is="option.is"
               :ref="id"
               v-if="reload"
               :style="styleChartName"
               v-bind="config"></component>
  </div>
</template>

<script>
import create from "../../create";
export default create({
  name: "datav",
  data () {
    return {
      reload: false,
      config: {}
    }
  },
  methods: {
    getFormatData(){
      let _formatData = this.deepClone(this.dataChart) || [];
      // debugger
      switch (this.$attrs.name) {
        case '进度池':
        case '水位图':
           if(_formatData && Array.isArray(_formatData) && _formatData.length > 0) {
            _formatData = _formatData[0];
           }
          break;
      
        default:
          break;
      }
      return _formatData
    },
    handleClick () {
     // debugger
      let _formatData =this.getFormatData()
      this.updateClick(_formatData);
      this.clickFormatter && this.clickFormatter({
        data: _formatData
      }, this.getItemRefs());
    },
    updateChart () {
      //const optionData = this.deepClone(this.dataChart) || [];
      let optionData =this.getFormatData()
      this.config = this.echartFormatter && this.echartFormatter(optionData, this.dataParams);
      this.reload = false;
      this.$nextTick(() => {
        this.reload = true;
      })
    }
  }
});
</script>



