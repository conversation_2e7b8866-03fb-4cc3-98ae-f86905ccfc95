<template>
     <!-- <script src="/cdn/echarts/5.4.0/echarts.min.js"></script> -->
  <div :class="[b(),className]"
       :style="styleSizeName">
     <div ref="jsCodeRef"></div>
    <div :ref="id"
         :style="styleChartName"></div>
     
  </div>
</template>

<script>
// import * as echarts from '/cdn/echarts/5.4.0/echarts.min.js'
import create from "../../create";
export default create({
  name: "common",
  data(){
    return {
    }
  },
  mounted(){
    // qiankun 构架导致 无法直接使用 echarts 函数对象功能，需要通过 独立导入 获取
    this.loadJavascript("/subapp/bulletin/cdn/echarts/5.4.0/echarts.min.js")
  },
  methods: {
    loadJavascript(jsUrl) {
      let _self = this
        return new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.type = 'text/javascript'
            script.onload = () => resolve('')
            script.onerror = (err) => reject(err)
            script.src = jsUrl
            //debugger
             // 获取容器元素
            const container = _self.$refs["jsCodeRef"];
            container.appendChild(script)
           // document.body.appendChild(script)
        })
    },
    debounce (func, wait = 600) {
      let that = this;
      return function () {
        const context = this;
        const args = arguments;
        clearTimeout(that.timeout);
        that.timeout = setTimeout(function () {
          func.apply(context, args);
        }, wait);
      };
    },
    updateChart () {
     // debugger
      const optionData = this.deepClone(this.dataChart) || [];
      let option
      const callback = (res) => {
        this.$emit('error-change', '')
        this.loading = false;
        this.myChart.resize();
        this.myChart.setOption(option,true);
       // this.bindEvent()
      }
      try {
          if(this.echartFormatter){
            option = this.echartFormatter(optionData, this.dataParams) || {};
            callback()
          }
      
        } catch (err) {
          this.$emit('error-change', err)
        }
    }
  }
});
</script>



