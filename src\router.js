import Router from 'vue-router';
import Vue from 'vue';
Vue.use(Router)
 console.log(process.env.BASE_URL);
const vueRouter = new Router({
  //base: process.env.BASE_URL,
  base: window.__POWERED_BY_QIANKUN__?"/bulletin":process.env.BASE_URL,
  mode: process.env.VUE_APP_MODE,
  routes: [{
    path: '/',

    
    name: 'list',
    redirect:'/largescreendesign',
    component: () =>import( /* webpackChunkName: "page" */ '@/page/index')
      
  },
  {
    path: '/largescreendesign',
    name: 'largescreendesign',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/list/index.vue')
  },
  {
    path: '/largescreentemplate',
    name: 'largescreentemplate',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/list/template.vue')
  },
  {
    path: '/classification',
    name: 'Classification',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/list/category.vue')
  },
  {
    path: '/components',
    name: 'components',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/list/components.vue')
  },
  {
    path: '/datasetmanagement',
    name: 'datasetManagement',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/datasetManagement/index.vue')
  },
  {
    path: '/sourcemanagement',
    name: 'sourcemanagement',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/sourceManagement/index.vue')
  },

  {
    path: '/kanbanLabel',
    name: 'kanbanLabel',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/kanbanLabel/index.vue')
  },
  {
    path: '/mapManagement',
    name: 'mapManagement',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/mapManagement/index.vue')
  },
  {
    path: '/filemanagement',
    name: 'filemanagement',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/filemanagement/index.vue')
  },
  
  // //分类管理
  // {
  //   path: '/classification',
  //   name: 'Classification',
  //   component: () =>
  //     import( /* webpackChunkName: "page" */ '@/page/list/category.vue')
  // },
  //  //数据源管理
  //  {
  //   path: '/datasource',
  //   name: 'datasource',
  //   component: () =>
  //     import( /* webpackChunkName: "page" */ '@/page/list/db.vue')
  // },
   {
    path: '/create',
    name: 'create',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/create')
  }, {
    path: '/build/:id',
    name: 'build',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/build')
  }, {
    path: '/view/:id',
    name: 'view',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/view')
  }]
})
export default vueRouter;