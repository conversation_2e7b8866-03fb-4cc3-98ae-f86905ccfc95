import Vue from 'vue'
import ElementUI from 'element-ui';
import axios from './axios'
import 'element-ui/lib/theme-chalk/index.css';
import dataV from '@jiaminghi/data-view'
import router from './router.js';
import store from './store'
import { website } from '@/config.js'
import actions from '@/micors/actions'
import App from './App.vue'
import VXETable from "vxe-table";
import "vxe-table/lib/style.css";
import '@/utils/es6'
import '@/mock/'
//导入主题文件
import '@/theme/index.js'
// 引入并使用vue-i18n
import VueI18n from 'vue-i18n'
// 引入语言包，注意路径
import zh from '@/locales/zh.js';
import en from '@/locales/en.js';
import vi from '@/locales/vi.js';
import zhCN from 'vxe-table/lib/locale/lang/zh-CN'
import enUS from 'vxe-table/lib/locale/lang/en-US'
// import viVN from 'vxe-table/lib/locale/lang/vi-VN'
import enLocale from 'element-ui/lib/locale/lang/en'
import viLocale from 'element-ui/lib/locale/lang/en'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
Vue.use(VueI18n)
// 构造i18n对象
const i18n = new VueI18n({
  //legacy:false,
	// 默认语言，这里的local属性，对应message中的zh、en属性
	locale: 'zh',
	// 引入语言文件
	messages: {
		// 这里的属性名是任意的，您也可以把zh设置为cn等，只是后续切换语言时
		// 要标识这里的语言属性，如：this.$i18n.locale = zh|en|zh|xxx
		'zh': {...zh, ...zhCN, ...zhLocale}, // 这里为上面通过import引入的语言包
		'en': {...en, ...enUS, ...enLocale},
    'vh': {...vi, ...viLocale}
	}
})
Vue.prototype._i18n = i18n
Vue.config.productionTip = false
window.axios = axios;
document.title = website.title
// Vue.use(ElementUI);
// Vue.use(VXETable);
Vue.use(VXETable, {
  // 对组件内置的提示语进行国际化翻译
  i18n: (key, args) => i18n.t(key, args),
  // 可选，对参数中的列头、校验提示..等进行自动翻译（只对支持国际化的有效）
  //translate: (key, args) => i18n.t(key, args)
})
Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value)
})
Vue.prototype.$VXETable = VXETable;
Vue.use(window.AVUE);
Vue.prototype.$eventBus = new Vue()
Vue.prototype.$website = website;
Vue.use(dataV)
let instance=null;
function render(props) {
  if(props) actions.setActions(props)
  instance= new Vue({
    router,
    store,
    i18n,
    render: h => h(App)
  }).$mount('#appbulletin')
}
// 动态注入路径
if(window.__POWERED_BY_QIANKUN__){
  __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;
}
// 不使用乾坤
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

// 子组件协议
export async function bootstrap(props){
  // console.log(props);
}
export async function mount(props){
  Vue.prototype.$parentRouter = props.mainRouter;
   console.log(window,'+++');
  render(props)
 //setTimeout(() => {
  actions.setGlobalState({
    type: "loading",
  });

 //}, 1700);
  
   
 
}
export async function unmount(props){
 // console.log(props);
  instance.$destroy()
}

