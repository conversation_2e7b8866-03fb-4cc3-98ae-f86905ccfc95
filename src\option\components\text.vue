<!-- 文字配置 -->
<template>
  <div>
    <el-form-item label="文本内容" :label="`${$t('components.text.TextContent')}`">
      <avue-input type="textarea"
                  v-model="main.activeObj.data.value"></avue-input>
    </el-form-item>
    <el-form-item label="字体类型" :label="`${$t('components.text.FontType')}`">
      <avue-select filterable
                   allow-create
                   v-model="main.activeOption.fontFamily"
                   :dic="dicOption.fontFamily"></avue-select>
    </el-form-item>
    <el-form-item label="字体大小" :label="`${$t('components.text.FontSize')}`">
      <avue-input-number v-model="main.activeOption.fontSize"
                         :max="200"></avue-input-number>
    </el-form-item>
    <el-form-item label="字体颜色" :label="`${$t('components.text.FontColor')}`">
      <avue-input-color v-model="main.activeOption.color"></avue-input-color>
    </el-form-item>
    <el-form-item label="字体间距" :label="`${$t('components.text.FontSpacing')}`">
      <avue-input-number v-model="main.activeOption.split"></avue-input-number>
    </el-form-item>
    <el-form-item label="字体行高" :label="`${$t('components.text.FontLineHeight')}`">
      <avue-input-number v-model="main.activeOption.lineHeight"></avue-input-number>
    </el-form-item>
    <el-form-item label="字体背景" :label="`${$t('components.text.FontBackground')}`">
      <avue-input-color v-model="main.activeOption.backgroundColor"></avue-input-color>
    </el-form-item>
    <el-form-item label="文字粗细" :label="`${$t('components.text.FontWeight')}`">
      <avue-select v-model="main.activeOption.fontWeight"
                   :dic="dicOption.fontWeight">
      </avue-select>
    </el-form-item>
    <el-form-item label="对齐方式" :label="`${$t('components.text.Alignment')}`">
      <avue-select v-model="main.activeOption.textAlign"
                   :dic="dicOption.textAlign">
      </avue-select>
    </el-form-item>
    <el-collapse accordion>
      <el-collapse-item title="跑马灯设置" :title="`${$t('components.text.MarqueeSettings')}`">
        <el-form-item label="开启"  :label="`${$t('components.text.TurnOn')}`">
          <avue-switch v-model="main.activeOption.scroll"></avue-switch>
        </el-form-item>
        <template v-if="main.activeOption.scroll">
          <el-form-item label="滚动速度"  :label="`${$t('components.text.ScrollingSpeed')}`">
            <avue-input v-model="main.activeOption.speed"></avue-input>
          </el-form-item>
        </template>
      </el-collapse-item>
      <el-collapse-item title="超链设置"  :title="`${$t('components.text.HyperlinkSettings')}`">
        <el-form-item label="开启"  :label="`${$t('components.text.TurnOn')}`">
          <avue-switch v-model="main.activeOption.link"></avue-switch>
        </el-form-item>
        <template v-if="main.activeOption.link">
          <el-form-item label="打开方式"  :label="`${$t('components.text.OpeningMethod')}`">
            <avue-radio v-model="main.activeOption.linkTarget"
                        :dic="dicOption.target">
            </avue-radio>
          </el-form-item>
          <el-form-item label="超链地址"  :label="`${$t('components.text.HyperlinkAddress')}`">
            <avue-input v-model="main.activeOption.linkHref"></avue-input>
          </el-form-item>
        </template>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { dicOption } from '@/option/config'
export default {
  name: 'text',
  data () {
    return {
      dicOption: dicOption
    }
  },
  inject: ["main"]
}
</script>

<style>
</style>