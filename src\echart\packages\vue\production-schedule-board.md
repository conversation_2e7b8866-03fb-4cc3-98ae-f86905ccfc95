# 生产排程看板组件 - 完整案例文档

## 📋 组件概述

生产排程看板组件是一个专为工业生产管理设计的Vue组件，用于展示生产线的工单排程、设备状态、时间安排等关键信息。组件支持多种数据源类型，提供实时更新和交互功能。

## 🎯 功能特性

- **📅 时间轴展示**: 横向时间轴，清晰显示排程时间
- **🏭 工单管理**: 显示工单号、状态、持续时间
- **🎨 状态色彩**: 不同颜色表示不同的工单状态
- **🔄 实时更新**: 支持数据实时刷新
- **📱 响应式设计**: 适配不同屏幕尺寸
- **🖱️ 交互操作**: 点击工单查看详情

## 🔧 基础组件代码

```vue
<template>
  <div class="production-schedule-board">
    <div class="board-header">
      <h2>生产排程看板</h2>
      <div class="time-info">
        <span>当前时间: {{ currentTime }}</span>
        <span class="refresh-indicator" :class="{ active: isRefreshing }">●</span>
      </div>
    </div>
    
    <!-- 时间轴 -->
    <div class="timeline-header">
      <div class="timeline-cell time-label">时间</div>
      <div 
        v-for="hour in timeSlots" 
        :key="hour"
        class="timeline-cell"
        :class="{ current: isCurrentHour(hour) }"
      >
        {{ hour }}
      </div>
    </div>
    
    <!-- 生产线排程 -->
    <div class="schedule-content">
      <div 
        v-for="(line, lineIndex) in scheduleData" 
        :key="lineIndex"
        class="schedule-row"
      >
        <div class="line-label">{{ line.lineName }}</div>
        <div class="schedule-slots">
          <div 
            v-for="(slot, slotIndex) in line.slots" 
            :key="slotIndex"
            class="schedule-slot"
            :class="getSlotClass(slot)"
            :style="getSlotStyle(slot)"
            @click="handleSlotClick(slot, line)"
            :title="getSlotTooltip(slot)"
          >
            <div class="slot-content">
              <div class="work-order">{{ slot.workOrder }}</div>
              <div class="duration" v-if="slot.duration">{{ slot.duration }}h</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 状态图例 -->
    <div class="legend">
      <div class="legend-title">状态说明:</div>
      <div class="legend-items">
        <div 
          v-for="status in statusLegend" 
          :key="status.key"
          class="legend-item"
        >
          <span class="legend-color" :style="{ background: status.color }"></span>
          <span class="legend-text">{{ status.label }}</span>
        </div>
      </div>
    </div>
    
    <!-- 工单详情弹窗 -->
    <div v-if="selectedSlot" class="detail-modal" @click="closeDetail">
      <div class="detail-content" @click.stop>
        <h3>工单详情</h3>
        <div class="detail-info">
          <div class="info-item">
            <label>工单号:</label>
            <span>{{ selectedSlot.workOrder }}</span>
          </div>
          <div class="info-item">
            <label>产品:</label>
            <span>{{ selectedSlot.product }}</span>
          </div>
          <div class="info-item">
            <label>数量:</label>
            <span>{{ selectedSlot.quantity }}</span>
          </div>
          <div class="info-item">
            <label>状态:</label>
            <span :class="'status-' + selectedSlot.status">{{ getStatusText(selectedSlot.status) }}</span>
          </div>
          <div class="info-item">
            <label>开始时间:</label>
            <span>{{ selectedSlot.startTime }}</span>
          </div>
          <div class="info-item">
            <label>预计完成:</label>
            <span>{{ selectedSlot.endTime }}</span>
          </div>
        </div>
        <button @click="closeDetail" class="close-btn">关闭</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['dataChart'],
  data() {
    return {
      currentTime: '',
      isRefreshing: false,
      selectedSlot: null,
      timeSlots: [],
      statusLegend: [
        { key: 'running', label: '生产中', color: '#67C23A' },
        { key: 'waiting', label: '等待中', color: '#E6A23C' },
        { key: 'completed', label: '已完成', color: '#409EFF' },
        { key: 'error', label: '异常', color: '#F56C6C' },
        { key: 'maintenance', label: '维护', color: '#909399' }
      ]
    }
  },
  computed: {
    scheduleData() {
      return this.dataChart || [];
    }
  },
  created() {
    this.initTimeSlots();
    this.updateCurrentTime();
    this.timer = setInterval(this.updateCurrentTime, 1000);
    console.log('生产排程看板组件初始化', this.$parent);
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    initTimeSlots() {
      // 生成24小时时间槽
      for (let i = 0; i < 24; i++) {
        this.timeSlots.push(i.toString().padStart(2, '0') + ':00');
      }
    },
    updateCurrentTime() {
      this.currentTime = new Date().toLocaleString();
    },
    isCurrentHour(hour) {
      const currentHour = new Date().getHours();
      return hour === currentHour.toString().padStart(2, '0') + ':00';
    },
    getSlotClass(slot) {
      if (!slot.workOrder) return 'empty-slot';
      return {
        'schedule-slot-filled': true,
        [`status-${slot.status}`]: true,
        'urgent': slot.priority === 'high'
      };
    },
    getSlotStyle(slot) {
      if (!slot.workOrder) return {};
      
      const statusColors = {
        'running': '#67C23A',
        'waiting': '#E6A23C', 
        'completed': '#409EFF',
        'error': '#F56C6C',
        'maintenance': '#909399'
      };
      
      return {
        background: statusColors[slot.status] || '#f0f0f0',
        width: (slot.duration || 1) * 40 + 'px'
      };
    },
    getSlotTooltip(slot) {
      if (!slot.workOrder) return '';
      return `工单: ${slot.workOrder}\n产品: ${slot.product}\n状态: ${this.getStatusText(slot.status)}`;
    },
    getStatusText(status) {
      const statusMap = {
        'running': '生产中',
        'waiting': '等待中',
        'completed': '已完成', 
        'error': '异常',
        'maintenance': '维护'
      };
      return statusMap[status] || '未知';
    },
    handleSlotClick(slot, line) {
      if (slot.workOrder) {
        this.selectedSlot = { ...slot, lineName: line.lineName };
        this.$emit('slot-click', { slot, line });
      }
    },
    closeDetail() {
      this.selectedSlot = null;
    }
  }
}
</script>

<style>
.production-schedule-board {
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  font-family: 'Microsoft YaHei', sans-serif;
  min-height: 600px;
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e2e8f0;
}

.board-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 24px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #4a5568;
}

.refresh-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

.refresh-indicator.active {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.timeline-header {
  display: flex;
  background: #2d3748;
  color: white;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.timeline-cell {
  min-width: 40px;
  padding: 10px 5px;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  border-right: 1px solid #4a5568;
}

.timeline-cell.time-label {
  min-width: 100px;
  background: #1a202c;
}

.timeline-cell.current {
  background: #3182ce;
  color: #fff;
}

.schedule-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 8px 8px;
}

.schedule-row {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
}

.schedule-row:last-child {
  border-bottom: none;
}

.line-label {
  min-width: 100px;
  padding: 15px 10px;
  background: #f7fafc;
  border-right: 1px solid #e2e8f0;
  font-weight: bold;
  color: #2d3748;
  display: flex;
  align-items: center;
}

.schedule-slots {
  flex: 1;
  display: flex;
  min-height: 60px;
}

.schedule-slot {
  min-width: 40px;
  border-right: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.schedule-slot.empty-slot {
  background: #f9fafb;
}

.schedule-slot.empty-slot:hover {
  background: #f3f4f6;
}

.schedule-slot-filled {
  color: white;
  font-weight: bold;
  position: relative;
  overflow: hidden;
}

.schedule-slot-filled:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 10;
}

.schedule-slot.urgent {
  border: 2px solid #dc2626;
  animation: urgent-blink 2s infinite;
}

@keyframes urgent-blink {
  0%, 100% { border-color: #dc2626; }
  50% { border-color: #fca5a5; }
}

.slot-content {
  text-align: center;
  padding: 5px;
}

.work-order {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.duration {
  font-size: 10px;
  opacity: 0.9;
}

.legend {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.legend-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #2d3748;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #4a5568;
}

.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.detail-content h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  text-align: center;
}

.detail-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f7fafc;
}

.info-item label {
  font-weight: bold;
  color: #4a5568;
}

.info-item span {
  color: #2d3748;
}

.status-running { color: #67C23A; font-weight: bold; }
.status-waiting { color: #E6A23C; font-weight: bold; }
.status-completed { color: #409EFF; font-weight: bold; }
.status-error { color: #F56C6C; font-weight: bold; }
.status-maintenance { color: #909399; font-weight: bold; }

.close-btn {
  width: 100%;
  padding: 10px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: #337ecc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-cell {
    min-width: 30px;
    font-size: 10px;
  }
  
  .line-label {
    min-width: 80px;
    font-size: 12px;
  }
  
  .schedule-slot {
    min-width: 30px;
  }
  
  .work-order {
    font-size: 9px;
  }
  
  .legend-items {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
```

## 📊 数据源配置案例

### 1. 静态数据案例

```javascript
const staticDataConfig = {
  width: 1200,
  height: 600,
  dataType: 0,  // 静态数据
  data: [
    {
      lineName: "生产线A",
      slots: [
        { workOrder: "WO001", product: "产品A", quantity: 100, status: "completed", duration: 2, startTime: "08:00", endTime: "10:00", priority: "normal" },
        { workOrder: "", duration: 1 },
        { workOrder: "WO002", product: "产品B", quantity: 150, status: "running", duration: 3, startTime: "11:00", endTime: "14:00", priority: "high" },
        { workOrder: "", duration: 2 },
        { workOrder: "WO003", product: "产品C", quantity: 80, status: "waiting", duration: 2, startTime: "16:00", endTime: "18:00", priority: "normal" }
      ]
    },
    {
      lineName: "生产线B", 
      slots: [
        { workOrder: "WO004", product: "产品D", quantity: 200, status: "running", duration: 4, startTime: "08:00", endTime: "12:00", priority: "normal" },
        { workOrder: "WO005", product: "产品E", quantity: 120, status: "waiting", duration: 3, startTime: "12:00", endTime: "15:00", priority: "high" },
        { workOrder: "", duration: 1 },
        { workOrder: "WO006", product: "产品F", quantity: 90, status: "error", duration: 2, startTime: "16:00", endTime: "18:00", priority: "normal" }
      ]
    }
  ],
  
  option: {
    content: `上面的完整组件代码`
  }
};
```

### 2. API接口数据案例

```javascript
const apiDataConfig = {
  width: 1200,
  height: 600,
  dataType: 1,  // API接口数据
  dataMethod: "get",
  url: "https://api.factory.com/production/schedule",
  time: 30000,  // 30秒刷新一次

  // 请求参数
  dataQuery: `(url) => ({
    date: new Date().toISOString().split('T')[0],
    factory: 'main',
    lines: ['A', 'B', 'C', 'D'],
    includeCompleted: true
  })`,

  // 请求头
  dataHeader: `(url) => ({
    'Authorization': 'Bearer ' + localStorage.getItem('factoryToken'),
    'Content-Type': 'application/json',
    'X-Factory-ID': 'FACTORY001'
  })`,

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    try {
      if (data.code === 200 && data.data) {
        return data.data.productionLines.map(line => ({
          lineName: line.name,
          slots: line.schedule.map(item => ({
            workOrder: item.workOrderNo,
            product: item.productName,
            quantity: item.plannedQuantity,
            status: item.status.toLowerCase(),
            duration: Math.ceil((new Date(item.endTime) - new Date(item.startTime)) / (1000 * 60 * 60)),
            startTime: new Date(item.startTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
            endTime: new Date(item.endTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
            priority: item.priority || 'normal',
            actualQuantity: item.actualQuantity,
            efficiency: item.efficiency
          }))
        }));
      }
      return [];
    } catch (error) {
      console.error('生产排程数据处理错误:', error);
      return [];
    }
  }`,

  option: {
    content: `上面的完整组件代码`
  },

  // 点击事件处理
  clickFormatter: `(params, refs) => {
    console.log('生产排程点击事件:', params);

    if (params.type === 'slot-click') {
      // 显示工单详情，可以调用其他组件
      if (refs.workOrderDetail) {
        refs.workOrderDetail.updateData({
          workOrderNo: params.data.slot.workOrder
        });
      }

      // 更新相关统计图表
      if (refs.productionStats) {
        refs.productionStats.updateData({
          lineFilter: params.data.line.lineName
        });
      }
    }
  }`
};
```

### 3. 自定义数据接口案例

```javascript
const customDataConfig = {
  width: 1200,
  height: 600,
  dataType: 5,  // 自定义数据接口
  dataSet: 3001,  // 生产排程数据集ID

  // 自定义数据接口参数
  isCustom: btoa(JSON.stringify({
    paramters: [
      { key: "factoryId", value: "FACTORY001" },
      { key: "dateRange", value: "today" },
      { key: "includeLines", value: "A,B,C,D" },
      { key: "statusFilter", value: "all" },
      { key: "priorityFilter", value: "all" }
    ]
  })),

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    if (Array.isArray(data)) {
      // 按生产线分组数据
      const lineGroups = {};

      data.forEach(item => {
        const lineName = item.ProductionLine || item.lineName;
        if (!lineGroups[lineName]) {
          lineGroups[lineName] = [];
        }

        lineGroups[lineName].push({
          workOrder: item.WorkOrderNo || item.workOrder,
          product: item.ProductName || item.product,
          quantity: item.PlannedQty || item.quantity,
          status: (item.Status || item.status).toLowerCase(),
          duration: item.Duration || Math.ceil((new Date(item.EndTime) - new Date(item.StartTime)) / (1000 * 60 * 60)),
          startTime: item.StartTime ? new Date(item.StartTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) : '',
          endTime: item.EndTime ? new Date(item.EndTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) : '',
          priority: item.Priority || 'normal',
          actualQuantity: item.ActualQty || 0,
          efficiency: item.Efficiency || 0,
          operator: item.Operator || '',
          equipment: item.Equipment || ''
        });
      });

      // 转换为组件需要的格式
      return Object.keys(lineGroups).map(lineName => ({
        lineName: lineName,
        slots: this.fillTimeSlots(lineGroups[lineName])
      }));
    }
    return [];
  }`,

  option: {
    content: `
      <template>
        <div class="production-schedule-board">
          <!-- 增强版组件，包含更多功能 -->
          <div class="board-header">
            <h2>生产排程看板 - 数据集版</h2>
            <div class="controls">
              <select v-model="selectedFactory" @change="updateFilter">
                <option value="all">全部工厂</option>
                <option value="FACTORY001">工厂A</option>
                <option value="FACTORY002">工厂B</option>
              </select>
              <select v-model="selectedStatus" @change="updateFilter">
                <option value="all">全部状态</option>
                <option value="running">生产中</option>
                <option value="waiting">等待中</option>
                <option value="completed">已完成</option>
              </select>
              <button @click="refreshData" class="refresh-btn">刷新数据</button>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">总工单:</span>
              <span class="stat-value">{{ totalWorkOrders }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">生产中:</span>
              <span class="stat-value running">{{ runningCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">等待中:</span>
              <span class="stat-value waiting">{{ waitingCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成:</span>
              <span class="stat-value completed">{{ completedCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">异常:</span>
              <span class="stat-value error">{{ errorCount }}</span>
            </div>
          </div>

          <!-- 原有的排程表格内容 -->
          <!-- ... 其他组件内容保持不变 ... -->

        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        data() {
          return {
            selectedFactory: 'all',
            selectedStatus: 'all',
            // ... 其他数据属性
          }
        },
        computed: {
          scheduleData() {
            let data = this.dataChart || [];

            // 根据筛选条件过滤数据
            if (this.selectedStatus !== 'all') {
              data = data.map(line => ({
                ...line,
                slots: line.slots.filter(slot =>
                  !slot.workOrder || slot.status === this.selectedStatus
                )
              }));
            }

            return data;
          },
          totalWorkOrders() {
            return this.scheduleData.reduce((total, line) =>
              total + line.slots.filter(slot => slot.workOrder).length, 0
            );
          },
          runningCount() {
            return this.getStatusCount('running');
          },
          waitingCount() {
            return this.getStatusCount('waiting');
          },
          completedCount() {
            return this.getStatusCount('completed');
          },
          errorCount() {
            return this.getStatusCount('error');
          }
        },
        methods: {
          getStatusCount(status) {
            return this.scheduleData.reduce((count, line) =>
              count + line.slots.filter(slot => slot.status === status).length, 0
            );
          },
          updateFilter() {
            this.$emit('filter-change', {
              factory: this.selectedFactory,
              status: this.selectedStatus
            });
          },
          refreshData() {
            this.$emit('refresh-request');
          },
          fillTimeSlots(workOrders) {
            // 将工单填充到24小时时间槽中
            const slots = new Array(24).fill(null).map(() => ({ workOrder: '', duration: 1 }));

            workOrders.forEach(order => {
              const startHour = parseInt(order.startTime.split(':')[0]);
              const duration = order.duration || 1;

              for (let i = 0; i < duration && (startHour + i) < 24; i++) {
                if (i === 0) {
                  slots[startHour + i] = order;
                } else {
                  slots[startHour + i] = { ...order, workOrder: '', duration: 0 };
                }
              }
            });

            return slots;
          }
          // ... 其他方法保持不变
        }
      }
      </script>

      <style>
      /* 原有样式 + 新增样式 */
      .controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .controls select {
        padding: 6px 12px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        background: white;
      }

      .refresh-btn {
        padding: 6px 12px;
        background: #10b981;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s ease;
      }

      .refresh-btn:hover {
        background: #059669;
      }

      .stats-bar {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
      }

      .stat-label {
        font-size: 12px;
        color: #6b7280;
      }

      .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
      }

      .stat-value.running { color: #67C23A; }
      .stat-value.waiting { color: #E6A23C; }
      .stat-value.completed { color: #409EFF; }
      .stat-value.error { color: #F56C6C; }
      </style>
    `
  }
};
```

### 4. 全局数据源案例

```javascript
const globalDataConfig = {
  width: 1200,
  height: 600,
  dataType: 6,  // 全局数据源
  dataSet: 4001,  // 全局生产数据集ID

  // 全局数据源配置
  globalDataConfig: {
    globalDataSource: [{
      time: 10000  // 10秒刷新间隔
    }]
  },

  // 自定义数据接口参数（用于全局数据源）
  isCustom: btoa(JSON.stringify({
    paramters: [
      { key: "scope", value: "factory_wide" },
      { key: "realtime", value: "true" },
      { key: "includeMetrics", value: "efficiency,quality,oee" }
    ]
  })),

  // 数据格式化
  dataFormatter: `(data, params, refs) => {
    if (Array.isArray(data)) {
      // 全局数据源通常包含多个工厂的数据
      const result = {
        scheduleData: [],
        metrics: {
          totalOEE: 0,
          totalEfficiency: 0,
          qualityRate: 0,
          alertCount: 0
        }
      };

      // 处理排程数据
      const scheduleItems = data.filter(item => item.dataType === 'schedule');
      const lineGroups = {};

      scheduleItems.forEach(item => {
        const lineName = item.lineName;
        if (!lineGroups[lineName]) {
          lineGroups[lineName] = [];
        }

        lineGroups[lineName].push({
          workOrder: item.workOrderNo,
          product: item.productName,
          quantity: item.quantity,
          status: item.status.toLowerCase(),
          duration: item.duration,
          startTime: item.startTime,
          endTime: item.endTime,
          priority: item.priority,
          realTimeData: {
            currentQuantity: item.currentQuantity,
            efficiency: item.efficiency,
            quality: item.quality,
            oee: item.oee
          }
        });
      });

      result.scheduleData = Object.keys(lineGroups).map(lineName => ({
        lineName: lineName,
        slots: lineGroups[lineName]
      }));

      // 处理指标数据
      const metricsItems = data.filter(item => item.dataType === 'metrics');
      if (metricsItems.length > 0) {
        const metrics = metricsItems[0];
        result.metrics = {
          totalOEE: metrics.oee || 0,
          totalEfficiency: metrics.efficiency || 0,
          qualityRate: metrics.quality || 0,
          alertCount: metrics.alerts || 0
        };
      }

      return result;
    }
    return { scheduleData: [], metrics: {} };
  }`,

  option: {
    content: `
      <template>
        <div class="production-schedule-board global-version">
          <!-- 全局数据源版本 - 包含实时指标 -->
          <div class="board-header">
            <h2>全厂生产排程看板</h2>
            <div class="global-metrics">
              <div class="metric-card oee">
                <div class="metric-label">整体OEE</div>
                <div class="metric-value">{{ globalMetrics.totalOEE }}%</div>
              </div>
              <div class="metric-card efficiency">
                <div class="metric-label">生产效率</div>
                <div class="metric-value">{{ globalMetrics.totalEfficiency }}%</div>
              </div>
              <div class="metric-card quality">
                <div class="metric-label">质量合格率</div>
                <div class="metric-value">{{ globalMetrics.qualityRate }}%</div>
              </div>
              <div class="metric-card alerts">
                <div class="metric-label">告警数量</div>
                <div class="metric-value alert">{{ globalMetrics.alertCount }}</div>
              </div>
            </div>
          </div>

          <!-- 实时数据指示器 -->
          <div class="realtime-indicator">
            <span class="indicator-dot"></span>
            <span>实时数据联动</span>
            <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
          </div>

          <!-- 排程表格 - 增强版 -->
          <div class="schedule-content enhanced">
            <div
              v-for="(line, lineIndex) in scheduleData"
              :key="lineIndex"
              class="schedule-row"
            >
              <div class="line-label enhanced">
                <div class="line-name">{{ line.lineName }}</div>
                <div class="line-metrics">
                  <span class="mini-metric">效率: {{ getLineEfficiency(line) }}%</span>
                  <span class="mini-metric">OEE: {{ getLineOEE(line) }}%</span>
                </div>
              </div>
              <div class="schedule-slots">
                <div
                  v-for="(slot, slotIndex) in line.slots"
                  :key="slotIndex"
                  class="schedule-slot enhanced"
                  :class="getEnhancedSlotClass(slot)"
                  :style="getEnhancedSlotStyle(slot)"
                  @click="handleEnhancedSlotClick(slot, line)"
                >
                  <div class="slot-content enhanced">
                    <div class="work-order">{{ slot.workOrder }}</div>
                    <div class="realtime-info" v-if="slot.realTimeData">
                      <div class="progress-mini">
                        <div
                          class="progress-fill-mini"
                          :style="{ width: getProgressPercent(slot) + '%' }"
                        ></div>
                      </div>
                      <div class="efficiency-badge">{{ slot.realTimeData.efficiency }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 全局数据联动面板 -->
          <div class="data-sync-panel">
            <h3>数据联动状态</h3>
            <div class="sync-items">
              <div class="sync-item">
                <span class="sync-label">生产数据:</span>
                <span class="sync-status active">已同步</span>
              </div>
              <div class="sync-item">
                <span class="sync-label">质量数据:</span>
                <span class="sync-status active">已同步</span>
              </div>
              <div class="sync-item">
                <span class="sync-label">设备数据:</span>
                <span class="sync-status active">已同步</span>
              </div>
              <div class="sync-item">
                <span class="sync-label">人员数据:</span>
                <span class="sync-status pending">同步中</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <script>
      export default {
        props: ['dataChart'],
        data() {
          return {
            lastUpdateTime: '',
            // ... 其他数据
          }
        },
        computed: {
          scheduleData() {
            return (this.dataChart && this.dataChart.scheduleData) || [];
          },
          globalMetrics() {
            return (this.dataChart && this.dataChart.metrics) || {};
          }
        },
        watch: {
          dataChart: {
            handler() {
              this.lastUpdateTime = new Date().toLocaleTimeString();
              // 触发全局数据更新事件
              this.$emit('global-data-update', this.dataChart);
            },
            deep: true
          }
        },
        methods: {
          getLineEfficiency(line) {
            const slots = line.slots.filter(slot => slot.realTimeData);
            if (slots.length === 0) return 0;
            const total = slots.reduce((sum, slot) => sum + slot.realTimeData.efficiency, 0);
            return Math.round(total / slots.length);
          },
          getLineOEE(line) {
            const slots = line.slots.filter(slot => slot.realTimeData);
            if (slots.length === 0) return 0;
            const total = slots.reduce((sum, slot) => sum + slot.realTimeData.oee, 0);
            return Math.round(total / slots.length);
          },
          getProgressPercent(slot) {
            if (!slot.realTimeData) return 0;
            return Math.round((slot.realTimeData.currentQuantity / slot.quantity) * 100);
          },
          getEnhancedSlotClass(slot) {
            const baseClass = this.getSlotClass(slot);
            if (slot.realTimeData) {
              if (slot.realTimeData.efficiency < 70) {
                baseClass['low-efficiency'] = true;
              }
              if (slot.realTimeData.quality < 95) {
                baseClass['quality-issue'] = true;
              }
            }
            return baseClass;
          },
          getEnhancedSlotStyle(slot) {
            const baseStyle = this.getSlotStyle(slot);
            if (slot.realTimeData && slot.realTimeData.efficiency < 70) {
              baseStyle.border = '2px solid #f59e0b';
            }
            return baseStyle;
          },
          handleEnhancedSlotClick(slot, line) {
            this.handleSlotClick(slot, line);

            // 全局数据联动
            this.$emit('global-slot-select', {
              slot,
              line,
              metrics: slot.realTimeData
            });
          }
        }
      }
      </script>

      <style>
      /* 原有样式 + 全局数据源增强样式 */
      .global-version {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .global-metrics {
        display: flex;
        gap: 15px;
      }

      .metric-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        min-width: 100px;
      }

      .metric-label {
        font-size: 12px;
        opacity: 0.8;
        margin-bottom: 5px;
      }

      .metric-value {
        font-size: 20px;
        font-weight: bold;
      }

      .metric-value.alert {
        color: #fbbf24;
        animation: pulse 2s infinite;
      }

      .realtime-indicator {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 15px 0;
        font-size: 14px;
        opacity: 0.9;
      }

      .indicator-dot {
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      .line-label.enhanced {
        background: rgba(255,255,255,0.1);
        color: white;
      }

      .line-metrics {
        font-size: 10px;
        margin-top: 5px;
        opacity: 0.8;
      }

      .mini-metric {
        display: block;
        margin-bottom: 2px;
      }

      .schedule-slot.enhanced {
        position: relative;
      }

      .schedule-slot.low-efficiency {
        animation: warning-pulse 3s infinite;
      }

      .schedule-slot.quality-issue::after {
        content: '⚠';
        position: absolute;
        top: 2px;
        right: 2px;
        color: #fbbf24;
        font-size: 10px;
      }

      @keyframes warning-pulse {
        0%, 100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7); }
        50% { box-shadow: 0 0 0 4px rgba(245, 158, 11, 0); }
      }

      .realtime-info {
        margin-top: 3px;
      }

      .progress-mini {
        height: 3px;
        background: rgba(255,255,255,0.3);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 2px;
      }

      .progress-fill-mini {
        height: 100%;
        background: #10b981;
        transition: width 0.5s ease;
      }

      .efficiency-badge {
        font-size: 8px;
        background: rgba(255,255,255,0.2);
        padding: 1px 3px;
        border-radius: 2px;
      }

      .data-sync-panel {
        margin-top: 20px;
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        padding: 15px;
        border-radius: 8px;
      }

      .data-sync-panel h3 {
        margin: 0 0 10px 0;
        font-size: 16px;
      }

      .sync-items {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
      }

      .sync-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .sync-label {
        font-size: 12px;
        opacity: 0.8;
      }

      .sync-status {
        font-size: 11px;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: bold;
      }

      .sync-status.active {
        background: #10b981;
        color: white;
      }

      .sync-status.pending {
        background: #f59e0b;
        color: white;
        animation: pulse 2s infinite;
      }
      </style>
    `
  },

  // 全局数据联动事件处理
  clickFormatter: `(params, refs) => {
    console.log('全局生产排程事件:', params);

    if (params.type === 'global-slot-select') {
      // 更新所有相关的全局数据源组件
      const globalRefs = this.getItemRefs();

      Object.keys(globalRefs).forEach(key => {
        const component = globalRefs[key];
        if (component.dataType === 6) {
          // 更新其他全局数据源组件
          if (key.includes('quality')) {
            component.updateData({
              workOrderFilter: params.data.slot.workOrder
            });
          } else if (key.includes('equipment')) {
            component.updateData({
              lineFilter: params.data.line.lineName
            });
          } else if (key.includes('efficiency')) {
            component.updateData({
              timeRange: 'current_shift',
              lineFilter: params.data.line.lineName
            });
          }
        }
      });
    }

    if (params.type === 'global-data-update') {
      // 全局数据更新时，通知其他组件
      this.$eventBus.$emit('production-data-updated', params.data);
    }
  }`
};
```

## 🎯 使用建议

### 1. 数据源选择指南

- **静态数据**: 适用于演示、测试或固定排程展示
- **API接口**: 适用于常规生产环境，定期获取排程数据
- **自定义数据接口**: 适用于复杂查询需求，支持多参数筛选
- **全局数据源**: 适用于多组件联动的综合看板

### 2. 性能优化建议

- 合理设置数据刷新间隔（建议30秒以上）
- 大数据量时使用分页或时间窗口限制
- 避免在模板中进行复杂计算
- 使用计算属性缓存处理结果

### 3. 扩展功能建议

- 添加拖拽排程功能
- 集成甘特图视图
- 支持多工厂切换
- 添加导出功能
- 集成告警系统

---

**⚠️ 注意**:
- 测试案例全放到 `demo/` 目录下
- 使用当前打包好的 `dist/` 文件
- 参考其它案例使用方法
- 根据实际生产环境调整数据结构和样式
```
