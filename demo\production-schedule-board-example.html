<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产排程看板组件 - 演示案例</title>
    
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <link rel="stylesheet" href="../dist/lib/index.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-header h1 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .demo-header p {
            margin: 0;
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .demo-tabs {
            display: flex;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border: none;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: bold;
        }
        
        .demo-tab.active {
            background: #409EFF;
            color: white;
        }
        
        .demo-tab:hover:not(.active) {
            background: #e9ecef;
        }
        
        .demo-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        
        .demo-info {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #409EFF;
        }
        
        .demo-info h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .demo-info p {
            margin: 0;
            color: #5a6c7d;
            line-height: 1.6;
        }
        
        .component-container {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }
        
        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <!-- 演示头部 -->
            <div class="demo-header">
                <h1>🏭 生产排程看板组件演示</h1>
                <p>展示不同数据源类型的生产排程看板组件使用方法</p>
            </div>
            
            <!-- 选项卡 -->
            <div class="demo-tabs">
                <button 
                    class="demo-tab" 
                    :class="{ active: activeTab === 'static' }"
                    @click="switchTab('static')"
                >
                    静态数据演示
                </button>
                <button 
                    class="demo-tab" 
                    :class="{ active: activeTab === 'api' }"
                    @click="switchTab('api')"
                >
                    API接口演示
                </button>
                <button 
                    class="demo-tab" 
                    :class="{ active: activeTab === 'custom' }"
                    @click="switchTab('custom')"
                >
                    自定义数据接口
                </button>
                <button 
                    class="demo-tab" 
                    :class="{ active: activeTab === 'global' }"
                    @click="switchTab('global')"
                >
                    全局数据源
                </button>
            </div>
            
            <!-- 内容区域 -->
            <div class="demo-content">
                <!-- 静态数据演示 -->
                <div v-if="activeTab === 'static'">
                    <div class="demo-info">
                        <h3>📊 静态数据演示</h3>
                        <p>使用预定义的静态数据展示生产排程看板，适用于演示、测试或固定排程展示场景。</p>
                    </div>
                    <div class="component-container">
                        <avue-echart-vue 
                            :option="staticConfig.option"
                            :data="staticConfig.data"
                            :dataType="staticConfig.dataType"
                            :width="staticConfig.width"
                            :height="staticConfig.height"
                            @slot-click="handleSlotClick"
                        ></avue-echart-vue>
                    </div>
                </div>
                
                <!-- API接口演示 -->
                <div v-if="activeTab === 'api'">
                    <div class="demo-info">
                        <h3>🌐 API接口演示</h3>
                        <p>通过HTTP API获取实时生产排程数据，支持定时刷新和参数配置。</p>
                    </div>
                    <div class="component-container">
                        <avue-echart-vue 
                            :option="apiConfig.option"
                            :dataType="apiConfig.dataType"
                            :url="apiConfig.url"
                            :dataMethod="apiConfig.dataMethod"
                            :time="apiConfig.time"
                            :dataQuery="apiConfig.dataQuery"
                            :dataHeader="apiConfig.dataHeader"
                            :dataFormatter="apiConfig.dataFormatter"
                            :width="apiConfig.width"
                            :height="apiConfig.height"
                            @slot-click="handleSlotClick"
                        ></avue-echart-vue>
                    </div>
                </div>
                
                <!-- 自定义数据接口演示 -->
                <div v-if="activeTab === 'custom'">
                    <div class="demo-info">
                        <h3>🔧 自定义数据接口演示</h3>
                        <p>使用预定义的数据集和自定义参数获取生产排程数据，支持复杂查询和筛选。</p>
                    </div>
                    <div class="component-container">
                        <avue-echart-vue 
                            :option="customConfig.option"
                            :dataType="customConfig.dataType"
                            :dataSet="customConfig.dataSet"
                            :isCustom="customConfig.isCustom"
                            :dataFormatter="customConfig.dataFormatter"
                            :width="customConfig.width"
                            :height="customConfig.height"
                            @slot-click="handleSlotClick"
                            @filter-change="handleFilterChange"
                        ></avue-echart-vue>
                    </div>
                </div>
                
                <!-- 全局数据源演示 -->
                <div v-if="activeTab === 'global'">
                    <div class="demo-info">
                        <h3>🌍 全局数据源演示</h3>
                        <p>使用全局数据源实现多组件数据联动，支持实时指标监控和跨组件通信。</p>
                    </div>
                    <div class="component-container">
                        <avue-echart-vue 
                            :option="globalConfig.option"
                            :dataType="globalConfig.dataType"
                            :dataSet="globalConfig.dataSet"
                            :globalDataConfig="globalConfig.globalDataConfig"
                            :isCustom="globalConfig.isCustom"
                            :dataFormatter="globalConfig.dataFormatter"
                            :clickFormatter="globalConfig.clickFormatter"
                            :width="globalConfig.width"
                            :height="globalConfig.height"
                            @global-slot-select="handleGlobalSlotSelect"
                            @global-data-update="handleGlobalDataUpdate"
                        ></avue-echart-vue>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 事件日志 -->
        <div class="demo-container" style="margin-top: 20px;">
            <div class="demo-content">
                <h3>📝 事件日志</h3>
                <div style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <div v-for="(log, index) in eventLogs" :key="index" style="margin-bottom: 5px; font-size: 12px; color: #5a6c7d;">
                        <span style="color: #7f8c8d;">[{{ log.time }}]</span> {{ log.message }}
                    </div>
                    <div v-if="eventLogs.length === 0" style="text-align: center; color: #bdc3c7;">
                        暂无事件日志
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入依赖 -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <script src="../dist/cdn/element-ui/index.js"></script>
    
    <!-- 引入组件库 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        new Vue({
            el: '#app',
            data: {
                activeTab: 'static',
                eventLogs: [],
                
                // 静态数据配置
                staticConfig: {
                    width: 1200,
                    height: 600,
                    dataType: 0,
                    data: [
                        {
                            lineName: "生产线A",
                            slots: [
                                { workOrder: "WO001", product: "产品A", quantity: 100, status: "completed", duration: 2, startTime: "08:00", endTime: "10:00", priority: "normal" },
                                { workOrder: "", duration: 1 },
                                { workOrder: "WO002", product: "产品B", quantity: 150, status: "running", duration: 3, startTime: "11:00", endTime: "14:00", priority: "high" },
                                { workOrder: "", duration: 2 },
                                { workOrder: "WO003", product: "产品C", quantity: 80, status: "waiting", duration: 2, startTime: "16:00", endTime: "18:00", priority: "normal" }
                            ]
                        },
                        {
                            lineName: "生产线B",
                            slots: [
                                { workOrder: "WO004", product: "产品D", quantity: 200, status: "running", duration: 4, startTime: "08:00", endTime: "12:00", priority: "normal" },
                                { workOrder: "WO005", product: "产品E", quantity: 120, status: "waiting", duration: 3, startTime: "12:00", endTime: "15:00", priority: "high" },
                                { workOrder: "", duration: 1 },
                                { workOrder: "WO006", product: "产品F", quantity: 90, status: "error", duration: 2, startTime: "16:00", endTime: "18:00", priority: "normal" }
                            ]
                        },
                        {
                            lineName: "生产线C",
                            slots: [
                                { workOrder: "WO007", product: "产品G", quantity: 180, status: "completed", duration: 3, startTime: "08:00", endTime: "11:00", priority: "normal" },
                                { workOrder: "WO008", product: "产品H", quantity: 160, status: "running", duration: 4, startTime: "11:00", endTime: "15:00", priority: "high" },
                                { workOrder: "", duration: 1 },
                                { workOrder: "WO009", product: "产品I", quantity: 110, status: "maintenance", duration: 2, startTime: "16:00", endTime: "18:00", priority: "normal" }
                            ]
                        }
                    ],
                    option: {
                        content: this.getComponentContent()
                    }
                },
                
                // API接口配置
                apiConfig: {
                    width: 1200,
                    height: 600,
                    dataType: 1,
                    dataMethod: "get",
                    url: "/api/mock/production/schedule", // 模拟API地址
                    time: 30000,
                    dataQuery: `(url) => ({
                        date: new Date().toISOString().split('T')[0],
                        factory: 'main',
                        lines: ['A', 'B', 'C', 'D'],
                        includeCompleted: true
                    })`,
                    dataHeader: `(url) => ({
                        'Content-Type': 'application/json',
                        'X-Factory-ID': 'FACTORY001'
                    })`,
                    dataFormatter: `(data, params, refs) => {
                        // 模拟API数据处理
                        if (data && data.code === 200) {
                            return data.data || [];
                        }
                        // 返回模拟数据
                        return [
                            {
                                lineName: "API生产线A",
                                slots: [
                                    { workOrder: "API001", product: "API产品A", quantity: 120, status: "running", duration: 2, startTime: "09:00", endTime: "11:00", priority: "high" },
                                    { workOrder: "", duration: 1 },
                                    { workOrder: "API002", product: "API产品B", quantity: 180, status: "waiting", duration: 3, startTime: "12:00", endTime: "15:00", priority: "normal" }
                                ]
                            }
                        ];
                    }`,
                    option: {
                        content: this.getComponentContent()
                    }
                },
                
                // 自定义数据接口配置
                customConfig: {
                    width: 1200,
                    height: 600,
                    dataType: 5,
                    dataSet: 3001,
                    isCustom: btoa(JSON.stringify({
                        paramters: [
                            { key: "factoryId", value: "FACTORY001" },
                            { key: "dateRange", value: "today" },
                            { key: "includeLines", value: "A,B,C,D" }
                        ]
                    })),
                    dataFormatter: `(data, params, refs) => {
                        // 模拟自定义数据接口处理
                        return [
                            {
                                lineName: "自定义生产线A",
                                slots: [
                                    { workOrder: "CUSTOM001", product: "自定义产品A", quantity: 150, status: "completed", duration: 3, startTime: "08:00", endTime: "11:00", priority: "normal" },
                                    { workOrder: "CUSTOM002", product: "自定义产品B", quantity: 200, status: "running", duration: 4, startTime: "11:00", endTime: "15:00", priority: "high" }
                                ]
                            }
                        ];
                    }`,
                    option: {
                        content: this.getComponentContent()
                    }
                },
                
                // 全局数据源配置
                globalConfig: {
                    width: 1200,
                    height: 600,
                    dataType: 6,
                    dataSet: 4001,
                    globalDataConfig: {
                        globalDataSource: [{ time: 10000 }]
                    },
                    isCustom: btoa(JSON.stringify({
                        paramters: [
                            { key: "scope", value: "factory_wide" },
                            { key: "realtime", value: "true" }
                        ]
                    })),
                    dataFormatter: `(data, params, refs) => {
                        // 模拟全局数据源处理
                        return {
                            scheduleData: [
                                {
                                    lineName: "全局生产线A",
                                    slots: [
                                        { 
                                            workOrder: "GLOBAL001", 
                                            product: "全局产品A", 
                                            quantity: 180, 
                                            status: "running", 
                                            duration: 3, 
                                            startTime: "09:00", 
                                            endTime: "12:00", 
                                            priority: "high",
                                            realTimeData: {
                                                currentQuantity: 120,
                                                efficiency: 85,
                                                quality: 98,
                                                oee: 83
                                            }
                                        }
                                    ]
                                }
                            ],
                            metrics: {
                                totalOEE: 85,
                                totalEfficiency: 88,
                                qualityRate: 96,
                                alertCount: 2
                            }
                        };
                    }`,
                    clickFormatter: `(params, refs) => {
                        console.log('全局数据源点击事件:', params);
                    }`,
                    option: {
                        content: this.getComponentContent()
                    }
                }
            },
            methods: {
                switchTab(tab) {
                    this.activeTab = tab;
                    this.addEventLog(`切换到${this.getTabName(tab)}演示`);
                },
                
                getTabName(tab) {
                    const names = {
                        'static': '静态数据',
                        'api': 'API接口',
                        'custom': '自定义数据接口',
                        'global': '全局数据源'
                    };
                    return names[tab] || tab;
                },
                
                handleSlotClick(params) {
                    this.addEventLog(`点击工单: ${params.slot?.workOrder || '空槽位'} (${params.line?.lineName})`);
                },
                
                handleFilterChange(params) {
                    this.addEventLog(`筛选条件变更: ${JSON.stringify(params)}`);
                },
                
                handleGlobalSlotSelect(params) {
                    this.addEventLog(`全局工单选择: ${params.slot?.workOrder} - 效率: ${params.metrics?.efficiency}%`);
                },
                
                handleGlobalDataUpdate(params) {
                    this.addEventLog(`全局数据更新: OEE ${params.metrics?.totalOEE}%`);
                },
                
                addEventLog(message) {
                    const time = new Date().toLocaleTimeString();
                    this.eventLogs.unshift({ time, message });
                    
                    // 保持最多50条日志
                    if (this.eventLogs.length > 50) {
                        this.eventLogs = this.eventLogs.slice(0, 50);
                    }
                },
                
                getComponentContent() {
                    // 这里应该返回完整的Vue组件代码
                    // 由于篇幅限制，这里返回一个简化版本
                    return `
                        <template>
                            <div class="production-schedule-board">
                                <div class="board-header">
                                    <h2>生产排程看板</h2>
                                    <div class="time-info">
                                        <span>当前时间: {{ currentTime }}</span>
                                    </div>
                                </div>
                                <div class="schedule-content">
                                    <div v-for="(line, index) in dataChart" :key="index" class="schedule-row">
                                        <div class="line-label">{{ line.lineName }}</div>
                                        <div class="schedule-slots">
                                            <div v-for="(slot, slotIndex) in line.slots" :key="slotIndex" 
                                                 class="schedule-slot" 
                                                 :class="getSlotClass(slot)"
                                                 @click="handleSlotClick(slot, line)">
                                                <div class="slot-content">
                                                    <div class="work-order">{{ slot.workOrder }}</div>
                                                    <div class="duration" v-if="slot.duration">{{ slot.duration }}h</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <script>
                        export default {
                            props: ['dataChart'],
                            data() {
                                return {
                                    currentTime: ''
                                }
                            },
                            created() {
                                this.updateCurrentTime();
                                setInterval(this.updateCurrentTime, 1000);
                            },
                            methods: {
                                updateCurrentTime() {
                                    this.currentTime = new Date().toLocaleString();
                                },
                                getSlotClass(slot) {
                                    if (!slot.workOrder) return 'empty-slot';
                                    return 'status-' + slot.status;
                                },
                                handleSlotClick(slot, line) {
                                    if (slot.workOrder) {
                                        this.$emit('slot-click', { slot, line });
                                    }
                                }
                            }
                        }
                        </script>
                        <style>
                        .production-schedule-board { padding: 20px; background: #f8fafc; border-radius: 12px; }
                        .board-header { display: flex; justify-content: space-between; margin-bottom: 20px; }
                        .schedule-row { display: flex; margin-bottom: 10px; }
                        .line-label { width: 100px; padding: 10px; background: #f0f0f0; font-weight: bold; }
                        .schedule-slots { display: flex; flex: 1; }
                        .schedule-slot { min-width: 80px; padding: 10px; border: 1px solid #ddd; cursor: pointer; }
                        .empty-slot { background: #f9f9f9; }
                        .status-running { background: #67C23A; color: white; }
                        .status-waiting { background: #E6A23C; color: white; }
                        .status-completed { background: #409EFF; color: white; }
                        .status-error { background: #F56C6C; color: white; }
                        .status-maintenance { background: #909399; color: white; }
                        .work-order { font-weight: bold; font-size: 12px; }
                        .duration { font-size: 10px; opacity: 0.8; }
                        </style>
                    `;
                }
            },
            mounted() {
                this.addEventLog('生产排程看板演示页面加载完成');
            }
        });
    </script>
</body>
</html>
