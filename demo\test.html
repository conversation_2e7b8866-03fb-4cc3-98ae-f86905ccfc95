# 表格格式化函数修复指南

## 问题描述

您遇到的问题是格式化函数影响了其他列的显示。这是一个常见的问题，主要原因是：

1. **参数名称错误**：使用了 `(name,data)` 而不是正确的 `(item, row)`
2. **直接修改原始数据**：`data.font_Color="red"` 修改了原始数据对象
3. **全局污染**：所有列共享同一个 `row` 对象，导致样式互相影响

## 您的原始代码

```javascript
// ❌ 有问题的代码
(name,data)=>{
    if(data["type1"].includes("1")){
        data.font_Color="red"  // 直接修改原始数据
    } 
    if(data["type1"].includes("2")){ 
        data.font_Color="green" 
    } 
    return data["type1"] 
}
```

## 修复方案

### 方案1：HTML格式化（推荐）

```javascript
// ✅ 正确的代码
(item, row) => {
    let color = '#333'; // 默认颜色
    if(row["type1"] && row["type1"].includes("1")){
        color = "red";
    } else if(row["type1"] && row["type1"].includes("2")){ 
        color = "green";
    }
    return `<span style="color: ${color}">${row["type1"] || ''}</span>`;
}
```

### 方案2：条件格式化配置

如果您想要更灵活的样式控制，建议使用表格插件的条件格式化功能：

```javascript
// 列配置示例
{
    label: "类型1",
    prop: "type1",
    width: 150,
    // 主条件
    condition: 4, // 包含
    value: "1",
    cellfont: "red",
    // 多条件配置
    editableTabs: [
        {
            condition: 4, // 包含
            value: "2",
            cellfont: "green"
        }
    ]
}
```

### 方案3：复杂逻辑处理

```javascript
(item, row) => {
    const value = row[item.prop] || '';
    
    // 复杂条件判断
    if (value.includes("1") && value.includes("2")) {
        return `<span style="color: orange; font-weight: bold;">${value}</span>`;
    } else if (value.includes("1")) {
        return `<span style="color: red;">${value}</span>`;
    } else if (value.includes("2")) {
        return `<span style="color: green;">${value}</span>`;
    }
    
    return value;
}
```

## 关键修复点

### 1. 正确的参数名称

```javascript
// ❌ 错误
(name, data) => { ... }

// ✅ 正确
(item, row) => { ... }
```

### 2. 避免修改原始数据

```javascript
// ❌ 错误 - 直接修改原始数据
data.font_Color = "red";

// ✅ 正确 - 返回HTML字符串
return `<span style="color: red">${value}</span>`;
```

### 3. 安全的数据访问

```javascript
// ❌ 可能出错
row["type1"].includes("1")

// ✅ 安全访问
row["type1"] && row["type1"].includes("1")
```

## 最佳实践

### 1. 使用模板字符串

```javascript
(item, row) => {
    const value = row[item.prop] || '';
    const color = value.includes("1") ? "red" : 
                  value.includes("2") ? "green" : "#333";
    
    return `<span style="color: ${color}">${value}</span>`;
}
```

### 2. 添加图标和样式

```javascript
(item, row) => {
    const value = row[item.prop] || '';
    let icon = '';
    let color = '#333';
    
    if (value.includes("1")) {
        icon = '🔴';
        color = 'red';
    } else if (value.includes("2")) {
        icon = '🟢';
        color = 'green';
    }
    
    return `${icon} <span style="color: ${color}; font-weight: bold;">${value}</span>`;
}
```

### 3. 处理空值和异常

```javascript
(item, row) => {
    try {
        const value = row[item.prop];
        
        // 处理空值
        if (!value) {
            return '<span style="color: #ccc;">-</span>';
        }
        
        // 类型检查
        if (typeof value !== 'string') {
            return String(value);
        }
        
        // 业务逻辑
        let color = '#333';
        if (value.includes("1")) {
            color = "red";
        } else if (value.includes("2")) {
            color = "green";
        }
        
        return `<span style="color: ${color}">${value}</span>`;
    } catch (error) {
        console.error('格式化函数出错:', error);
        return row[item.prop] || '';
    }
}
```

## 测试您的修复

1. 打开 `demo/table-formatter-fix-example.html`
2. 对比问题演示和解决方案的效果
3. 查看最佳实践部分的高级用法

## 总结

- ✅ 使用正确的参数名称 `(item, row)`
- ✅ 返回HTML字符串而不是修改原始数据
- ✅ 添加安全检查避免运行时错误
- ✅ 使用条件格式化配置处理复杂样式需求
- ✅ 考虑使用图标和更丰富的视觉效果

这样修复后，您的格式化函数将只影响当前列，不会干扰其他列的显示。
