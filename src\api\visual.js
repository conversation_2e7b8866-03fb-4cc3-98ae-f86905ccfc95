import { url ,urls} from '@/config';
import { config } from '@/option/config'
import request from '../axios'



//获得模板数据

export const getCategory = (params) => request({
  url: url + 'api/kanban/kanbanGroup/GetList',
  method: 'get',
  params: params
});
//获取模板库标签
export const getall = (params) => request({
  url: url + 'api/kanban/kanbanLabel/getall',
  method: 'get',
  params:params
});
//获取大屏列表
export const getList = (params) => request({
    url: url + 'api/kanban/kanban/GetList',
    method: 'get',
    params: params
  });
  // 根据分类id查询看板数据
export const GetPageListByQueryJson = (params) => request({
  url: url + 'api/kanban/kanban/GetPageList',
  method: 'get',
  params:params
});
//复制看板
export const copyObj = (id) => request({
  url: url + 'api/kanban/kanban/Copy',
  method: 'get',
  params: {
    id: id
  }
});
//更新看板数据
export const updateaddObj = (data) => request({
  url: url + 'api/kanban/kanban/Update',
  method: 'post',
  data: data
});
//新增看板数据
export const kanbanGroupAddObj = (data) => request({
  url: url + 'api/kanban/kanbanGroup/Add',
  method: 'post',
  data: data
});
export const addObj = (data,params) => request({
  url: url + 'api/kanban/kanban/AddKanban',
  method: 'post',

data: {
  Kanban: {
    CPASSWORD: data.CPASSWORD,
    CBACKGROUND_URL: data.CBACKGROUND_URL,
    CGROUP_ID: data.CGROUP_ID,
    CTITLE: data.CTITLE,
    CKANBAN_TYPE:0,
    CWIDTH:data.CWIDTH,
    CHEIGHT:data.CHEIGHT
  },
  KanbanConfig: {
     CDETAIL:!!params?params.CDETAIL:  JSON.stringify(Object.assign(config, {
      name: data.CTITLE,
      width: data.CWIDTH,
      height: data.CHEIGHT,
      backgroundImage:data.CBACKGROUND_URL,
    })),
    CCOMPONET:!!params?params.CCOMPONET: '[]'
  },
}
});
export const getObj = (id) => request({
  url: url + 'api/kanban/kanban/Detail',
  method: 'get',
  params: {
    id
  }
});
//保存图片
export const uploadImg = (data) => request({
  url: url + 'api/kanban/kanban/UploadFile',
  method: 'post',
   data:data,

});
//保存大屏
export const updateComponent = (data) => request({
  url: url + 'api/kanban/kanban/UpdateKanban',
  method: 'post',
  data: data
});
//根据id删除看板
export const delObj = (data) => request({
  url: url + 'api/kanban/kanban/Delete',
  method: 'post',
  data:data
     
  
});
//查询分类
export const GetListByQueryJson = (params) => request({
  url: url + 'api/kanban/kanbanGroup/GetAll',
  method: 'get',
  params: params
});

//删除分类
export const DeleteByID = (data) => request({
  url: url + 'api/kanban/kanbanGroup/Delete',
  method: 'post',
  data: data
});
//更新看板数据
export const updateObj = (data) => request({
  url: url + 'api/kanban/kanbanGroup/Update',
  method: 'post',
  data: data
});
//数据源管理接口
export const GetDataType = (params) => request({
  url: url + 'api/MD/DataSource/GetDataType',
  method: 'get',
  params:params
});
//数据源列表
export const GetPagelist = (params) => request({
  url: url + 'api/MD/DataSet/GetPagelist',
  method: 'get',
  params:params
});
//请求数据表数据
export const GetTabels = (data) => request({
  url: url + 'api/MD/DataSet/GetTabels',
  method: 'post',
  data: data
});
//运行脚本
export const QueryTableDatas = (data) => request({
  url: url + 'api/MD/DataSet/QueryTableDatas',
  method: 'post',
  data: data
});
//数据源测试
export const ConnectionTest = (data) => request({
  url: url + 'api/MD/DataSource/ConnectionTest',
  method: 'post',
  data: data
});
//添加数据源
export const AddDatasource = (data) => request({
  url: url + 'api/MD/DataSource/Add',
  method: 'post',
  data: data
});
//修改数据源

export const UpdateDatasource = (data) => request({
  url: url + 'api/MD/DataSource/Update',
  method: 'post',
  data: data
});
//删除数据元
export const DeleteDatasource = (data) => request({
  url: url + 'api/MD/DataSource/Delete',
  method: 'post',
  data: data
});
//根据分页获取数据源
export const DataSourceGetPagelist = (params) => request({
  url: url + 'api/MD/DataSource/GetPagelist',
  method: 'get',
  params:params
});
//数据源上传
export const UploadFile = (data) => request({
  url: url + 'api/MD/DataSource/UploadFile',
  method: 'post',
  data: data
});
//分页获取数据集列表
// export const DataSetGetPagelist = (params) => request({
//   url: url + 'api/MD/DataSet/GetPagelist',
//   method: 'get',
//   params:params
// });
//数据源标签
export const GetDataSetLabels = (params) => request({
  url: url + 'api/MD/DataSet/GetDataSetLabels',
  method: 'get',
  params:params
});
//获取数据源列表
export const GetDataSources = (params) => request({
  url: url + 'api/MD/DataSet/GetDataSources',
  method: 'get',
  params:params
});
//添加数据集


export const AddDataSet = (data) => request({
  url: url + 'api/MD/DataSet/Add',
  method: 'post',
  data: data
});
//添加看板模板
export const kanbanTemplate = (data) => request({
  url: url + 'api/kanban/kanbanTemplate/Add',
  method: 'post',
  data: data
});
//获取所用模板列表
export const TemplateGetPageList = (params) => request({
  url: url + 'api/kanban/kanbanTemplate/GetAll',
  method: 'get',
  params:params
});
//根据id删除看板模板

export const kanbanTemplateDeleteByID = (data) => request({
  url: url + 'api/kanban/kanbanTemplate/Delete',
  method: 'post',
  data:data
});
//复制看板模板
export const kanbanTemplateCopy = (params) => request({
  url: url + 'api/kanban/kanbanTemplate/Copy',
  method: 'get',
  params:params
});
//添加到模板库
export const AddTemplate = (data) => request({
  url: url + 'api/kanban/kanbanTemplate/AddTemplate',
  method: 'post',
  data:data
});
//编辑模板
export const UpdateTemplate = (data) => request({
  url: url + 'api/kanban/kanbanTemplate/Update',
  method: 'post',
  data:data
});



export const Import1 = (data) => {
  return request({
    url:url + 'api/kanban/KanbanTemplate/Import',
    method: 'post',
    data
  })
}
export const Export1 = (data) => {
  return request({
    url:url + 'api/kanban/KanbanTemplate/Export',
    method: 'post',
    data
  })
}
export const PushData = (data) => {
  return request({
    url:url + 'api/kanban/KanbanTemplate/PushData',
    method: 'post',
    data
  })
}



export const Import2 = (data) => {
  return request({
    url:url + 'api/kanban/KanbanGroup/Import',
    method: 'post',
    data
  })
}
export const Export2 = (data) => {
  return request({
    url:url + 'api/kanban/KanbanGroup/Export',
    method: 'post',
    data
  })
}
export const PushData1 = (data) => {
  return request({
    url:url + 'api/kanban/KanbanGroup/PushData',
    method: 'post',
    data
  })
}







export const Import3 = (data) => {
  return request({
    url:url + 'api/kanban/Kanban/Import',
    method: 'post',
    data
  })
}
export const Export3 = (data) => {
  return request({
    url:url + 'api/kanban/Kanban/Export',
    method: 'post',
    data
  })
}
export const PushData2 = (data) => {
  return request({
    url:url + 'api/kanban/Kanban/PushData',
    method: 'post',
    data
  })
}



