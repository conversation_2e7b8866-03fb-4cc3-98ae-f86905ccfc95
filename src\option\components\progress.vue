<!-- 进度条配置 -->
<template>
  <div>
    <el-form-item label="描述" :label="`${$t('components.progress.Description')}`">
      <avue-input v-model="main.activeOption.describe">
      </avue-input>
    </el-form-item>
    <el-form-item label="类型" :label="`${$t('components.progress.Type')}`">
      <avue-radio v-model="main.activeOption.type"
                  :dic="dicOption.line">
      </avue-radio>
    </el-form-item>
    <el-form-item label="宽度" :label="`${$t('components.progress.Width')}`">
      <avue-input-number v-model="main.activeOption.strokeWidth"
                         :max="50"></avue-input-number>
    </el-form-item>
    <el-form-item label="颜色" :label="`${$t('components.progress.Color')}`">
      <avue-input-color v-model="main.activeOption.borderColor"></avue-input-color>
    </el-form-item>
    <el-form-item label="背景颜色" :label="`${$t('components.progress.BackgroundColor')}`">
      <avue-input-color v-model="main.activeOption.defineBackColor"></avue-input-color>
    </el-form-item>
    <el-form-item label="字体大小" :label="`${$t('components.progress.FontSize')}`">
      <avue-input-number v-model="main.activeOption.fontSize"
                         :max="200"></avue-input-number>
    </el-form-item>
    <el-form-item label="字体颜色" :label="`${$t('components.progress.FontColor')}`">
      <avue-input-color v-model="main.activeOption.color"></avue-input-color>
    </el-form-item>
    <el-form-item label="文字粗细" :label="`${$t('components.progress.FontWeight')}`">
      <avue-select v-model="main.activeOption.FontWeight"
                   :dic="dicOption.fontWeight">
      </avue-select>
    </el-form-item>
    <el-form-item label="前缀字体大小" :label="`${$t('components.progress.PrefixFontSize')}`">
      <avue-input-number v-model="main.activeOption.suffixFontSize"
                         :max="200"></avue-input-number>
    </el-form-item>
    <el-form-item label="前缀字体颜色" :label="`${$t('components.progress.PrefixFontColor')}`">
      <avue-input-color v-model="main.activeOption.suffixColor"></avue-input-color>
    </el-form-item>
    <el-form-item label="前缀文字粗细" :label="`${$t('components.progress.PrefixFontWeight')}`">
      <avue-select v-model="main.activeOption.suffixFontWeight"
                   :dic="dicOption.fontWeight">
      </avue-select>
    </el-form-item>
  </div>
</template>

<script>
import { dicOption } from '@/option/config'
export default {
  name: 'progress',
  data () {
    return {
      dicOption: dicOption
    }
  },
  inject: ["main"]
}
</script>

<style>
</style>