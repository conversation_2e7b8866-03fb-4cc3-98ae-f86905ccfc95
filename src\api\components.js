import { url,urls } from '@/config';
import request from '../axios'
export const getList = (params) => request({
  url: url + 'api/kanban/KanbanComponent/GetList',
  method: 'get',
  params: params
});

//保存图片
export const UploadFile = (data) => request({
  url: url + 'api/kanban/KanbanComponent/UploadFile',
  method: 'post',
   data:data,
});

export const getObj = (id) => request({
  url: url + 'api/kanban/KanbanComponent/Detail',
  method: 'get',
  params: {
    id
  }
});

export const addObj = (data) => request({
  url: url + 'api/kanban/KanbanComponent/Save',
  method: 'post',
  data: data
});
export const updateObj = (data) => request({
  url: url + 'api/kanban/KanbanComponent/Update',
  method: 'post',
  data: data
});



export const delObj = (data) => request({
  url: url + 'api/kanban/KanbanComponent/Delete',
  method: 'post',
  data: data
  // params: {
  //   ids: id
  // }
});