import { urls } from '@/config';
import request from '../axios'
export const getList = (params) => request({
  url: urls + '/category/list',
  method: 'get',
  params: params
});



export const getObj = (id) => request({
  url: urls + '/category/detail',
  method: 'get',
  params: {
    id
  }
});

export const addObj = (data) => request({
  url: urls + '/category/save',
  method: 'post',
  data: data
});
export const updateObj = (data) => request({
  url: urls + '/category/update',
  method: 'post',
  data: data
});



export const delObj = (id) => request({
  url: urls + '/category/remove',
  method: 'post',
  params: {
    ids: id
  }
});