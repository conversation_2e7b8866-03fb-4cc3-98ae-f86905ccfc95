<template>
    <div   :class="isCilent?'kanbanLabel kanbanLabel1':'kanbanLabel'">
        <vxe-toolbar ref="xToolbar1">
            <template #buttons>
            <el-button
                type="primary"
                icon="vxe-icon-add"
                size="small"
                @click="addProject"
                >&nbsp;
                <!-- 新增 -->
                {{ $t('page.kanbanLabel.Add') }}
                </el-button
            >
            <el-upload
           
           style="margin: 0 10px;"
           :show-file-list="false"
           :httpRequest="httpRequest4"
           action=""
           ref="upload"
           accept=".json"
         >
         <el-button
         icon="el-icon-upload2
"
         type="primary"
         size="mini"
         @click="import_part"
         >
         <!-- 导入 -->
         {{ $t('page.kanbanLabel.Import') }}
         </el-button
       >
         </el-upload>

       <el-button
         icon="el-icon-download"
         type="primary"
         size="mini"
         @click="export_part"
         >
         <!-- 导出 -->
         {{ $t('page.kanbanLabel.Export') }}
         </el-button
       >  
              <el-button
              class="pinBtnOnCla"
              icon="el-icon-tickets"
              type="primary"
              size="small"
           
              @click="synchronous()"
            >
              <!-- 同步 -->
              {{ $t('page.kanbanLabel.Synchronize') }}
            </el-button>
           
            <!-- &nbsp;<el-button
                icon="el-icon-download"
                size="small"
                type="primary"
                @click="exportDataEvent"
                >导出</el-button
            > -->
    
            <!-- <el-button type="primary" size="small" @click="showBtnOpen">展开</el-button>
            <el-button type="primary" size="small" @click="showBtnClose">关闭</el-button> -->
            </template>
            <template #tools>
            <span style="color: #333; font-size: 14px">
                <!-- 关键词 -->
                {{ $t('page.kanbanLabel.Keyword') }}
            </span>
            <el-input style="width: 180px; margin: 0 10px" size="mini"     @keyup.enter.native="onSubmit"
                v-model="formData.name"  placeholder1="关键词" :placeholder="`${$t('page.kanbanLabel.Keyword')}`"
            />
            <el-button type="primary" size="small" icon="el-icon-search" @click="onSubmit" title1="查询" :title="`${$t('page.kanbanLabel.Query')}`"></el-button>
            <el-button type="primary" size="small" icon="el-icon-refresh-right" @click="reset" title1="重置" :title="`${$t('page.kanbanLabel.Reset')}`"></el-button>
            </template>
        </vxe-toolbar> 
        <vxe-table
            ref="xTable"
            border align="center"
            show-overflow
            keep-source
            :loading="loading"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            :tree-config="{
            transform: true,
            rowField: 'CID',
            parentField: 'CPARENT_ID',
            }"
            :data="tableData"
        >
        <vxe-column type="checkbox" width="60"></vxe-column>
            <vxe-column width="60" type="seq" title="序号" :title="`${$t('page.kanbanLabel.SerialNumber')}`" class="drag-btn">
                
            </vxe-column>

            <vxe-column field="CCODE" title="标签编号" :title="`${$t('page.kanbanLabel.LabelCode')}`" align="left" tree-node></vxe-column>
            <vxe-column   field="CNAME" title="标签名称" :title="`${$t('page.kanbanLabel.LabelName')}`" align="left"></vxe-column>
            <vxe-column   field="CDESC" title="标签备注" :title="`${$t('page.kanbanLabel.LabelRemarks')}`" align="left"></vxe-column>
            <vxe-column field="address" width="195" fixed="right"  title="操作" :title="`${$t('page.kanbanLabel.Operation')}`">
            <template #default="{ row }">
                <el-button type="text" @click="edit(row)"> 
                    <!-- 编辑  -->
                    {{ $t('page.kanbanLabel.Edit') }}
                </el-button>
                <el-button type="text" @click="deletes(row)"   style="color:#f56c6c">
                    <!-- 删除 -->
                    {{ $t('page.kanbanLabel.Delete') }}
                </el-button>
            </template>
            </vxe-column>
        </vxe-table>
        <vxe-pager
            background
            align="right"
            @page-change="pageChange"
            :current-page.sync="page.currentPage"
            :page-size.sync="page.pageSize"
            :total="page.totalResult"
            :layouts="[
                'PrevJump',
                'PrevPage',
                'JumpNumber',
                'NextPage',
                'NextJump',
                'Sizes',
                'FullJump',
                'Total',
            ]"
        >
        </vxe-pager>
        <!-- <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
            <span class="dialogContent">
            <i class="el-icon-error" style="color: #F56C6C;width: 28px;height: 28px;font-size: 28px;"></i>&nbsp;
            <span>你确定删除当前看板标签【 {{ objRow.CNAME }} 】？</span> 
            </span>
            <span slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
            <el-button size="small" type="primary" @click="delOk">确 定</el-button>
            </span>
        </el-dialog> -->
        <OrganizationalModel
            ref="child"
            :title="title"
            :formOptions="formOptions"
            :childEvent="reset"
        />
    </div>
</template>
<script>
import { useLanguageSetting } from "@/utils/useLanguageSetting"
 import OrganizationalModel from "./organizationalModel.vue";
 import { getall,kanbanLabelDelete,Change,PushData,Import1,Export1 } from "./Api";
 import Sortable from 'sortablejs';
 import { getCookie } from '@/utils/setStore.js'
export default {
    name: 'kanbanLabel',
    components: {
      OrganizationalModel
    },
    data(){
        return {
            useLanguageSettingFn: useLanguageSetting(this),
            formData: {
                name: '',
               
            },
            tableData: [],
            loading: true,
            title: '',
            formOptions:[],
            objRow: {},
            dialogVisible: false,
            page:{
                currentPage: 1,
                pageSize: 10,
                totalResult: 0,
            }
        }
    },
    created() {
      this.$nextTick(() => {
        // 手动将表格和工具栏进行关联
        this.$refs.xTable.connect(this.$refs.xToolbar1);
        this.rowDrop()
      });
    },
    mounted() {
        this.useLanguageSettingFn.setLanguage()
        this.GetList()
    },
    computed: {
   
   isCilent(){
  return getCookie('client')?true:false
 }
 },
    methods: {
       //同步
  synchronous(){
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
      PushData(ids).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
        }else{
          this.$message.error(res.data.Content)
        }
      })
    },
    httpRequest4(file){
   const reader = new FileReader();
 // 异步处理文件数据
 reader.readAsText(file.file, 'UTF-8');
 // 处理完成后立马触发 onload
 reader.onload = fileReader => {
     const fileData = fileReader.target.result;
     if(fileData){
      var text=JSON.parse(fileData)


      Import1(text).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
          this.$message.success(res.data.Content)
          this.getdatasoures()

        }else{
          this.$message.error(res.data.Content)
        }
      })
     }
    
     // 上面的两个输出相同
 };

    },
    import_part() {
      console.log(111);
    },
    export_part() {
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      const ids = selectRecords.map(item => item.CID);
     Export1(ids).then(res=>{
        res=res.data
        if(res.code===200&&res.data.Success){
        let link = document.createElement("a");
        link.download = "模板标签.json";
        link.href = "data:text/plain," + JSON.stringify(res.data.Datas);
        link.click();
        }else{
          this.$message.error(res.data.Content)
        }
     })
     
    },
        SortMethod({ data, column, property, order }) {
            console.log(data, column, property, order,'data, column, property, order');
        },
        // 行拖拽保存 
        rowChangeSeven(newRowId, odlRowId) {
            Change({
            cid: odlRowId,
            rid: newRowId,
            }).then(res => {
                res=res.data
            if(res.code === 200 && res.data.Success) {
              
                this.$message({
                  message:res.data.Content,
                  type: "success",
                });
            }else{
                this.$message({
                message: res.data.Content,
                type: "error",
                });
            }
            })
        },
        // 动态设置表格行样式类名 
        rowClassName ({ row, rowIndex }) {
            if ([null, 0].includes(rowIndex)) {
            return 'row-green'
            }
        },
        rowDrop () {
            this.$nextTick(() => {
            
            let _this = this;
            const $table = this.$refs.xTable; 
            let el = $table.$el.querySelector('.vxe-table--body tbody')
            console.log(el,'eee');
            let ops = {
                handle: '.vxe-body--row',
                //停靠位置样式
                draggable: '.vxe-body--row',
                filter: '.row-green',
                animation: 500,
                direction: 'vertical',
                onChoose({ oldIndex }) {
                _this.selectedTableData =  _this.arrayTreeSetLevel($table.getTableData()['fullData'])
                _this.flatSelectedTableData = _this.flatTreeData(_this.selectedTableData); // 把树形的结构转为列表再进行拖拽
                _this.$set(_this,"tableData",_this.flatSelectedTableData);
                _this.drawOldIndex = oldIndex;
                },
                onMove: function ({ related }) {
                const oldRow = _this.flatSelectedTableData[_this.drawOldIndex];
                const newRow = _this.flatSelectedTableData[related.rowIndex];
                },
                onEnd({ newIndex, oldIndex }) {
                if (oldIndex !== newIndex) {
                    const oldRow = _this.flatSelectedTableData[_this.drawOldIndex];
                    const newRow = _this.flatSelectedTableData[newIndex];
                    _this.rowChangeSeven(newRow.CID, oldRow.CID)
                }
                },
            }
            Sortable.create(el, ops);
            
            })
        },
        // 将树数据转化为平铺数据
        flatTreeData(treeData=[], childKey = 'children') {
            const arr = [];
            const expanded = (data) => {
            if (data && data.length > 0) {
                data
                .filter((d) => d)
                .forEach((e) => {
                    arr.push(e);
                    expanded(e[childKey] || []);
                });
            }
            };
            expanded(treeData);
            
            return arr;
        },
        // 给树形的数据去添加每一层的层级
        arrayTreeSetLevel(array, levelName = "level", childrenName = "children") {
            if (!Array.isArray(array)) {
            return [];
            }
            
            const recursive = (array, level = 0) => {
            level++;
            return array.map((v) => {
                console.log(level,'level');
                v[levelName] = level;
                const child = v[childrenName];
                if (child && child.length) {
                recursive(child, level);
                }
                return v;
            });
            };
            return recursive(array);
        },
        GetList () {
            let params = {
                condition: this.formData.name,
                start: this.page.currentPage,
                length: this.page.pageSize
            }
            getall(params).then((res) => {
                res=res.data
               
                if (res.code == 200 && res.data.Success) {
                    this.loading = false
                    this.tableData = this.flatten(res.data.Datas)
                    this.formOptions = this.flatten(res.data.Datas)
                    this.formOptions.push({
                        "CNO": "0",
                        "CNAME": "全部",
                        "CPARENT_ID": null,
                        "CID": 0,
                    })
                    this.page.totalResult=res.data.TotalRows
                } else {
                    this.$message({
            message: res.data.Content || res.message.msg,
            type: "error",
          });
                    this.dialogVisible = false;
                }
            })
        },
        flatten(arr){
            return arr.reduce((result,item)=> {
                return result.concat(item,(Array.isArray(item.CHILDS) ? this.flatten(item.CHILDS) : []))
            }, []);
        },
        pageChange(value) {
            this.loading = true;
            this.page.currentPage = value.currentPage,
            this.page.pageSize = value.pageSize,
            this.GetList()
        },
        //新增项目
        addProject(){
            this.$nextTick(() => {
                this.$refs.child.editEvent(false);
            });
            this.title = this.$t('page.kanbanLabel.AddTemplateTag')//"添加模板标签";
        },
        //编辑项目
        edit(row) {
            this.$nextTick(() => {
            this.$refs.child.editEvent(true, row);
            });
            this.title = this.$t('page.kanbanLabel.EditTemplateTag')//"编辑模板标签";
        },
         //删除
        deletes(row) {
            // this.objRow = row;
            // this.dialogVisible = true;
            this.$confirm(`${this.$t('message.PermanentDeletion')}`, `${this.$t('message.Prompt')}`, {
        confirmButtonText: `${this.$t('message.confirmButtonText')}`,
        cancelButtonText: `${this.$t('message.cancelButtonText')}`,
        iconClass: "el-icon-error",
      })
        .then(() => {
            kanbanLabelDelete({ CID: row.CID}).then((res) => {
                res=res.data
            if (res.code == 200 && res.data.Success) {
                this.$message({
                  message:res.data.Content,
                  type: "success",
                });
                this.loading = true;
                this.GetList();
                this.dialogVisible = false;
            } else {
                this.$message({
            message: res.data.Content || res.message.msg,
            type: "error",
          });
                this.dialogVisible = false;
            }
            });
        })
        .catch(() => {

        });
        },
        delOk() {
            kanbanLabelDelete({ CID: this.objRow.CID}).then((res) => {
                res=res.data
            if (res.code == 200 && res.data.Success) {
                this.$message({
                  message:res.data.Content,
                  type: "success",
                });
                this.loading = true;
                this.GetList();
                this.dialogVisible = false;
            } else {
                this.$message({
            message: res.data.Content || res.message.msg,
            type: "error",
          });
                this.dialogVisible = false;
            }
            });
        },
        showBtnOpen(value){
            this.$refs.xTable.setAllTreeExpand(value);
        },
        showBtnClose(value){
            this.$refs.xTable.clearTreeExpand();
        },
        onSubmit(){
            this.loading = true;
            this.GetList()
        },
        //重置
        reset(){
            this.loading = true;
            this.formData.name = ''
            this.GetList();
        }
    },
}
</script>
<style lang="scss">
    .kanbanLabel {
    padding: 5px 10px 0px 10px;
    background: #fff;
  
    .vxe-table--render-default .vxe-body--column>.vxe-cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 3;
    }
    .vxe-body--column {
      cursor: move;
      font-size: 12px;
    }
    .sortable-row-demo .vxe-body--row.sortable-ghost,
    .sortable-row-demo .vxe-body--row.sortable-chosen {
      background-color: #dfecfb;
    }
    .dialogContent {
      display: flex;
      height: 28px;
      justify-content: flex-start;
      align-items: center;
    }
     .vxe-table--body-wrapper {
   height: calc(100vh - 273px);
  }
  }
  .kanbanLabel1{
    .vxe-table--body-wrapper {
   height: calc(100vh - 173px);
  }
  }
</style>