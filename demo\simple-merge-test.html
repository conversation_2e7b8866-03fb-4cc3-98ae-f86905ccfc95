<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单合并列测试</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
        }
        .control-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .table-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #409eff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 简单合并列测试</h1>
        
        <div class="controls">
            <div class="control-item">
                <label>
                    <input type="checkbox" id="enableMerge" checked> 启用合并功能
                </label>
            </div>
            <div class="control-item">
                <label>
                    <input type="checkbox" id="showBorder" checked> 显示边框
                </label>
            </div>
            <div class="control-item">
                <button onclick="updateTable()">更新表格</button>
            </div>
            <div class="control-item">
                <button onclick="generateNewData()">生成新数据</button>
            </div>
        </div>

        <div class="table-container">
            <div id="app"></div>
        </div>

        <div class="info">
            <h3>测试说明：</h3>
            <ul>
                <li>✅ 这是一个简化的合并列功能测试</li>
                <li>✅ 使用原生Element UI表格组件</li>
                <li>✅ 实现了基本的单元格合并逻辑</li>
                <li>✅ 数据按工序和工站排序，确保相同值相邻</li>
            </ul>
            <p><strong>当前状态：</strong> <span id="status">正在初始化...</span></p>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    
    <script>
        // 测试数据
        let tableData = [
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061001", operator: "费学进" },
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061002", operator: "张三" },
            { workstation: "免片线路", station: "前处理1线", serialNumber: "J25061003", operator: "李四" },
            { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062001", operator: "王五" },
            { workstation: "免片线路", station: "前处理2线", serialNumber: "J25062002", operator: "赵六" },
            { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063001", operator: "孙七" },
            { workstation: "外层线路", station: "前处理3线", serialNumber: "J25063002", operator: "周八" },
            { workstation: "外层线路", station: "前处理4线", serialNumber: "J25064001", operator: "吴九" },
            { workstation: "内层显影", station: "显影1线", serialNumber: "J25071001", operator: "郑十" },
            { workstation: "内层显影", station: "显影1线", serialNumber: "J25071002", operator: "钱一" }
        ];

        // Vue应用
        let app = new Vue({
            el: '#app',
            data: {
                tableData: tableData,
                enableMerge: true,
                showBorder: true
            },
            template: `
                <el-table 
                    :data="tableData" 
                    :border="showBorder"
                    :span-method="enableMerge ? spanMethod : null"
                    style="width: 100%;">
                    <el-table-column type="index" label="#" width="60"></el-table-column>
                    <el-table-column prop="workstation" label="工序" width="120"></el-table-column>
                    <el-table-column prop="station" label="工站" width="120"></el-table-column>
                    <el-table-column prop="serialNumber" label="流程卡号" width="150"></el-table-column>
                    <el-table-column prop="operator" label="操作员" width="100"></el-table-column>
                </el-table>
            `,
            methods: {
                spanMethod({ row, column, rowIndex, columnIndex }) {
                    // 只对工序列(索引1)和工站列(索引2)进行合并
                    if (columnIndex !== 1 && columnIndex !== 2) {
                        return [1, 1];
                    }
                    
                    const prop = column.property;
                    const currentValue = row[prop];
                    
                    // 如果当前值为空，不合并
                    if (currentValue === null || currentValue === undefined || currentValue === '') {
                        return [1, 1];
                    }
                    
                    // 查找连续相同值的行数
                    let rowspan = 1;
                    
                    // 向下查找相同值
                    for (let i = rowIndex + 1; i < this.tableData.length; i++) {
                        if (this.tableData[i][prop] === currentValue) {
                            rowspan++;
                        } else {
                            break;
                        }
                    }
                    
                    // 向上查找，如果上一行有相同值，则当前行不显示
                    for (let i = rowIndex - 1; i >= 0; i--) {
                        if (this.tableData[i][prop] === currentValue) {
                            return [0, 0]; // 不显示当前单元格
                        } else {
                            break;
                        }
                    }
                    
                    return [rowspan, 1];
                }
            },
            mounted() {
                updateStatus();
            }
        });

        // 更新表格
        function updateTable() {
            app.enableMerge = document.getElementById('enableMerge').checked;
            app.showBorder = document.getElementById('showBorder').checked;
            updateStatus();
        }

        // 生成新数据
        function generateNewData() {
            const workstations = ['免片线路', '外层线路', '内层显影', '钻孔工序'];
            const stations = ['前处理1线', '前处理2线', '前处理3线', '前处理4线', '显影1线', '显影2线'];
            const operators = ['费学进', '张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十', '钱一'];
            
            tableData = [];
            let serialNum = 1;
            
            workstations.forEach(workstation => {
                const stationCount = Math.floor(Math.random() * 2) + 2; // 2-3个工站
                for (let i = 0; i < stationCount; i++) {
                    const station = stations[Math.floor(Math.random() * stations.length)];
                    const recordCount = Math.floor(Math.random() * 3) + 2; // 2-4条记录
                    
                    for (let j = 0; j < recordCount; j++) {
                        tableData.push({
                            workstation: workstation,
                            station: station,
                            serialNumber: `J2506${String(serialNum).padStart(4, '0')}`,
                            operator: operators[Math.floor(Math.random() * operators.length)]
                        });
                        serialNum++;
                    }
                }
            });
            
            // 按工序和工站排序
            tableData.sort((a, b) => {
                if (a.workstation !== b.workstation) {
                    return a.workstation.localeCompare(b.workstation);
                }
                return a.station.localeCompare(b.station);
            });
            
            app.tableData = [...tableData];
            updateStatus();
        }

        // 更新状态信息
        function updateStatus() {
            const status = document.getElementById('status');
            status.innerHTML = `
                合并功能: ${app.enableMerge ? '启用' : '禁用'} | 
                边框显示: ${app.showBorder ? '启用' : '禁用'} | 
                数据条数: ${app.tableData.length}
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简单合并列测试页面已加载');
            updateStatus();
        });
    </script>
</body>
</html>
