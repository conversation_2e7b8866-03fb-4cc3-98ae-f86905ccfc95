<!-- 表格配置 -->
<template>
  <div>
    <el-form-item label="开启排名" :label="`${$t('components.table.OpenRanking')}`">
      <avue-switch v-model="main.activeOption.index"></avue-switch>
    </el-form-item>
    <el-form-item v-show="main.activeOption.index" label="排名宽度" :label="`${$t('components.table.RankingWidth')}`">
      <avue-input-number v-model="main.activeOption.indexWidth">
      </avue-input-number>
    </el-form-item>
    <el-form-item label="边框" :label="`${$t('components.table.Border')}`">
      <avue-switch v-model="main.activeOption.border"> </avue-switch>
    </el-form-item>
    <el-form-item label="圆角" :label="`${$t('components.table.RoundCorner')}`">
      <avue-switch v-model="main.activeOption.roundedTable"> </avue-switch>
    </el-form-item>
    <el-form-item label="追加模式" :label="`${$t('components.table.AppendMode')}`">
      <avue-switch v-model="main.activeOption.dataAppend"> </avue-switch>
    </el-form-item>
    <el-form-item label="启用合并列" :label="`${$t('components.table.EnableMergeColumn')}`">
      <avue-switch v-model="main.activeOption.enableMergeColumn"> </avue-switch>
    </el-form-item>
    <el-form-item label="开启滚动" :label="`${$t('components.table.EnableScrolling')}`">
      <avue-switch v-model="main.activeOption.scroll"> </avue-switch>
    </el-form-item>
    <template v-if="main.activeOption.scroll">
      <el-form-item label="滚动间隔" :label="`${$t('components.table.ScrollInterval')}`">
        <avue-input-number v-model="main.activeOption.scrollTime">
        </avue-input-number>
      </el-form-item>
      <el-form-item label="滚动速度" :label="`${$t('components.table.ScrollSpeed')}`">
        <avue-input-number v-model="main.activeOption.scrollSpeed">
        </avue-input-number>
      </el-form-item>
    </template>
    <el-collapse accordion>
      <el-collapse-item title="表头设置" :title="`${$t('components.table.HeaderSettings')}`">
        <el-form-item label="显示" :label="`${$t('components.table.Display')}`">
          <avue-switch v-model="main.activeOption.showHeader"> </avue-switch>
        </el-form-item>
        <el-form-item label="字体大小" :label="`${$t('components.table.FontSize')}`">
          <avue-input-number v-model="main.activeOption.headerFontSize">
          </avue-input-number>
        </el-form-item>
        <el-form-item label="列头高度" :label="`${$t('components.table.ColumnHeaderHeight')}`">
          <avue-input-number v-model="main.activeOption.headerColHeight">
          </avue-input-number>
        </el-form-item>
        <el-form-item label="背景颜色" :label="`${$t('components.table.BackgroundColor')}`">
          <avue-input-color type="textarea" v-model="main.activeOption.headerBackground"></avue-input-color>
        </el-form-item>
        <el-form-item label="字体颜色" :label="`${$t('components.table.FontColor')}`">
          <avue-input-color type="textarea" v-model="main.activeOption.headerColor"></avue-input-color>
        </el-form-item>
        <el-form-item label="对其方式" :label="`${$t('components.table.AlignmentMethod')}`">
          <avue-select v-model="main.activeOption.headerTextAlign" :dic="dicOption.textAlign">
          </avue-select>
        </el-form-item>
      </el-collapse-item>
      <el-collapse-item title1="表格设置" :title="`${$t('components.table.TableSettings')}`">
        <el-form-item label="显示行数" :label="`${$t('components.table.NumberofDisplayedRows')}`">
          <avue-input-number v-model="main.activeOption.count">
          </avue-input-number>
        </el-form-item>
        <el-form-item label="字体大小" :label="`${$t('components.table.FontSize')}`">
          <avue-input-number v-model="main.activeOption.bodyFontSize">
          </avue-input-number>
        </el-form-item>
        <el-form-item label="对其方式" :label="`${$t('components.table.AlignmentMethod')}`">
          <avue-select v-model="main.activeOption.bodyTextAlign" :dic="dicOption.textAlign">
          </avue-select>
        </el-form-item>
        <el-form-item label="文字颜色" :label="`${$t('components.table.TextColor')}`">
          <avue-input-color type="textarea" v-model="main.activeOption.bodyColor"></avue-input-color>
        </el-form-item>
        <el-form-item label="奇行颜色" :label="`${$t('components.table.OddRowColor')}`">
          <avue-input-color type="textarea" v-model="main.activeOption.nthColor"></avue-input-color>
        </el-form-item>
        <el-form-item label="偶行颜色" :label="`${$t('components.table.EvenRowColor')}`">
          <avue-input-color type="textarea" v-model="main.activeOption.othColor"></avue-input-color>
        </el-form-item>
      </el-collapse-item>
      <el-collapse-item title1="表格列设置" :title="`${$t('components.table.TableColumnSettings')}`">
       
        <avue-crud :option="tableOption" :data="main.activeOption.column" v-model="form" @row-save="rowSave"
          @row-del="rowDel" @row-update="rowUpdate">
          <template slot="formatterForm" slot-scope="{}">
            <el-tabs v-model="formatterForm_activeName" @tab-click="handleClick" type="card" editable
              @edit="handleTabsEdit">
              <el-tab-pane :closable="false" label="默认配置" :label="`${$t('components.table.DefaultConfiguration')}`" name="1">
                <!-- ======================= -->
                <div style="display: flex">
                  <el-form-item label="行背景" :label="`${$t('components.table.RowBackground')}`">
                    <avue-input-color type="textarea" placeholder1="请选择颜色" :placeholder="`${$t('components.table.PleaseSelectColor')}`"
                      v-model="form.rowbackground"></avue-input-color>

                  </el-form-item>
                  <el-form-item label="行字体" :label="`${$t('components.table.RowFont')}`">
                    <avue-input-color type="textarea" placeholder="请选择颜色" :placeholder="`${$t('components.table.PleaseSelectColor')}`" v-model="form.rowfont"></avue-input-color>
                  </el-form-item>
                </div>

                <div style="display: flex">
                  <el-form-item label="单元格背景" :label="`${$t('components.table.CellBackground')}`">
                    <avue-input-color type="textarea" placeholder="请选择颜色" :placeholder="`${$t('components.table.PleaseSelectColor')}`"
                      v-model="form.cellbackground"></avue-input-color>
                  </el-form-item>
                  <el-form-item label="单元格字体" :label="`${$t('components.table.CellFont')}`" width="160">
                    <avue-input-color type="textarea" placeholder="请选择颜色" :placeholder="`${$t('components.table.PleaseSelectColor')}`" v-model="form.cellfont"></avue-input-color>
                  </el-form-item>
                </div>
                <div style="display: flex">
                  <el-form-item label="条件" :label="`${$t('components.table.Condition')}`">
                    <!-- {{ coditionList }} -->
                    <avue-select @change="conditionChange" placeholder1="请选择条件" :placeholder="`${$t('components.table.PleaseSelectCondition')}`" v-model="form.condition" :dic="formCondictionList"></avue-select>
                  </el-form-item>
                  <el-form-item label="值" :label="`${$t('components.table.Value')}`">
                    <avue-input placeholder1="请输入值"  :placeholder="`${$t('components.table.PleaseEnterValue')}`" type="text" v-model="form.value"></avue-input>
                  </el-form-item>
                </div>

                <el-form-item label="格式化" :label="`${$t('components.table.Formatting')}`">
                  <template slot="label">
                    <!-- 格式化 -->
                    {{$t('components.table.Formatting') }}
                    <el-tooltip effect="dark" content='根据不同条件，显示不同的行字体颜色案例 比如在列【化验结果】编辑,必须在数据列表存在font_Color字段，或在数据查询中过滤器添加字段：font_Color：
                                        (data,params,refs)=>{
                                            let newData = data.map(item=>{
                                                item["font_Color"]=""
                                                return item
                                            })
                                            return newData
                                        }
                                        根据不同条件：
                                        (name,data)=>{
                                          if(data["班次"]=="白班"){
                                              data.font_Color="red"
                                          }
                                          if(data["班次"]=="晚班"){
                                              data.font_Color="green" 
                                          }
                                        return data["化验结果"]
                                      }' placement="top">
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </template>
                  <monaco-editor v-model="form.formatter" language="javascript" disabled height="100"></monaco-editor>
                  <el-button size="small" type="primary" icon="el-icon-edit"
                    @click="openCode('formatter', `${$t('components.table.Formatting')}`)">
                    <!-- 编辑 -->
                    {{$t('components.table.Edit') }}
                  </el-button>

                </el-form-item>
                <!-- ======================= -->
              </el-tab-pane>
              <el-tab-pane :key="tabItem.name" v-for="(tabItem, tabIndex) in editableTabs" :label="tabItem.label"
                :name="tabItem.name">

                <tableColumnsTabOptions @openCode="openCode('formatter', `${$t('components.table.FormattingTAB')}`, $event)" :editableTabs="editableTabs"
                  :tabItemChange.sync="editableTabsFormJSON[tabIndex]" :firstTabCondition="form.condition"
                  :tabItem="tabItem" :tabIndex="tabIndex" :dataForm="form"></tableColumnsTabOptions>
              </el-tab-pane>
            </el-tabs>
          </template>
          <template slot="menuLeft" slot-scope="{}">
            <el-button size="small" icon="el-icon-s-operation" @click="openCode('column', `${$t('components.table.TableColumnSettings')}`)"
              type="primary">
              <!-- 编辑数据 -->
              {{$t('components.table.EditData') }}
            </el-button>

            <el-button :loading="isloadColumns" v-if="(!!main.isCustom||!!main.isGlobalDataSource)" size="small" icon="el-icon-s-operation"
              @click="loadModels()" type="primary">
              <!-- 加载模型 -->
              {{$t('components.table.LoadModel') }}
            </el-button>
          </template>
        </avue-crud>
      </el-collapse-item>
    </el-collapse>
    <codeedit @submit="codeClose" :title="code.title" v-model="code.obj" v-if="code.box" :type="code.type"
      :visible.sync="code.box"></codeedit>
  </div>
</template>

<script>
import tableColumnsTabOptions from "./tableColumnsTabOptions.vue";
import MonacoEditor from "@/page/components/editor";
import { tableOption, dicOption } from "@/option/config";
import cloneDeep from 'clone-deep'
import codeedit from "../../page/group/code";
export default {
  name: "table",
  inject: ["main"],
  components: {
    codeedit,
    MonacoEditor,
    tableColumnsTabOptions,
  },
  data() {
    return {
      isloadColumns: false,
      currentTabName: '1',
      editableTabsFormJSON: [],
      editableTabs: [],
      tabIndex: 1,
      coditionList: Object.assign([], dicOption.codition),
      formatterForm_activeName: '1',
      form: {},
      dicOption: dicOption,
      tableOption: tableOption,
      code: {
        isMain: true,
        box: false,
        type: "",
        obj: {},
      },
    };
  },
  computed: {
      formCondictionList(){
        // 返回所有可用条件，移除互斥限制
        return this.getAvailableConditions()
      }
  },
  watch: {
    formCondictionList:{
      handler(val) {
        if(val && val.length>0){
          console.log("========formCondictionList change...======")
        }
        
      },
      deep: true
    },
    form: {
      handler(val) {
        console.log("form value Change:", val)
        this.initTabsEdit()
        this.initCondictionList()
        // 移除条件重置逻辑，允许保持已选择的条件
      },
      deep: true
    }
  },
  mounted() {
     // 格式化【avue-crud】多语言
    this.format_tableOption()
  },
  methods: {
    // 格式化【avue-crud】多语言
    format_tableOption(){
      if(this.tableOption && this.tableOption.column && this.tableOption.column.length>0){
        this.tableOption.column.forEach(item=>{
          if(item.label){
            item.label = this.$t(item.label)
          }
        })
      }
    },
    conditionChange(params) {
      // 移除条件互斥限制，允许重复选择相同条件类型
      console.log('条件选择变更:', params)
      // 不再需要全局条件状态管理，每个条件独立配置
    },
    initCondictionList() {
      // 移除条件互斥限制，不再需要全局条件状态管理
      // 每个条件配置独立，允许重复选择相同条件类型
      console.log('初始化条件列表 - 已移除互斥限制')

      // 设置默认的可用条件列表（所有条件都可用）
      let _defaultCondictionList = this.getAvailableConditions()
      this.$store.commit("set_globalConditionList", _defaultCondictionList);
    },

    // 获取可用条件列表（移除互斥限制）
    getAvailableConditions() {
      // 返回所有可用条件，不再禁用已选择的条件
      return cloneDeep(dicOption.codition).map(item => ({
        ...item,
        disabled: false, // 移除禁用状态
        tabIndex: -1     // 移除标签索引
      }))
    },

    // 已废弃：原条件互斥检查方法（保留兼容性）
    coditionListFn(excludeTabIndex) {
      console.log('条件互斥检查已废弃，现在允许重复选择相同条件类型')
      return this.getAvailableConditions()
    },
    // 加载模型  by andy 20241101
    loadModels() {
      this.isloadColumns = true
      let newColumns = []
      let oldColumns = cloneDeep(this.main.activeOption.column)
      try {
        let models = this.main.DIC.model
        // label: "CPROCESS_NAME"
        // value: "CPROCESS_NAME"
        if (models && models.length > 0) {
          models.forEach((item, index) => {
            let newColumn = {
              label: item.label,
              prop: item.value,
              $index: index,
            }
            let oldColumItem = oldColumns.find((oldItem) => {
              return oldItem.prop === item.value
            })
            if (oldColumItem) {
              newColumn = oldColumItem
            }
            newColumns.push(newColumn)
          })
        }
        //debugger
        this.$set(this.main.activeOption, 'column', newColumns)
        //debugger 
      } catch (error) {

      }
      setTimeout(() => {
        this.isloadColumns = false
      }, 1000)


    },
    handleClick(params) {
      this.currentTabName = params.name
    },
    // 初始化 TAB 数据
    initTabsEdit() {
      //debugger editableTabsFormJSON
      this.formatterForm_activeName = '1'
      this.editableTabs = []
      try {
        if (this.form.editableTabsFormJSON && this.form.editableTabsFormJSON.length > 0) {
          this.editableTabsFormJSON = cloneDeep(this.form.editableTabsFormJSON)
          this.form.editableTabsFormJSON.forEach((item, index) => {
            let newTabName = index + 2 + '';
            this.editableTabs.push({
              label: "配置" + newTabName,
              name: newTabName,
            })
          })
        }
      } catch (error) {

      }
    },
    handleTabsEdit(targetName, action) {
      this.tabIndex = this.editableTabs.length + 1
      //debugger
      if (targetName === '1') {
        return
      }
      if (action === 'add') {
        let newTabName = ++this.tabIndex + '';
        this.editableTabs.push({
          label: "配置" + newTabName,
          name: newTabName,
        });
        this.formatterForm_activeName = newTabName;
      }
      if (action === 'remove') {
        //debugger
        let lastTabIndex = this.editableTabs.length + 1 + ''
        if (lastTabIndex != targetName) {
          this.$message.error(`${this.$t('message.PleaseDeleteTheLastTabFirst')}`)//"请先删除最后一个选项卡"
          return
        }
        this.tabIndex = this.tabIndex - 1
        let tabs = this.editableTabs;
        let activeName = this.formatterForm_activeName;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.name;
              } else {
                activeName = "1"
              }
            }
          });
        }
        if (this.editableTabsFormJSON && this.editableTabsFormJSON.length > 0) {
          // 移除指定数据
          let removeFlag = this.editableTabsFormJSON.splice(Number(targetName - 2), 1)
        }

        this.formatterForm_activeName = activeName;
        this.editableTabs = tabs.filter(tab => tab.name !== targetName);
      }
    },
    rowSave(row, done) {
      //debugger
      if (this.editableTabsFormJSON) {
        row.editableTabsFormJSON = this.editableTabsFormJSON
      }
      this.main.activeOption.column.push(this.deepClone(row));
      done();
    },
    rowDel(row, index) {
      this.main.activeOption.column.splice(index, 1);
    },
    rowUpdate(row, index, done) {
      //debugger
      if (this.editableTabsFormJSON) {
        row.editableTabsFormJSON = this.editableTabsFormJSON
      }
      this.main.activeOption.column.splice(index, 1, this.deepClone(row));
      //console.log(this.main.activeOption.column,row);
      done();
    },
    codeClose(value) {
      //debugger
      if (this.code.type == "formatter") {
        if (!this.code.isMain) {
          let _index = Number(this.currentTabName) - 2
          this.editableTabsFormJSON[_index].formatter = value;
        } else {
          this.form.formatter = value;
        }
      } else {
        if (Object.prototype.toString.call(value) === '[object String]') {
          this.main.activeOption.column = JSON.parse(value)
        } else {
          this.main.activeOption.column = value;
        }

      }
      this.code.isMain = true
    },
    openCode(type, title, e = null) {
      //debugger
      this.tabIndex = this.editableTabs.length + 1
      this.code.title = title;
      this.code.type = type;
      if (type == "formatter") {
        if (e && e.formatter) {
          this.code.isMain = false
          this.code.obj = e.formatter
        } else {
          this.code.obj = this.form.formatter;
        }
      } else {
        this.code.obj = this.main.activeOption.column;
      }
      this.code.box = true;
    },

  },
};
</script>

<style></style>
