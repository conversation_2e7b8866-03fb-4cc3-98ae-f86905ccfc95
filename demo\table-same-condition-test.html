<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件 - 相同条件类型测试</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .title {
            color: #fff;
            text-align: center;
            font-size: 32px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .subtitle {
            color: rgba(255,255,255,0.9);
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section-title {
            color: #fff;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .section-title::before {
            content: '';
            width: 5px;
            height: 25px;
            background: #667eea;
            border-radius: 3px;
        }
        .feature-highlight {
            background: rgba(102,126,234,0.2);
            border: 1px solid rgba(102,126,234,0.5);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #fff;
        }
        .feature-highlight h4 {
            color: #667eea;
            margin: 0 0 10px 0;
        }
        .test-data {
            background: rgba(0,0,0,0.5);
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            color: #ccc;
            font-size: 14px;
        }
        .test-data h4 {
            color: #667eea;
            margin: 0 0 15px 0;
        }
        .condition-item {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            padding: 10px;
            margin: 8px 0;
            border-left: 4px solid #667eea;
        }
        .condition-item.red {
            border-left-color: #e74c3c;
        }
        .condition-item.green {
            border-left-color: #27ae60;
        }
        .condition-item.orange {
            border-left-color: #f39c12;
        }
        .controls {
            text-align: center;
            margin: 25px 0;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: #fff;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        .btn-success:hover {
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }
        .table-wrapper {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
        }
        .test-result {
            background: rgba(0,0,0,0.6);
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 13px;
            color: #ccc;
            max-height: 300px;
            overflow-y: auto;
        }
        .highlight {
            color: #667eea;
            font-weight: bold;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .data-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        .data-value {
            font-weight: bold;
            color: #667eea;
        }
        .expected-style {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: #fff;
        }
        .style-red { background: #e74c3c; }
        .style-green { background: #27ae60; }
        .style-orange { background: #f39c12; }
        .style-default { background: #95a5a6; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎯 相同条件类型测试</h1>
        <p class="subtitle">验证可以配置多个相同类型的条件：条件1（大于0红色）+ 条件2（大于5绿色）</p>
        
        <div class="controls">
            <button class="btn" onclick="runSameConditionTest()">🧪 运行测试</button>
            <button class="btn btn-success" onclick="showConfiguration()">⚙️ 查看配置</button>
            <button class="btn" onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <!-- 新功能说明 -->
        <div class="test-section">
            <div class="section-title">🆕 功能改进</div>
            <div class="feature-highlight">
                <h4>移除条件互斥限制：</h4>
                <p>✅ 现在可以配置多个相同类型的条件（如多个"大于"条件）</p>
                <p>✅ 条件按配置顺序执行，后面的条件优先级更高</p>
                <p>✅ 支持更灵活的条件组合和样式覆盖</p>
            </div>
        </div>

        <!-- 测试场景 -->
        <div class="test-section">
            <div class="section-title">🎯 测试场景</div>
            <div class="test-data">
                <h4>条件配置：</h4>
                <div class="condition-item red">
                    <strong>条件1：</strong> 数值 > 0 → 红色背景 (#e74c3c)
                </div>
                <div class="condition-item green">
                    <strong>条件2：</strong> 数值 > 5 → 绿色背景 (#27ae60) - 优先级更高
                </div>
                <div class="condition-item orange">
                    <strong>条件3：</strong> 数值 > 10 → 橙色背景 (#f39c12) - 优先级最高
                </div>
                
                <h4>测试数据与预期结果：</h4>
                <div class="data-row">
                    <span>数值: -1</span>
                    <span class="expected-style style-default">默认样式</span>
                </div>
                <div class="data-row">
                    <span>数值: 3</span>
                    <span class="expected-style style-red">红色背景</span>
                </div>
                <div class="data-row">
                    <span>数值: 8</span>
                    <span class="expected-style style-green">绿色背景</span>
                </div>
                <div class="data-row">
                    <span>数值: 15</span>
                    <span class="expected-style style-orange">橙色背景</span>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-section">
            <div class="section-title">📊 测试结果</div>
            <div class="table-wrapper" id="testResults">
                <div style="color: #667eea; text-align: center; padding: 40px;">
                    点击"运行测试"开始验证相同条件类型功能
                </div>
            </div>
        </div>

        <!-- 配置详情 -->
        <div class="test-section">
            <div class="section-title">⚙️ 配置详情</div>
            <div class="test-data" id="configDetails">
                <h4>表格列配置：</h4>
                <p>点击"查看配置"按钮显示详细配置信息</p>
            </div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 测试数据
        const testData = [
            { id: 1, name: '测试1', value: -1, description: '负数，不满足任何条件' },
            { id: 2, name: '测试2', value: 0, description: '零值，边界情况' },
            { id: 3, name: '测试3', value: 3, description: '满足条件1（>0）' },
            { id: 4, name: '测试4', value: 5, description: '边界值，等于5' },
            { id: 5, name: '测试5', value: 8, description: '满足条件1和条件2（>5）' },
            { id: 6, name: '测试6', value: 10, description: '边界值，等于10' },
            { id: 7, name: '测试7', value: 15, description: '满足所有条件（>10）' },
            { id: 8, name: '测试8', value: 100, description: '大数值，满足所有条件' }
        ];

        // 表格配置 - 支持多个相同类型条件
        const tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 8,
            scroll: false,
            headerBackground: "#2c3e50",
            headerColor: "#ecf0f1",
            headerFontSize: 16,
            headerTextAlign: "center",
            bodyColor: "#2c3e50",
            bodyFontSize: 14,
            bodyTextAlign: "center",
            nthColor: "rgba(236, 240, 241, 0.1)",
            othColor: "rgba(189, 195, 199, 0.1)",
            column: [
                { 
                    label: "名称", 
                    prop: "name", 
                    width: 100
                },
                { 
                    label: "数值", 
                    prop: "value", 
                    width: 100,
                    // 主条件：大于0显示红色
                    condition: 1, // 大于
                    value: 0,
                    cellbackground: "#e74c3c",
                    cellfont: "#ffffff",
                    // 扩展条件：支持多个相同类型的条件
                    editableTabsFormJSON: [
                        {
                            condition: 1, // 大于（相同类型）
                            value: 5,
                            cellbackground: "#27ae60",
                            cellfont: "#ffffff"
                        },
                        {
                            condition: 1, // 大于（相同类型）
                            value: 10,
                            cellbackground: "#f39c12",
                            cellfont: "#ffffff"
                        }
                    ]
                },
                { 
                    label: "说明", 
                    prop: "description", 
                    width: 250
                }
            ]
        };

        // 运行相同条件类型测试
        function runSameConditionTest() {
            console.log('🧪 开始运行相同条件类型测试...');
            
            let html = '<div style="color: #fff;">';
            html += '<h3 style="color: #667eea; margin-bottom: 20px;">🎯 相同条件类型测试结果</h3>';
            
            // 模拟条件处理逻辑
            testData.forEach((row, index) => {
                const result = simulateMultipleSameConditions(row);
                
                html += `<div class="test-result">`;
                html += `<div class="highlight">测试 ${index + 1}: ${row.name} (值: ${row.value})</div>`;
                html += `<div>说明: ${row.description}</div>`;
                html += `<div>条件1 (>0): ${result.condition1 ? '<span class="success">✅ 满足</span>' : '<span class="warning">❌ 不满足</span>'}</div>`;
                html += `<div>条件2 (>5): ${result.condition2 ? '<span class="success">✅ 满足</span>' : '<span class="warning">❌ 不满足</span>'}</div>`;
                html += `<div>条件3 (>10): ${result.condition3 ? '<span class="success">✅ 满足</span>' : '<span class="warning">❌ 不满足</span>'}</div>`;
                html += `<div>最终样式: <span class="highlight">${result.finalStyle}</span></div>`;
                html += `<div>优先级说明: ${result.explanation}</div>`;
                html += `</div>`;
            });
            
            html += '</div>';
            document.getElementById('testResults').innerHTML = html;
            
            console.log('✅ 相同条件类型测试完成');
        }

        // 模拟多个相同条件的处理逻辑
        function simulateMultipleSameConditions(row) {
            const value = row.value;
            const condition1 = value > 0;   // 主条件
            const condition2 = value > 5;   // 扩展条件1
            const condition3 = value > 10;  // 扩展条件2
            
            let finalStyle = '默认样式';
            let explanation = '无条件满足';
            
            // 按新的逻辑处理：所有条件都检查，后面的优先级更高
            if (condition1) {
                finalStyle = '红色背景（条件1）';
                explanation = '满足条件1：大于0';
            }
            
            if (condition2) {
                finalStyle = '绿色背景（条件2）';
                explanation = '满足条件2：大于5，覆盖条件1';
            }
            
            if (condition3) {
                finalStyle = '橙色背景（条件3）';
                explanation = '满足条件3：大于10，覆盖前面所有条件';
            }
            
            return {
                condition1,
                condition2,
                condition3,
                finalStyle,
                explanation
            };
        }

        // 显示配置详情
        function showConfiguration() {
            console.log('⚙️ 表格配置:', tableConfig);
            console.log('📊 测试数据:', testData);
            
            let html = '<h4>表格列配置：</h4>';
            html += '<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 5px; color: #ccc; font-size: 12px; overflow-x: auto;">';
            html += JSON.stringify(tableConfig.column[1], null, 2); // 显示数值列的配置
            html += '</pre>';
            
            document.getElementById('configDetails').innerHTML = html;
            
            alert('配置信息已输出到控制台和页面，请查看详细信息');
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = 
                '<div style="color: #667eea; text-align: center; padding: 40px;">测试结果已清空</div>';
            document.getElementById('configDetails').innerHTML = 
                '<h4>表格列配置：</h4><p>点击"查看配置"按钮显示详细配置信息</p>';
            console.clear();
            console.log('🎯 相同条件类型测试');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 相同条件类型测试页面已加载');
            console.log('🎯 测试目标：验证可以配置多个相同类型的条件');
            console.log('📊 测试数据已准备就绪，共', testData.length, '条记录');
        });
    </script>
</body>
</html>
