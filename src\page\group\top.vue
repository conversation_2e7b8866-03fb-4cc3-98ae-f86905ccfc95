<template>
  <div class="top-nav">
    <!-- x:{{ trackObj.x }} /oy:{{ trackObj.oy }} -->
     <!-- /Y:{{ trackObj.y }}/oy:{{ trackObj.oy }} ox:{{ trackObj.ox }} -->
    <div class="top_search">
      <el-input
        style="height: 40px; border: 0"
        placeholder1="搜索组件"
        :placeholder="`${$t('page.top.search')}`"
        @input="onchange"
        v-model="serachValue"
      >
        <el-button
          style="border: 0; color: #fff"
          slot="append"
          icon="el-icon-search"
        ></el-button
      ></el-input>
     
    </div>
    <el-collapse v-model="activeName">
      <el-collapse-item
        v-for="(item, index) in baseList"
        :key="index"
        title1="item.label"
        :title="`${$t(item.label)}`"
        :name="index"
      >
        <div class="content_data" @mousedown="contain.handleInitActive">
          <div
            v-for="(citem, cindex) in item.children"
            @click="handleAdd(citem.option, true)"
            :key="cindex"
            class="menu-inline"
            :index="`${index}-${cindex}`"
          >
          <draggable ghost-class="ghost"
             class="menu_ul"
             :group="{ name: 'layer' }"
             :list="item.children"
             @end="onEnd_draggable($event,citem.option,cindex)"
             :animation="300">
             <!-- @dragend="onEnd_draggable($event,citem.option,cindex)" -->
                <div 
                    :draggable="true" class="usehove">
                  <img :src="url+citem.option.img" class="inside-img" />
                  <div class="bottom-text">{{ $t(citem.label) }}</div>
                </div>
          </draggable>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <!-- <el-menu class="nav"
             mode="horizontal"
             background-color="#212528"
             text-color="#fff"
             active-text-color="#409EFF"
             @mousedown="contain.handleInitActive">
      <el-submenu :index="index+''"
                  v-for="(item,index) in baseList"
                  :key="index">
        <template slot="title">
          <el-tooltip effect="dark"
                      :content="item.label"
                      placement="top">
            <i :class="'nav__icon iconfont '+item.icon"></i>
          </el-tooltip>
        </template>
        <div style="width:390px">
          <el-menu-item v-for="(citem,cindex) in item.children"
                        @click="handleAdd(citem.option,true)"
                        :key="cindex"
                        class="menu-inline"
                        :index="`${index}-${cindex}`">
            <div class="usehove">
              <img :src="citem.option.img"
                   class="inside-img">
              <div class="bottom-text">{{citem.label}}</div>
            </div>
          </el-menu-item>
        </div>
      </el-submenu>
    </el-menu> -->
  </div>
</template>

<script>
// import { setLocalStorageStore, getLocalStorageStore,getCookie } from '@/utils/setStore'
import vuedraggable from 'vuedraggable';
import { uuid } from "@/utils/utils";
import { website } from "@/config.js";
// import  menuLocalesObj  from "@/locales/menu.js";
export default {
  inject: ["contain"],
  components: {
    draggable: vuedraggable
  },
  provide() {
    return {
      contain: this.contain,
     
     
    };
  },
  data() {
    return {
      trackObj:{x:0,y:0,ox:0,oy:0},
      serachValue: "",
      url:'',
      activeName: "",
      baseList: website.baseList,
    };
  },
  methods: {
    // getStatusTranslation(text){
    //   let language= getLocalStorageStore('language')
    //   let defaultText = text;
    //   if(language == 'en'){
    //     for (const [key,value] of Object.entries(menuLocalesObj.en)) {
    //         if(key == text){
    //           return value
    //         }
    //       }
    //   }
    //   return defaultText;
    // },
    onEnd_draggable($event, option, _index){
     // debugger
      if($event.originalEvent && $event.originalEvent.layerX>200){
          let parentRect = document.getElementById('container').getBoundingClientRect();
          let x = $event.originalEvent.clientX //- parentRect.left //- option.component.width;
          let y = $event.originalEvent.clientY + $event.originalEvent.clientY/3 // - option.component.height;
          this.trackObj.ox = $event.originalEvent.clientX
          this.trackObj.oy = $event.originalEvent.clientY
          let obj = this.deepClone(option);
          let index = uuid();
          this.trackObj.x= parentRect.left
          this.trackObj.y= parentRect.top
          obj.left = x //$event.originalEvent.layerX -$event.originalEvent.currentTarget.scrollWidth//-option.component.width/2+120;
          obj.top = y //$event.originalEvent.screenY +$event.originalEvent.currentTarget.scrollHeight //+option.component.height/2+100;
          obj.index = index;
          this.contain.nav.unshift(obj);
          setTimeout(() => this.contain.selectNav(index));
       }
     
    },
    onchange() {
      if (this.serachValue == "") {
        this.baseList = website.baseList;
      } else {
        var arr = [];
        this.baseList.forEach((ele) => {
          var brr = ele.children.filter(
            (item) => item.label.indexOf(this.serachValue) > -1
          );
          if (brr && brr.length > 0) {
            arr.push(ele);
          }
        });
        this.baseList = arr;
        this.activeName = 0;
      }
    },
    handleAdd(option, first = false) {
      let obj = this.deepClone(option);
      let index = uuid();
      obj.left = 0;
      obj.top = 0;
      obj.index = index;
      if (first) {
        this.contain.nav.unshift(obj);
      } else {
        this.contain.nav.push(obj);
      }
      setTimeout(() => this.contain.selectNav(index));
    },
  },
  mounted() {
    if (process.env.NODE_ENV === "production") {
      this.url = window.location.origin + "/subapp/bulletin";
    }
  },
};
</script>

<style lang="scss">
.top-nav {
  text-align: center;
  margin-top: -5px;
  .content_data {
    display: flex;
    flex-wrap: wrap;
  }
  .top_search {
    input::-webkit-input-placeholder {
      color: #999;
    }
  }
  .menu-inline {
    height: 85px;
    text-align: center;
    width: 48.5%;
    border: 1px solid #333;
  }
  .usehove {
  }
  .bottom-text {
    color: #fff;
  }
  .inside-img {
    width: 67px;
    height: 46px;

    box-sizing: border-box;
    margin-top: 8px;
  }

  .usehove:hover {
    opacity: 0.9;
    cursor: pointer;
  }
}
</style>
