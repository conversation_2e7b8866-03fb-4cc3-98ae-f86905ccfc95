import { urls } from '@/config';
import crypto from '@/utils/crypto'
import request from '../axios';

export const getList = (params) => {
  return request({
    url: urls + '/db/list',
    method: 'get',
    params: params
  })
}

export const getDetail = (id) => {
  return request({
    url: urls + '/db/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: urls + '/db/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: urls + '/db/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: urls + '/db/submit',
    method: 'post',
    data: row
  })
}
export const dynamicSql = (data) => {
  return request({
    url: urls + '/db/dynamic-query',
    method: 'post',
    headers: {
      'data': data,
      'Content-Type': 'application/json'
    },
    data: data
  })
}
export const dbTest = (data) => {
  return request({
    url: urls + '/db/db-test',
    method: 'post',
    headers: {
      'data': crypto.encrypt(JSON.stringify(data)),
      'Content-Type': 'application/json'
    },
    data: crypto.encrypt(JSON.stringify(data))
  })
}