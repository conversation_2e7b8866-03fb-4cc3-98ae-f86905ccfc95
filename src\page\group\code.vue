<template>
  <el-dialog :visible.sync="visible"
             :close-on-click-modal="false"
             :before-close="handleClose"
             :title="title || '数据处理'"
             width="80%">

    <div class="content">
      <monaco-editor ref="codeEditor"
                     v-model="code"
                     :options="editorOptions"></monaco-editor>
      <monaco-editor v-model="tip"
                     disabled></monaco-editor>
    </div>
    <span slot="footer"
          class="dialog-footer">
      <div class="footer-left">
        <el-button size="small"
                   type="success"
                   icon="el-icon-magic-stick"
                   @click="formatCode">
                   格式化代码
        </el-button>
        <el-button size="small"
                   type="warning"
                   icon="el-icon-view"
                   @click="validateCode">
                   验证语法
        </el-button>
      </div>
      <div class="footer-right">
        <el-button size="small"
                   @click="setVisible(false)">
                   <!-- 取 消 -->
                  {{$t('other.CancelBtn')}}
                  </el-button>
        <el-button type="primary"
                   @click="submit"
                   size="small">
                   <!-- 确 定 -->
                   {{$t('other.ConfirmBtn')}}
                  </el-button>
      </div>
    </span>
  </el-dialog>
</template>

<script>
import { tip } from '@/config'
import { funEval } from '@/utils/utils'
import MonacoEditor from '@/page/components/editor'
export default {
  components: { MonacoEditor },
  data () {
    return {
      code: '',
      tip: '',
      editorOptions: {
        formatOnPaste: true,
        formatOnType: true,
        automaticLayout: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        wordWrap: 'on',
        fontSize: 14,
        tabSize: 4,
        insertSpaces: true
      }
    }
  },
  props: {
    rules: {
      type: Boolean,
      default: true
    },
    title: String,
    visible: Boolean,
    type: String,
    value: [String, Object, Array]
  },
  watch: {
    value: {
      handler (val) {
        if (this.validatenull(val)) {
          if (['dataFormatter', 'stylesFormatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(data,params,refs)=>{
    return data
}`
          } else if (['query', 'header', 'dataQuery', 'dataHeader'].includes(this.type) && this.validatenull(val)) {
            this.code = `(data)=>{
    return data
}`
          } else if (['echartFormatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(data)=>{
    return data
}`
          } else if (['clickFormatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(params,refs)=>{
    console.log(params,refs)
}`
          } else if (['labelFormatter', 'formatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(name,data)=>{
    console.log(name,data)
    return ''
}`
          }
        } else {
          this.code = val;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created () {
    this.tip = tip
  },
  methods: {
    // 格式化代码
    async formatCode() {
      try {
        // 检查代码是否为空
        if (!this.code || this.code.trim() === '') {
          this.$message.warning('代码内容为空，无法格式化');
          return;
        }

        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在格式化代码...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 使用Monaco编辑器的格式化功能
          if (this.$refs.codeEditor && this.$refs.codeEditor.formatCode) {
            await this.$refs.codeEditor.formatCode();
            this.$message.success('代码格式化成功');
          } else {
            // 回退到自定义格式化
            const formattedCode = this.formatJavaScript(this.code);
            this.code = formattedCode;
            this.$message.success('代码格式化成功（使用自定义格式化）');
          }
        } finally {
          loading.close();
        }
      } catch (error) {
        console.error('格式化失败:', error);
        this.$message.error(`代码格式化失败: ${error.message}`);
      }
    },

    // 验证代码语法
    async validateCode() {
      try {
        if (!this.code || this.code.trim() === '') {
          this.$message.warning('代码内容为空，无法验证');
          return;
        }

        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在验证语法...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 首先使用Monaco编辑器的语法检查
          if (this.$refs.codeEditor && this.$refs.codeEditor.validateSyntax) {
            await this.$refs.codeEditor.validateSyntax();
          }

          // 然后使用funEval进行运行时验证
          funEval(this.code);
          this.$message.success('代码语法验证通过');
        } finally {
          loading.close();
        }
      } catch (error) {
        console.error('语法验证失败:', error);
        this.$message.error(`语法错误: ${error.message}`);
      }
    },

    // 重新设计的JavaScript代码格式化方法 - 彻底解决箭头函数问题
    formatJavaScript(code) {
      if (!code || code.trim() === '') {
        return code;
      }

      try {
        // 1. 保护字符串内容
        const stringPlaceholders = [];
        let stringIndex = 0;
        let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
          const placeholder = `__STRING_${stringIndex++}__`;
          stringPlaceholders.push({ placeholder, content: match });
          return placeholder;
        });

        // 2. 保护箭头函数 - 使用特殊占位符
        const arrowPlaceholders = [];
        let arrowIndex = 0;
        formatted = formatted.replace(/\s*=>\s*/g, () => {
          const placeholder = `__ARROW_${arrowIndex++}__`;
          arrowPlaceholders.push({ placeholder, content: ' => ' });
          return placeholder;
        });

        // 3. 基本的空白处理
        formatted = formatted.replace(/\s+/g, ' ').trim();

        // 4. 处理操作符（现在不会影响箭头函数）
        formatted = formatted
          // 算术操作符
          .replace(/\s*([+\-*/%])\s*/g, ' $1 ')
          // 比较操作符
          .replace(/\s*(==|!=|===|!==|<=|>=|<|>)\s*/g, ' $1 ')
          // 逻辑操作符
          .replace(/\s*(&&|\|\|)\s*/g, ' $1 ')
          // 赋值操作符（现在安全了，因为箭头函数已被保护）
          .replace(/\s*([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))\s*=\s*([^=])/g, '$1 = $2');

        // 5. 处理标点符号
        formatted = formatted
          // 逗号后加空格
          .replace(/,(?!\s)/g, ', ')
          // 分号后换行
          .replace(/;(?!\s*\n)/g, ';\n')
          // 大括号处理
          .replace(/\{(?!\s*\n)/g, ' {\n')
          .replace(/\}(?!\s*\n)/g, '\n}')
          // 小括号处理
          .replace(/\s*\(\s*/g, '(')
          .replace(/\s*\)\s*/g, ')');

        // 6. 最终清理（避免破坏已保护的内容）
        formatted = formatted
          .replace(/\s*{\s*/g, ' {\n')
          .replace(/\s*}\s*/g, '\n}')
          .replace(/\s*;\s*/g, ';\n')
          .replace(/\s*,\s*/g, ', ');

        // 7. 恢复箭头函数
        arrowPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 8. 恢复字符串内容
        stringPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 添加适当的缩进
        const lines = formatted.split('\n');
        let indentLevel = 0;
        const indentSize = 4; // 4个空格缩进

        const formattedLines = lines.map(line => {
          line = line.trim();
          if (line === '') return '';

          // 减少缩进（在处理行之前）
          if (line.includes('}')) {
            indentLevel = Math.max(0, indentLevel - 1);
          }

          const indentedLine = ' '.repeat(indentLevel * indentSize) + line;

          // 增加缩进（在处理行之后）
          if (line.includes('{')) {
            indentLevel++;
          }

          return indentedLine;
        });

        return formattedLines.join('\n');
      } catch (error) {
        console.error('格式化过程中出错:', error);
        return code; // 如果格式化失败，返回原始代码
      }
    },

    // handleOpen () {
    //   this.form = this.value
    //   this.box = true;
    //   this.$nextTick(() => {
    //     this.handleOption()
    //   })
    // },
    handleClose () {
      this.setVisible(false);
    },
    submit () {
      let value = this.code;
      if (!this.rules) {
        this.$emit('submit', value);
        this.setVisible(false)
        return
      }
      try {
        funEval(value);
        if (['data'].includes(this.type)) value = funEval(value);
        this.$emit('submit', value);
        this.setVisible(false)
      } catch (error) {
        console.log(error);
        this.$message.error(`${this.$t('message.DataFormatIsIncorrect')}`)//'数据格式有误'
      }

    },
    setVisible (val) {
      this.$emit('update:visible', val);
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  .monaco_editor_container {
    flex: 1;
    &:first-child {
      flex: 2;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-left {
    display: flex;
    gap: 10px;
  }

  .footer-right {
    display: flex;
    gap: 10px;
  }
}

// 格式化按钮样式
::v-deep .el-button--success {
  background-color: #67c23a;
  border-color: #67c23a;

  &:hover {
    background-color: #85ce61;
    border-color: #85ce61;
  }
}

::v-deep .el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;

  &:hover {
    background-color: #ebb563;
    border-color: #ebb563;
  }
}
</style>
