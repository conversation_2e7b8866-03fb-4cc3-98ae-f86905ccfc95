<template>
  <div :class="[b(),className]"
       :style="styleSizeName"
       ref="main"
       @click="handleClick">
        <!-- <span style="color: white;"> andy videoUrlFormat:>>>>> {{ videoUrlFormat }}/////dataChart:{{ dataChart }}</span>
   -->
    <video :style="styleChartName"
           muted
           :width="width"
           :height="height"
           :src="dataChart.value"
           v-bind="params"
           :poster="poster"
           style="object-fit: fill">
    </video>
    <img :src="option.poster"
         v-if="option.poster"
         alt=""
         :style="styleSizeName"
         :class="b('img')">
  </div>
</template>

<script>
import create from "../../create";
export default create({
  name: "video",
  data () {
    return {

    };
  },
  computed: {
    videoUrlFormat () {
      let _optionData = (Array.isArray(this.dataChart)?this.dataChart[0]:this.dataChart)
      return _optionData
    },
    poster () {
      return this.option.poster ? '-' : ''
    },
    params () {
      let result = {}
      if (this.option.controls) result.controls = "controls"
      if (this.option.loop) result.loop = "loop"
      if (this.option.autoplay) result.autoplay = "autoplay"
      return result
    }
  },
  created () { },
  mounted () { },
  methods: {
    handleClick () {
      this.clickFormatter && this.clickFormatter({
        type: index,
        item: item,
        value: item.value,
        data: this.dataChart
      }, this.getItemRefs());
    }
  },
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      }
    }
  }
});
</script>


