<template>
    <div class="mapManagementModel">
        <vxe-modal v-model="showEdit" :title="title" width="900" @close="close" resize destroy-on-close>
            <template #default>
                <el-button type="primary" style="margin-bottom: 10px;" size="small" @click="addmore()">
                    <!-- 添加更多地图 -->
                    {{ $t('page.mapManagement.AddMoreMaps') }}
                </el-button>
                
                <el-form size="small" class="el_form" :rules="formRule" ref="form" :model="formData" label-width="80px">
                    <el-form-item label="地图名字" :label="`${$t('page.mapManagement.MapName')}`" prop="CNAME">
                        <el-input v-model="formData.CNAME" placeholder="请输入地图名字" :placeholder="`${$t('page.mapManagement.PleaseEnterMapName')}`"></el-input>
                    </el-form-item>
                    <el-form-item label="地图数据" :label="`${$t('page.mapManagement.MapData')}`" prop="CDATA">
                        <el-input type="textarea" :rows="24" v-model="formData.CDATA" placeholder="请输入地图数据" :placeholder="`${$t('page.mapManagement.PleaseEnterMapData')}`"></el-input>
                    </el-form-item>

                    <el-form-item>
                        <div style="text-align: right;"> <el-button @click="close()">
                            <!-- 取消 -->
                            {{ $t('page.mapManagement.Cancel') }}
                        </el-button>
                            <el-button type="primary"  @click="submitEvent('form')">
                                <!-- 确定 -->
                                {{ $t('page.mapManagement.Confirm') }}
                            </el-button>
                        </div>

                    </el-form-item>
                </el-form>







            </template>
        </vxe-modal>
    </div>
</template>
  
<script>
import {
 

  Update,
 
  Add,
} from "./Api.js";
export default {
    name: "Modal",

    props: ["modelEvent", "title", "getList"],
    data() {
        return {

            showEdit: false,
            Blean: '',
            formData: {
                CNAME: "",
                CDATA:'',
            },
            formRule: {
                CNAME: [
                    { required: true, message: "请输入地图名字", trigger: "blur" },
                ],
            }

        };
    },
    mounted() {
      this.formRule= {
                CNAME: [
                    { required: true, message: this.$t('rules.PleaseEnterMapName'), trigger: "blur" },
                ],
            }
    },
    methods: {
        addmore(){
            window.open("https://datav.aliyun.com/portal/school/atlas/area_selector#&lat=33.521903996156105&lng=104.29849999999999&zoom=4",'_blank');        
          
        },
        //新建
        addshowEdit(value) {
            this.formData= {
                CNAME: "",
                CDATA:'',
            }
            this.Blean = value
            this.showEdit = true;
        },

        //编辑
        appendshowEdit(value, row) {
            this.formData=JSON.parse(JSON.stringify(row))
            this.Blean = value
            this.showEdit = true;
        },




     
        close() {
         
          this.showEdit = false
        },



        submitEvent(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                  
                 
                    if (this.Blean == true) {
                        Update(this.formData).then((res) => {
                            res = res.data;
                            if (res.code === 200 && res.data.Success) {
                                this.$message({
                                    message:`${this.$t('message.SavedSuccessfully')}`,//'保存成功'
                                    type: "success",
                                });
                                this.close();
                                this.getList();
                            } else {
                                this.$message({
                                    message: res.data.Content,
                                    type: "error",
                                });
                            }
                        });
                    } else {
                        Add(this.formData).then((res) => {
                            res = res.data;
                            if (res.code === 200 && res.data.Success) {
                                this.$message({
                                    message:`${this.$t('message.SavedSuccessfully')}`,//'保存成功'
                                    type: "success",
                                });
                                this.close();
                                this.getList();
                            } else {
                                this.$message({
                                    message: res.data.Content,
                                    type: "error",
                                });
                            }
                        });
                    }

                } else {
                  //  console.log("error submit!!");
                    return false;
                }
            });
        },

    },
};
</script>
  
  <!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.mapManagementModel {
    .el-form-item__error {
        top: 67%;
    }

    .el_form {

        padding: 0 10px;
    }
}
</style>
  