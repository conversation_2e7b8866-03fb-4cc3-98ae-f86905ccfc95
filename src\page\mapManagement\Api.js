import { url ,urls} from '@/config';
import { config } from '@/option/config'
import request from '../../axios'



//获取看地图板列表
export const GetPageListByQueryJson = (params) => request({
    url: url + 'api/kanban/kanbanMap/GetAll',
    method: 'get',
    params:params
  });

//添加看板地图
  export const Add = (data) => request({
    url: url + 'api/kanban/kanbanMap/Add',
    method: 'post',
    data:data
  });
  
//修改看板地图
export const Update = (data) => request({
    url: url + 'api/kanban/kanbanMap/Update',
    method: 'post',
    data:data
  });
  //删除看板地图
  export const Delete = (data) => request({
    url: url + 'api/kanban/kanbanMap/Delete',
    method: 'post',
    data:data
  });
  

  export const Import1 = (data) => {
    return request({
      url:url + 'api/kanban/KanbanMap/Import',
      method: 'post',
      data
    })
  }
  export const Export1 = (data) => {
    return request({
      url:url + 'api/kanban/KanbanMap/Export',
      method: 'post',
      data
    })
  }
  export const PushData = (data) => {
    return request({
      url:url + 'api/kanban/KanbanMap/PushData',
      method: 'post',
      data
    })
  }



