<!-- 翻牌器配置 -->
<template>
  <div>
    <el-form-item label="整体" :label="`${$t('components.flop.Overall')}`">
      <avue-switch v-model="main.activeOption.whole">
      </avue-switch>
    </el-form-item>
    <el-form-item label="精度" :label="`${$t('components.flop.Accuracy')}`">
      <avue-input-number v-model="main.activeOption.decimals">
      </avue-input-number>
    </el-form-item>
    <el-form-item label="长宽" :label="`${$t('components.flop.LengthandWidth')}`">
      <avue-input-number v-model="main.activeOption.width">
      </avue-input-number>
      <avue-input-number v-model="main.activeOption.height">
      </avue-input-number>
    </el-form-item>
    <el-form-item label="外边距" :label="`${$t('components.flop.Margin')}`">
      <avue-input-number v-model="main.activeOption.marginTop"
                         :max="1000"></avue-input-number>
      <avue-input-number v-model="main.activeOption.marginLeft"
                         :max="1000"></avue-input-number>
    </el-form-item>
    <el-form-item label="内边距" :label="`${$t('components.flop.Padding')}`">
      <avue-input-number v-model="main.activeOption.paddingTop"
                         :max="1000"></avue-input-number>
      <avue-input-number v-model="main.activeOption.paddingLeft"
                         :max="1000"></avue-input-number>
    </el-form-item>
    <el-form-item label="边框" :label="`${$t('components.flop.Border')}`">
      <avue-switch v-model="main.activeOption.border">
      </avue-switch>
    </el-form-item>
    <template v-if="main.activeOption.border">
      <el-form-item label="边框颜色" :label="`${$t('components.flop.BorderColor')}`">
        <avue-input-color v-model="main.activeOption.borderColor"></avue-input-color>
      </el-form-item>
      <el-form-item label="边框宽度" :label="`${$t('components.flop.BorderWidth')}`">
        <avue-input-number v-model="main.activeOption.borderWidth"
                           :max="10">
        </avue-input-number>
      </el-form-item>
    </template>
    <el-form-item label="背景颜色" :label="`${$t('components.flop.BackgroundColor')}`"> 
      <avue-input-color v-model="main.activeOption.backgroundColor"></avue-input-color>
    </el-form-item>
    <el-form-item label="图片地址" :label="`${$t('components.flop.ImageAddress')}`">
      <img v-if="main.activeOption.backgroundBorder"
           :src="main.activeOption.backgroundBorder"
           alt=""
           width="100%" />
      <el-input clearable @clear="clear_backgroundBorder" v-model="main.activeOption.backgroundBorder">
        <div @click="main.handleOpenImg('activeOption.backgroundBorder','border')"
             slot="append">
          <i class="iconfont icon-img"></i>
        </div>
      </el-input>
    </el-form-item>
    <el-collapse accordion>
      <el-collapse-item title="内容设置" :title="`${$t('components.flop.ContentSettings')}`">
        <el-form-item label="内容" :label="`${$t('components.flop.Content')}`">
          <avue-input v-model="main.activeObj.data.value"></avue-input>
        </el-form-item>
        <el-form-item label="字体大小" :label="`${$t('components.flop.FontSize')}`">
          <avue-input-number v-model="main.activeOption.fontSize"
                             :max="200"></avue-input-number>
        </el-form-item>
        <el-form-item label="字体颜色" :label="`${$t('components.flop.FontColor')}`">
          <avue-input-color v-model="main.activeOption.color"></avue-input-color>
        </el-form-item>
        <el-form-item label="文字粗细" :label="`${$t('components.flop.FontWeight')}`">
          <avue-select v-model="main.activeOption.fontWeight"
                       :dic="dicOption.fontWeight">
          </avue-select>
        </el-form-item>
        <el-form-item label="对其方式" :label="`${$t('components.flop.Alignment')}`">
          <avue-select v-model="main.activeOption.textAlign"
                       :dic="dicOption.textAlign">
          </avue-select>
        </el-form-item>
      </el-collapse-item>
      <el-collapse-item title="前缀设置" :title="`${$t('components.flop.PrefixSettings')}`">
        <el-form-item label="不换行" :label="`${$t('components.flop.NoLineBreak')}`">
          <avue-switch v-model="main.activeOption.prefixInline"></avue-switch>
        </el-form-item>
        <el-form-item label="前缀内容" :label="`${$t('components.flop.PrefixContent')}`">
          <avue-input v-model="main.activeObj.data.prefixText"></avue-input>
        </el-form-item>
        <el-form-item label="对其方式" :label="`${$t('components.flop.Alignment')}`">
          <avue-select v-model="main.activeOption.prefixTextAlign"
                       :dic="dicOption.textAlign">
          </avue-select>
        </el-form-item>
        <el-form-item label="颜色" :label="`${$t('components.flop.Color')}`">
          <avue-input-color v-model="main.activeOption.prefixColor"></avue-input-color>
        </el-form-item>
        <el-form-item label="字体大小" :label="`${$t('components.flop.FontSize')}`">
          <avue-input-number v-model="main.activeOption.prefixFontSize"
                             :max="200">
          </avue-input-number>
        </el-form-item>
        <el-form-item label="字体行高" :label="`${$t('components.flop.FontLineHeight')}`">
          <avue-input-number v-model="main.activeOption.prefixLineHeight"
                             :max="200">
          </avue-input-number>
        </el-form-item>
      </el-collapse-item>
      <el-collapse-item title="后缀设置" :title="`${$t('components.flop.SuffixSettings')}`">
        <el-form-item label="不换行" :label="`${$t('components.flop.NoLineBreak')}`">
          <avue-switch v-model="main.activeOption.suffixInline"></avue-switch>
        </el-form-item>
        <el-form-item label="后缀内容" :label="`${$t('components.flop.SuffixContent')}`">
          <avue-input v-model="main.activeObj.data.suffixText"></avue-input>
        </el-form-item>
        <el-form-item label="对其方式" :label="`${$t('components.flop.Alignment')}`">
          <avue-select v-model="main.activeOption.suffixTextAlign"
                       :dic="dicOption.textAlign">
          </avue-select>
        </el-form-item>
        <el-form-item label="颜色" :label="`${$t('components.flop.Color')}`">
          <avue-input-color v-model="main.activeOption.suffixColor"></avue-input-color>
        </el-form-item>
        <el-form-item label="字体大小" :label="`${$t('components.flop.FontSize')}`">
          <avue-input-number v-model="main.activeOption.suffixFontSize"
                             :max="200">
          </avue-input-number>
        </el-form-item>
        <el-form-item label="字体行高" :label="`${$t('components.flop.FontLineHeight')}`">
          <avue-input-number v-model="main.activeOption.suffixLineHeight"
                             :max="200">
          </avue-input-number>
        </el-form-item>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { dicOption } from '@/option/config'
export default {
  name: 'flop',
  data () {
    return {
      dicOption: dicOption
    }
  },
  inject: ["main"],
  methods:{
    clear_backgroundBorder(val){
      setTimeout(()=>{
        this.$set(this.main.activeOption,'backgroundBorder',null)
      },300)
    }
  }
}
</script>

<style>
</style>