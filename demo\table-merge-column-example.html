<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格插件 - 合并列功能演示</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="../dist/cdn/element-ui/index.css">
    <!-- 引入插件样式 -->
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        .header {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            color: #fff;
            backdrop-filter: blur(10px);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .demo-section {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #fff;
            font-size: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: #69bfe7;
            border-radius: 2px;
        }
        .controls {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-item label {
            color: #fff;
            font-size: 14px;
            white-space: nowrap;
        }
        .control-item input, .control-item select {
            padding: 6px 10px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: #fff;
            border-radius: 4px;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: linear-gradient(45deg, #69bfe7, #5aa3d1);
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(105, 191, 231, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(105, 191, 231, 0.4);
        }
        .table-container {
            height: 500px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .mock-table {
            width: 100%;
            height: 100%;
            color: #69bfe7;
            font-family: monospace;
        }
        .mock-table-header {
            background: #050e18;
            color: #69bfe7;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 2px solid #69bfe7;
        }
        .mock-table-body {
            padding: 20px;
            text-align: center;
        }
        .data-preview {
            background: rgba(0,0,0,0.5);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #fff;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .feature-list {
            color: #fff;
            margin: 15px 0;
        }
        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #69bfe7;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 表格合并列功能演示</h1>
        <p>展示表格插件的智能列合并功能</p>
    </div>

    <div class="container">
        <!-- 功能说明 -->
        <div class="demo-section">
            <div class="section-title">📋 功能说明</div>
            <ul class="feature-list">
                <li>启用合并列功能后，相同值的相邻单元格会自动合并</li>
                <li>支持多列同时合并，适用于分组数据展示</li>
                <li>合并逻辑：向下查找相同值进行合并，向上有相同值则隐藏当前单元格</li>
                <li>空值或undefined不参与合并</li>
                <li>可以为每一列单独配置是否启用合并</li>
            </ul>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="enableMergeColumn" checked> 启用合并列功能
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="mergeWorkstation" checked> 合并工序列
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="mergeStation" checked> 合并工站列
                    </label>
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="showBorder" checked> 显示边框
                    </label>
                </div>
                <button class="btn" onclick="updateTableConfig()">应用配置</button>
                <button class="btn" onclick="generateNewData()">生成新数据</button>
            </div>
        </div>

        <!-- 数据预览 -->
        <div class="demo-section">
            <div class="section-title">📊 数据预览</div>
            <div class="data-preview" id="dataPreview">
                数据加载中...
            </div>
        </div>

        <!-- 表格演示 -->
        <div class="demo-section">
            <div class="section-title">🔗 合并列表格演示</div>
            <div class="table-container" id="mergeTable">
                <div class="mock-table">
                    <div class="mock-table-header">
                        合并列表格演示
                    </div>
                    <div class="mock-table-body">
                        <p>🔄 正在初始化表格插件</p>
                        <p>📦 使用 dist/lib/index.umd.min.js</p>
                        <p>🔗 启用合并列功能</p>
                        <p>📋 模拟生产线数据</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <!-- 引入Element UI -->
    <script src="../dist/cdn/element-ui/index.js"></script>
    <!-- 引入插件 -->
    <script src="../dist/lib/index.umd.min.js"></script>
    
    <script>
        // 模拟生产线数据
        let productionData = [];
        
        // 生成模拟数据
        function generateProductionData() {
            const workstations = ['免片线路', '外层线路', '内层显影'];
            const stations = ['前处理1线', '前处理2线', '前处理3线', '前处理4线'];
            const operators = ['费学进', '张三', '李四', '王五', '赵六'];
            
            productionData = [];
            let id = 1;
            
            workstations.forEach(workstation => {
                const stationCount = Math.floor(Math.random() * 3) + 2; // 2-4个工站
                const selectedStations = stations.slice(0, stationCount);
                
                selectedStations.forEach(station => {
                    const recordCount = Math.floor(Math.random() * 4) + 2; // 2-5条记录
                    
                    for (let i = 0; i < recordCount; i++) {
                        productionData.push({
                            id: id++,
                            workstation: workstation,
                            station: station,
                            serialNumber: `J250${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}-${String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')}`,
                            productModel: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}E${Math.floor(Math.random() * 100000)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 10)}`,
                            operator: operators[Math.floor(Math.random() * operators.length)],
                            uploadTime: `2025-07-08 ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`
                        });
                    }
                });
            });
            
            // 按工序和工站排序，确保相同值相邻
            productionData.sort((a, b) => {
                if (a.workstation !== b.workstation) {
                    return a.workstation.localeCompare(b.workstation);
                }
                return a.station.localeCompare(b.station);
            });
            
            updateDataPreview();
        }
        
        // 更新数据预览
        function updateDataPreview() {
            const preview = document.getElementById('dataPreview');
            let html = `<strong>数据总数: ${productionData.length} 条</strong><br><br>`;
            html += '<strong>数据结构:</strong><br>';
            html += JSON.stringify(productionData.slice(0, 3), null, 2);
            if (productionData.length > 3) {
                html += '<br>... 更多数据 ...';
            }
            preview.innerHTML = html;
        }
        
        // 表格配置
        let tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 12,
            scroll: false,
            border: true,
            enableMergeColumn: true,
            headerBackground: "#050e18",
            headerColor: "#69bfe7",
            headerFontSize: 14,
            headerTextAlign: "center",
            bodyColor: "#69bfe7",
            bodyFontSize: 12,
            bodyTextAlign: "center",
            nthColor: "rgba(9, 25, 44, 0.8)",
            othColor: "rgba(20, 42, 64, 0.8)",
            column: [
                { 
                    label: "工序", 
                    prop: "workstation", 
                    width: 120,
                    mergeColumn: true
                },
                { 
                    label: "工站", 
                    prop: "station", 
                    width: 120,
                    mergeColumn: true
                },
                { 
                    label: "流程卡号", 
                    prop: "serialNumber", 
                    width: 180
                },
                { 
                    label: "产品型号", 
                    prop: "productModel", 
                    width: 150
                },
                { 
                    label: "录入人员", 
                    prop: "operator", 
                    width: 100
                },
                { 
                    label: "上传时间", 
                    prop: "uploadTime", 
                    width: 140
                }
            ]
        };
        
        // 更新表格配置
        function updateTableConfig() {
            tableConfig.enableMergeColumn = document.getElementById('enableMergeColumn').checked;
            tableConfig.border = document.getElementById('showBorder').checked;
            
            // 更新列的合并配置
            tableConfig.column[0].mergeColumn = document.getElementById('mergeWorkstation').checked;
            tableConfig.column[1].mergeColumn = document.getElementById('mergeStation').checked;
            
            renderTable();
        }
        
        // 生成新数据
        function generateNewData() {
            generateProductionData();
            renderTable();
        }
        
        // 渲染表格
        function renderTable() {
            const container = document.getElementById('mergeTable');
            
            let html = '<div class="mock-table">';
            html += '<div class="mock-table-header">🔗 合并列表格演示 (共 ' + productionData.length + ' 条记录)</div>';
            html += '<div class="mock-table-body">';
            html += '<p>✅ 表格插件已加载</p>';
            html += '<p>🔗 合并列功能: ' + (tableConfig.enableMergeColumn ? '启用' : '禁用') + '</p>';
            html += '<p>📋 工序列合并: ' + (tableConfig.column[0].mergeColumn ? '启用' : '禁用') + '</p>';
            html += '<p>🏭 工站列合并: ' + (tableConfig.column[1].mergeColumn ? '启用' : '禁用') + '</p>';
            html += '<p>🎨 边框显示: ' + (tableConfig.border ? '启用' : '禁用') + '</p>';
            html += '<br>';
            html += '<strong>配置信息:</strong><br>';
            html += '<pre style="text-align: left; font-size: 10px; color: #ccc;">';
            html += JSON.stringify(tableConfig, null, 2);
            html += '</pre>';
            html += '</div>';
            html += '</div>';
            
            container.innerHTML = html;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateProductionData();
            renderTable();
            
            console.log('合并列功能演示页面已加载');
            console.log('表格配置:', tableConfig);
            console.log('生产数据:', productionData);
        });
    </script>
</body>
</html>
