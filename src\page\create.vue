<template>
  <div class="list create-screen">
    <div class="top_title">
      <el-page-header @back="goBack">
        <template slot="content">
          <div
            style="display: flex; width: 100%; justify-content: space-between"
          >
            <div class="detailspage1" style="font-size: 14px">
              <!-- 大屏设计 -->
              {{$t('page.create.BigScreenDesign')}}
            </div>
            <div style="line-height: 35px">
              <el-button @click="goBack" size="small">
                <!-- 取消 -->
                {{$t('page.create.Cancel')}}
              </el-button>
              <el-button
                :loading="loading"
                size="mini"
                type="primary"
                @click="handleSave"
                >&nbsp;
                <!-- 确定 -->
                {{$t('page.create.Confirm')}}
                </el-button
              >
            </div>
          </div>
        </template>
      </el-page-header>
    </div>
    <div class="top-bar" style="height: 280px">
      <!-- <div class="right-bar"></div>
      <div class="left-bar"></div>
      <router-link to="/"
                   class="return-btn">
        <span class="return-text">
          取消创建
        </span>
      </router-link> -->

      <div class="create-content">
        <img :src="active.backgroundUrl" alt="" />
        <!-- <avue-form :option="option"
                 v-model="form"
                 @submit="handleSave"></avue-form> -->

        <el-form
          style="width: 68%; margin-top: 8px"
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="大屏名称" :label="`${$t('page.create.BigScreenName')}`" prop="CTITLE">
            <el-input v-model="ruleForm.CTITLE"></el-input>
          </el-form-item>

          <el-form-item label="所属分组" :label="`${$t('page.create.BelongingGroup')}`" prop="CGROUP_ID">
            <el-select
              filterable
              style="width: 100%"
              v-model="ruleForm.CGROUP_ID"
              clearable
              ref="selecteltree"
              placeholder="请选择所属分组"
              :placeholder="`${$t('page.create.PleaseSelectBelongingGroup')}`"
            >
              <el-option
                style="display: none"
                v-for="item in optionList"
                :key="item.CID"
                :label="item.CNAME"
                :value="item.CID"
              ></el-option>

              <el-tree
                class="main-select-el-tree"
                :data="dataTypeList"
                node-key="CID"
                highlight-current
                :props="defaultProps"
                @node-click="handleNodeClick"
                :current-node-key="value"
                :expand-on-click-node="expandOnClickNode"
                default-expand-all
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <i class=""></i> {{ data.CNAME }}</span
                >
              </el-tree>
            </el-select>
          </el-form-item>
          <el-form-item label="大屏尺寸" :label="`${$t('page.create.BigScreenSize')}`">
            <el-input
              style="width: 38%; margin-right: 7%"
              v-model="ruleForm.CWIDTH"
            ></el-input>
            <el-input style="width: 38%" v-model="ruleForm.CHEIGHT"></el-input>
            <span>&nbsp;&nbsp;&nbsp;
              <!-- 宽*高 -->
              {{$t('page.create.WidthHeight')}}
            </span>
          </el-form-item>
          <el-form-item label="大屏密码" :label="`${$t('page.create.BigScreenPassword')}`">
            <el-input v-model="ruleForm.CPASSWORD"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="create-box" style="height: calc(100vh - 483px)">
      <div
        style="
          width: 100%;
          height: 40px;
          line-height: 39px;
          display: flex;
          justify-content: space-between;
        "
      >
        <div>
          &nbsp; &nbsp;<span style="color: #1989fa; cursor: pointer">
            <!-- 选择下面的方式进行创建 -->
            {{$t('page.create.SelecttheFollowingMethodsforCreation')}}
            </span
          >
        </div>
        <div>
          <span style="color: #333; font-size: 15px">
            <!-- 模板名称 -->
            {{$t('page.create.TemplateName')}}
            &nbsp; </span>
          <el-input
            v-model="search"
            placeholder="请搜索模板名称"
            :placeholder="`${$t('page.create.PleaseSearchforTemplateName')}`"
            size="mini"
            @keyup.enter.native="searchs"
            style="width: 200px"
          >
          </el-input>
          &nbsp;
          <el-button
            title="查询"
             :title="`${$t('page.create.Query')}`"
            size="mini"
            icon="el-icon-search"
            type="primary"  
            @click="searchs"
            >{{
          }}</el-button>
          <el-button
            title="重置"
             :title="`${$t('page.create.Reset')}`"
            type="primary"  
            size="mini"
            icon="el-icon-refresh-right"
            @click="reset"
            >{{
          }}</el-button>
          &nbsp;
        </div>
      </div>
      <!-- <div class="create-scroll"> -->
      <div
        class="content__box"
        style="height: calc(100vh - 523px); overflow-y: auto"
      >
        <div
          class="content__item"
          style="width: 320px"
          :class="{ 'content--active': count == index }"
          v-for="(item, index) in list"
          @click="handLick(item, index)"
          :key="index"
        >
          <div class="content__info">
            
            <img v-if="item.backgroundUrl" :src="item.backgroundUrl.indexOf('http') > -1 ? item.backgroundUrl :  serverUrl+item.backgroundUrl.replace('/', '')" alt="" />
          </div>
          <div class="content__main">
            <span class="content__name">{{ item.title }}</span>
            <div class="content__menulist"></div>
          </div>
        </div>
        <div style="height: 30px"></div>
      </div>
      <div class="page_vxe">
        <!-- <el-pagination
          v-if="page.total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          background
          size="mini"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-size="page.pageSize"
          handleSave
          :current-page.sync="page.pageIndex"
          :total="page.total"
        >
        </el-pagination> -->
        <vxe-pager
          align="right"
          background
          style="height: 37px"
          @page-change="pageChange"
          :current-page.sync="page.page"
          :page-size.sync="page.size"
          :total="page.total"
          :layouts="[
            'PrevJump',
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'NextJump',
            'Sizes',
            'FullJump',
            'Total',
          ]"
        ></vxe-pager>
      </div>
      <!-- </div> -->
    </div>
  </div>
</template>

<script>
import { useLanguageSetting } from "@/utils/useLanguageSetting"
import { TemplateGetPageList } from "@/api/visual";
// import { url } from "@/config";
import { url as serverUrl } from "@/config";
import {
  addObj,
  copyObj,
  updateaddObj,
  getCategory,
  GetPageListByQueryJson,
} from "@/api/visual";
export default {
  name: "CreateScreen",
  data() {
    return {
      useLanguageSettingFn: useLanguageSetting(this),
      serverUrl:serverUrl,
      optionList: [],
      expandOnClickNode: false,
      rules: {
        CTITLE: [
          { required: true, message: "请输入大屏名称", trigger: "blur" },
        ],
        CGROUP_ID: [
          { required: true, message: "请选择模板", trigger: "change" },
        ],
      },
      defaultProps: {
        label: "CNAME",
        children: "CHILDS",
      },
      search: "",
      kongbai: {
        id: 0,
        title: "空白模板",
        backgroundUrl: "/img/bg.jpg",
      },
      page: {
        page: 1,
        size: 20,
        total: 0,
      },
      list: [],
      count: 0,
      ruleForm: {
        CTITLE: "",
        CPASSWORD: "",
        CGROUP_ID: "",
        CBACKGROUND_URL: "",
        CWIDTH: "1920",
        CHEIGHT: "1080",
      },
      loading: false,
      dataTypeList: [],
    };
  },
  computed: {
    active() {
      return this.list[this.count] || {};
    },
  },

  mounted() {
    this.useLanguageSettingFn.setLanguage()
    //获取分类数据
    this.getCategory();
    this.rules= {
        CTITLE: [
          { required: true, message: this.$t('rules.PleaseEnterBigScreenName'), trigger: "blur" },
        ],
        CGROUP_ID: [
          { required: true, message: this.$t('rules.PleaseSelectTemplate'), trigger: "change" },
        ],
      }
  },
  methods: {
    //下拉树模板
    handleNodeClick(node) {
      this.ruleForm.CGROUP_ID = node.CID;
      this.$refs.selecteltree.blur();
    },
    //切换模板
    handLick(data, index) {
      this.count = index;
     // console.log(1111);
      if (data.item) {
        this.ruleForm.CTITLE = data.item.Kanban.CTITLE;
        this.ruleForm.CPASSWORD = data.item.Kanban.CPASSWORD;
        this.ruleForm.CBACKGROUND_URL = data.item.Kanban.CBACKGROUND_URL;
        //  this.ruleForm.CGROUP_ID = data.item.Kanban.CID;

        this.ruleForm.CWIDTH = "1920";
        this.ruleForm.CHEIGHT = "1080";
      }
    },
    TreeToArray(tree) {
      // 判断 tree 是否有值，无返回 []
      if (!Array.isArray(tree) || !tree.length) return [];
      let res = [];
      tree.forEach((v) => {
        // tree的每个元素都 放入到 res里面
        res.push(v);
        if (v.CHILDS) {
          // 有children 就把 children数据递归 返回  依次放到 res里面
          res.push(...this.TreeToArray(v.CHILDS));
        }
      });
      return res;
    },
    //获得模板数据
    getCategory() {
      const params = {
        queryJson: "",
      };
      getCategory(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          this.dataTypeList = res.data.Datas;
          this.optionList = this.TreeToArray(res.data.Datas);
        }
        this.getList();
      });
    },
    //分页
    pageChange(val) {
      this.page.page = val.currentPage;
      this.page.size = val.pageSize;
      this.getList();
    },
    //返回
    goBack() {
      this.$router.push("/");
      // this.$eventBus.$emit("updateKanBanList", "更新看板列表");
    },
    //搜索看板列表
    searchs() {
      var params = {
        queryJson: { keyword: this.search },
        start: this.page.page,
        length: this.page.size,
        type: 1,
      };
      this.list = [];

      window.__POWERED_BY_QIANKUN__
        ? (this.kongbai.backgroundUrl =
            window.location.origin + "/subapp/bulletin" + "/img/bg.jpg")
        : null;

      this.list = [this.kongbai];
      TemplateGetPageList(params).then((res) => {
        res = res.data;
        if (res.code === 200 && res.data.Success) {
          var arr = res.data.Datas;
          for (let i = 0; i < arr.length; i++) {
            const element = arr[i];
            this.list.push({
              id: element.Kanban.CID,
              title: element.Kanban.CTITLE,
              backgroundUrl: element.Kanban.CBACKGROUND_URL,
              item: arr[i],
            });
          }
          this.page.total = res.data.TotalRows;
        }
      });
    },
    //重置
    reset() {0
      this.search = "";
      this.page.page = 1;
      this.getList();
    },
    //获取模板列表
    getList() {
      this.list = [];

      window.__POWERED_BY_QIANKUN__
        ? (this.kongbai.backgroundUrl =
            window.location.origin + "/subapp/bulletin" + "/img/bg.jpg")
        : null;

      this.list = [this.kongbai];
      const params = {
        queryJson: {
          labelId: this.allabel,
          keyword: "",
        },
        start: this.page.page,
        length: this.page.size,
      };
      TemplateGetPageList(params).then((res) => {
        res = res.data;
        console.log(this.list);
        if (res.code === 200 && res.data.Success) {
          if (res.data.Datas && res.data.Datas.length > 0) {
            var arr = res.data.Datas;
            this.page.total = res.data.TotalRows;
            for (let i = 0; i < arr.length; i++) {
              const element = arr[i];
              this.list.push({
                id: element.Kanban.CID,
                title: element.Kanban.CTITLE,
                backgroundUrl: element.Kanban.CBACKGROUND_URL,
                item: arr[i],
              });
            }
          }
        }
      });
    },
    //保存
    handleSave() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.count == 0) {
            this.ruleForm.CBACKGROUND_URL = this.active.backgroundUrl;

            addObj(this.ruleForm).then((res) => {
              res = res.data;
              if (res.code === 200 && res.data.Success) {
                this.handleEdit({ id: res.data.Datas.Kanban.CID });
              } else {
                this.$message.error(res.message.msg || res.data.Content);
                this.loading = false;
              }
            });
          } else {
            let id;
            copyObj(this.active.id)
              .then((res) => {
                res = res.data;
                if (res.code === 200 && res.data.Success) {
                  id = res.data.Datas.CID;
                  this.ruleForm["CID"] = id;
                  return updateaddObj(this.ruleForm);
                } else {
                  this.$message.error(res.message.msg || res.data.Content);
                }
                this.loading = false;
              })
              .then(() => {
                this.loading = false;
                this.handleEdit({ id });
              });
          }
        }
      });
    },
    handleEdit(item) {
      let routeUrl = this.$router.resolve({
        path: "/build/" + item.id,
      });
      this.$eventBus.$emit("updateKanBanList", "更新看板列表");
      this.$router.push("/");

      setTimeout(() => {
        window.__POWERED_BY_QIANKUN__
          ? window.open(`/subapp${routeUrl.href}`, "_blank")
          : window.open(routeUrl.href, "_blank");
      }, 300);
    },

    // handleSave() {
    //   if (this.count == 0) {
    //     addObj(this.ruleForm).then((res) => {
    //       const id = res.data.Datas.id;
    //       this.handleEdit({ id });
    //     });
    //   } else {
    //     let id;
    //     copyObj(this.active.id)
    //       .then((res) => {
    //         id = res.data.data;
    //         return updateObj({
    //           id: id,
    //           category: this.ruleForm.category,
    //           password: this.ruleForm.CPASSWORD,
    //           status: this.ruleForm.status,
    //           title: this.ruleForm.title,
    //         });
    //       })
    //       .then(() => {
    //         this.handleEdit({ id });
    //       });
    //   }
    // },
    // handleEdit(item) {
    //   let routeUrl = this.$router.resolve({
    //     path: "/build/" + item.id,
    //   });
    //   window.location.replace(routeUrl.href);
    // },
  },
};
</script>

<style lang="scss">
@import "@/styles/list.scss";

.create-screen {
  height: 100%;
  display: flex;
  flex-direction: column;

  margin: 0;
  padding: 0;
  user-select: none;

  .top-bar {
    background: #fff;
    height: 50px;
    width: calc(100% - 20px);
    margin-bottom: 10px;
    margin-left: 10px;
    margin-right: 10px;
  }
}

.create-box {
  background: #fff;
  margin-top: 10px;
  margin-bottom: 40px;
  width: 98.8%;
  margin: 0 10px;

  .avue-tip-title {
    padding-left: 20px;
  }

  .page_vxe {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 85.9%;
    padding-right: 10px;
    background: #fff;
  }
}

// .create-scroll {
//   padding-bottom: 20px;
//   width: 3000px;
// }
.create-content {
  padding: 0px 20px;
  padding-top: 20px;
  display: flex;
  align-items: flex-start;

  box-sizing: border-box;

  img {
    width: 474px;
    display: block;
    height: 239px;
    position: relative;
    border: 1px solid #2681ff;
    box-shadow: 0 0 10px -6px #000;
  }
}

.create-dialog {
  .required {
    display: inline-block;
    margin-right: 6px;
    color: var(--datav-red-color);
  }

  .template-desc {
    margin-bottom: 20px;
    opacity: 0.8;
    overflow: hidden;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .name-title {
    margin-bottom: 10px;
  }

  .name-input {
    margin-bottom: 15px;
    height: 28px;
    line-height: 26px;
  }
}

.top_title {
  margin-bottom: 10px;

  //background-color: #fff;
  .el-page-header {
    height: 40px;
    background: #fff;
    line-height: 40px;
    padding-left: 5px;
    border-bottom: 1px solid #ddd;
    width: calc(100% - 25px);
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;

    .detailspage1 {
      display: inline-block;
    }

    .el-page-header__left {
      width: 60px;
    }

    .el-page-header__content {
      width: 100%;
      padding-right: 10px;
    }
  }
}
</style>
