<!-- 实时时间配置 -->
<template>
  <div>
    <el-form-item label="时间格式" :label="`${$t('components.datetime.TimeFormat')}`">
      <avue-select v-model="main.activeOption.format"
                   :dic="dicOption.format">
      </avue-select>
    </el-form-item>
    <el-form-item label="自定义格式" :label="`${$t('components.datetime.CustomFormat')}`">
      <avue-input v-model="main.activeOption.format">
      </avue-input>
    </el-form-item>
    <el-form-item label="字体间距" :label="`${$t('components.datetime.FontSpacing')}`">
      <avue-input-number v-model="main.activeOption.split"
                         :max="200"></avue-input-number>
    </el-form-item>
    <el-form-item label="字体大小" :label="`${$t('components.datetime.FontSize')}`">
      <avue-input-number v-model="main.activeOption.fontSize"
                         :max="200"></avue-input-number>
    </el-form-item>
    <el-form-item label="字体背景" :label="`${$t('components.datetime.FontBackground')}`">
      <avue-input-color v-model="main.activeOption.backgroundColor"></avue-input-color>
    </el-form-item>
    <el-form-item label="对其方式" :label="`${$t('components.datetime.Alignment')}`">
      <avue-select v-model="main.activeOption.textAlign"
                   :dic="dicOption.textAlign">
      </avue-select>
    </el-form-item>
    <el-form-item label="文字粗细" :label="`${$t('components.datetime.FontWeight')}`">
      <avue-select v-model="main.activeOption.fontWeight"
                   :dic="dicOption.fontWeight">
      </avue-select>
    </el-form-item>
    <el-form-item label="字体颜色" :label="`${$t('components.datetime.FontColor')}`">
      <avue-input-color v-model="main.activeOption.color"></avue-input-color>
    </el-form-item>
  </div>
</template>

<script>
import { dicOption } from '@/option/config'
export default {
  name: 'datetime',
  data () {
    return {
      dicOption: dicOption
    }
  },
  inject: ["main"]
}
</script>

<style>
</style>