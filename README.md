# CI.Web.Plugins.Bulletin - EDIT BY ANDY

## 📋 项目概述

CI.Web.Plugins.Bulletin 是一个基于 Vue.js 2.x 的高度可定制化数据大屏组件系统，专为企业级数据可视化和仪表板应用而设计。该系统集成了 Element UI、ECharts、DataV 等主流前端技术栈，提供了丰富的图表组件、交互控件和数据源支持。

### 🎯 核心特性

- **🚀 快速开发**: 提供30+预制组件，大幅缩短开发周期
- **⚡ 实时响应**: 支持动态局部刷新，毫秒级数据更新
- **📱 多端适配**: 完美适配各种拼接大屏和移动设备
- **🔌 多数据源**: 支持API、WebSocket、SQL、静态数据等多种数据源
- **🎨 高度定制**: 丰富的样式配置和自定义组件扩展能力
- **🌐 国际化**: 内置多语言支持
- **📦 开箱即用**: 提供完整的dist打包文件，可直接引入使用

## 🏗️ 系统架构

### 核心文件结构
```
src/echart/
├── packages/           # 组件包目录
│   ├── vue/           # 🆕 自定义Vue组件渲染器
│   ├── table/         # 📊 高级表格组件
│   ├── bar/           # 📊 柱状图组件
│   ├── pie/           # 🥧 饼图组件
│   ├── line/          # 📈 折线图组件
│   ├── gauge/         # ⏱️ 仪表盘组件
│   ├── progress/      # 📊 进度条组件
│   ├── text/          # 📝 文本组件
│   ├── swiper/        # 🎠 轮播图组件
│   ├── video/         # 🎥 视频播放器
│   └── ...           # 其他组件
├── common.js          # 公共混入逻辑
├── create.js          # 组件创建工厂
├── config.js          # 全局配置
└── index.js           # 组件导出入口
```

### 组件继承体系

所有组件都通过 `create.js` 工厂函数创建，自动注入以下能力：
- **bem**: BEM命名规范支持
- **common**: 公共属性和方法（数据源、样式、事件等）
- **统一前缀**: `avue-echart-` 组件名前缀

## 🧩 组件分类详解

### 📊 图表组件 (ECharts系列)
| 组件名 | 功能描述 | 特色功能 |
|--------|----------|----------|
| `bar` | 柱状图 | 支持横向/纵向、堆叠、渐变色 |
| `line` | 折线图 | 支持平滑曲线、区域填充、多Y轴 |
| `pie` | 饼图 | 支持环形图、玫瑰图、标签定制 |
| `gauge` | 仪表盘 | 支持多指针、分段颜色、动画效果 |
| `radar` | 雷达图 | 支持多维度数据展示 |
| `scatter` | 散点图 | 支持气泡图、回归线 |
| `funnel` | 漏斗图 | 支持数据转化率展示 |
| `pictorialBar` | 象形柱图 | 支持自定义图标、3D效果 |
| `wordCloud` | 词云图 | 支持自定义形状、颜色渐变 |
| `map` | 地图 | 支持区域着色、标点、热力图 |

### 📋 数据展示组件
| 组件名 | 功能描述 | 特色功能 |
|--------|----------|----------|
| `table` | 高级表格 | 🆕 支持列合并、条件格式化、滚动 |
| `flop` | 翻牌器 | 支持数字滚动动画、颜色块 |
| `progress` | 进度条 | 支持环形、线性、渐变色 |
| `text` | 文本组件 | 支持富文本、跑马灯效果 |
| `tabs` | 选项卡 | 支持动态切换、自定义样式 |

### 🎨 装饰组件
| 组件名 | 功能描述 | 特色功能 |
|--------|----------|----------|
| `borderBox` | 边框装饰 | 提供科技感边框效果 |
| `decoration` | 装饰元素 | 提供各种装饰图案 |
| `datav` | DataV组件 | 集成DataV可视化组件 |

### 🎬 媒体组件
| 组件名 | 功能描述 | 特色功能 |
|--------|----------|----------|
| `img` | 图片组件 | 支持懒加载、缩放、滤镜 |
| `video` | 视频播放器 | 支持多格式、自动播放 |
| `swiper` | 轮播图 | 支持3D效果、自动切换 |
| `iframe` | 内嵌页面 | 支持跨域、响应式 |

### 🕒 时间组件
| 组件名 | 功能描述 | 特色功能 |
|--------|----------|----------|
| `time` | 实时时间 | 支持多时区、自定义格式 |
| `datetime` | 日期时间 | 支持倒计时、节假日标记 |

### 🆕 自定义组件渲染器
| 组件名 | 功能描述 | 特色功能 |
|--------|----------|----------|
| `vue` | Vue组件渲染器 | 🚀 支持动态加载Vue单文件组件 |

## 🔥 核心特性详解

### 1. 🆕 自定义Vue组件渲染器 (`vue`)

这是系统的一个重要创新功能，允许用户通过配置动态加载和渲染Vue单文件组件。

**核心能力**:
- 动态解析Vue单文件组件的 `<template>`、`<script>`、`<style>` 部分
- 运行时编译和注册组件
- 支持组件间数据传递和事件通信
- 自动样式隔离和清理

**使用场景**:
- 需要高度定制化的业务组件
- 复杂的交互逻辑实现
- 第三方组件集成
- 动态表单和配置界面

### 2. 📊 高级表格组件 (`table`)

表格组件是系统中功能最丰富的组件之一，特别适用于数据大屏和仪表板场景。

**🆕 列合并功能**:
- 智能识别相同值的相邻单元格进行合并
- 支持多列独立配置合并规则
- 空值自动跳过，不参与合并
- 完美兼容滚动和条件格式化

**条件格式化**:
- 支持8种条件判断（大于、小于、等于、包含等）
- 支持行级和单元格级样式控制
- 支持多条件配置（Tab页管理）
- 支持自定义格式化函数

**滚动功能**:
- 智能按行滚动，支持暂停和重置
- 可配置滚动速度和间隔
- 鼠标悬停自动暂停
- 支持大数据量优化

### 3. 🔌 多数据源支持

系统支持6种数据源类型，满足不同场景需求：

| 数据源类型 | 描述 | 适用场景 |
|------------|------|----------|
| **静态数据** | 直接配置在组件中 | 演示、测试、固定数据展示 |
| **API接口** | HTTP请求获取数据 | 常规业务数据、第三方接口 |
| **SQL数据** | 直接执行SQL查询 | 数据库直连、复杂查询 |
| **WebSocket** | 实时数据推送 | 实时监控、股票行情、IoT数据 |
| **数据集** | 预定义数据集 | 标准化数据源、权限控制 |
| **全局数据源** | 组件间共享数据 | 数据联动、缓存优化 |

### 4. 🎨 样式系统

**主题支持**:
- 内置多套主题（深色、浅色、科技风等）
- 支持自定义主题配置
- 组件级样式覆盖

**响应式设计**:
- 自适应不同屏幕尺寸
- 支持拼接大屏场景
- 移动端优化

**动画效果**:
- 丰富的进入/退出动画
- 数据更新过渡动画
- 交互反馈动画

## 🚀 快速开始

### 方式一：使用打包文件（推荐）

```html
<!DOCTYPE html>
<html>
<head>
    <title>数据大屏示例</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="./dist/cdn/element-ui/index.css">
    <link rel="stylesheet" href="./dist/lib/index.css">
</head>
<body>
    <div id="app">
        <!-- 组件将在这里渲染 -->
    </div>

    <!-- 引入依赖 -->
    <script src="./dist/cdn/vue/vue.min.js"></script>
    <script src="./dist/cdn/element-ui/index.js"></script>
    
    <!-- 引入组件库 -->
    <script src="./dist/lib/index.umd.min.js"></script>
    
    <script>
        // 初始化应用
        new Vue({
            el: '#app',
            data: {
                // 组件配置
            }
        });
    </script>
</body>
</html>
```

### 方式二：Vue项目中使用

```javascript
// 引入组件
import TableComponent from './src/echart/packages/table/index.vue'
import BarComponent from './src/echart/packages/bar/index.vue'

export default {
  components: {
    TableComponent,
    BarComponent
  },
  data() {
    return {
      tableOption: {
        showHeader: true,
        index: true,
        count: 5,
        enableMergeColumn: true, // 🆕 启用列合并
        column: [
          { label: "姓名", prop: "name", width: 120 },
          { label: "部门", prop: "department", width: 150, mergeColumn: true }
        ]
      },
      tableData: [
        { name: "张三", department: "技术部" },
        { name: "李四", department: "技术部" },
        { name: "王五", department: "销售部" }
      ]
    }
  }
}
```

## 📚 演示案例

项目在 `demo/` 目录提供了丰富的演示案例：

- **demo/table-basic-example.html** - 表格基础功能演示
- **demo/table-dist-example.html** - 生产环境使用示例  
- **demo/table-merge-column-example.html** - 🆕 列合并功能演示
- **demo/simple-merge-test.html** - 简单合并测试
- **demo/real-merge-demo.html** - 真实场景合并演示

直接在浏览器中打开这些HTML文件即可查看效果。

## 🔧 技术栈

### 核心依赖
- **Vue.js 2.6.11** - 渐进式JavaScript框架
- **Element UI 2.13.0** - 基于Vue的组件库
- **ECharts** - 数据可视化图表库
- **@jiaminghi/data-view 2.10.0** - DataV数据可视化组件

### 开发工具
- **Monaco Editor 0.21.3** - 代码编辑器
- **Vue CLI 4.x** - Vue项目脚手架
- **Sass** - CSS预处理器
- **ESLint** - 代码质量检查

### 工具库
- **axios 0.19.0** - HTTP客户端
- **crypto-js 4.0.0** - 加密解密
- **dayjs 1.10.5** - 日期处理
- **vuedraggable 2.23.2** - 拖拽功能
- **clone-deep** - 深拷贝工具

## 📖 详细文档

### 组件文档
- [表格组件详细文档](src/echart/packages/table/README.md) - 包含完整的API文档和使用示例
- [Vue组件渲染器文档](src/echart/packages/vue/README.md) - 自定义组件开发指南

### 配置文档
- [数据源配置](docs/) - 各种数据源的配置方法
- [样式定制](docs/) - 主题和样式自定义指南
- [扩展开发](docs/) - 自定义组件开发教程

## 🛠️ 开发指南

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build

# 构建组件库
npm run lib
```

### 添加新组件

1. 在 `src/echart/packages/` 下创建组件目录
2. 实现组件的 `index.vue` 文件
3. 在 `src/echart/index.js` 中导出组件
4. 添加组件配置和文档

### 扩展现有组件

1. 在组件的 `option` 中添加新配置项
2. 在组件的 `methods` 中实现新功能
3. 更新组件文档和示例

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的支持：
- [Vue.js](https://vuejs.org/)
- [Element UI](https://element.eleme.io/)
- [ECharts](https://echarts.apache.org/)
- [DataV](http://datav.jiaminghi.com/)

## 🔍 组件详细说明

### 📊 表格组件 (table) - 重点功能

表格组件是本系统的核心组件之一，提供了企业级的数据展示能力。

#### 基础配置属性

```javascript
const tableConfig = {
  // 基础设置
  showHeader: true,           // 是否显示表头
  index: true,                // 是否显示序号列
  indexWidth: 80,             // 序号列宽度
  border: false,              // 是否显示边框
  roundedTable: false,        // 是否圆角表格
  count: 4,                   // 显示行数

  // 🆕 合并列功能
  enableMergeColumn: false,   // 是否启用列合并

  // 滚动设置
  scroll: false,              // 是否开启滚动
  scrollTime: 3000,           // 滚动间隔(毫秒)
  scrollSpeed: 1,             // 滚动速度

  // 表头样式
  headerBackground: "#050e18", // 表头背景色
  headerColor: "#69bfe7",      // 表头字体色
  headerFontSize: 20,          // 表头字体大小
  headerTextAlign: "center",   // 表头对齐方式
  headerColHeight: null,       // 表头高度

  // 表体样式
  bodyColor: "#69bfe7",        // 表体字体色
  bodyFontSize: 18,            // 表体字体大小
  bodyTextAlign: "center",     // 表体对齐方式
  nthColor: "#09192c",         // 奇数行背景色
  othColor: "#142a40",         // 偶数行背景色

  // 列配置
  column: [
    {
      label: "列标题",
      prop: "字段名",
      width: 120,
      hide: false,              // 是否隐藏
      wordBreak: false,         // 是否自动换行
      mergeColumn: false,       // 🆕 是否启用合并

      // 条件格式化
      condition: 3,             // 条件类型 (1-8)
      value: "比较值",
      cellbackground: "#67C23A", // 单元格背景色
      cellfont: "#ffffff",       // 单元格字体色
      rowbackground: "#67C23A",  // 行背景色
      rowfont: "#ffffff",        // 行字体色

      // 自定义格式化函数
      formatter: `(item, row) => {
        // 自定义逻辑
        return row[item.prop];
      }`,

      // 多条件配置
      editableTabsFormJSON: [
        {
          condition: 1,
          value: "其他值",
          cellbackground: "#F56C6C",
          cellfont: "#ffffff"
        }
      ]
    }
  ]
}
```

#### 🆕 列合并功能详解

列合并功能是表格组件的重要特性，特别适用于分组数据展示：

**启用方式**:
```javascript
// 1. 全局启用
tableConfig.enableMergeColumn = true;

// 2. 为特定列启用
column: [
  {
    label: "部门",
    prop: "department",
    mergeColumn: true  // 只有这一列会合并
  }
]
```

**合并规则**:
- 只合并相邻且值相同的单元格
- 空值(null/undefined/"")不参与合并
- 需要数据按合并字段预先排序
- 支持多列独立配置

**适用场景**:
- 生产线工序展示
- 部门人员分组
- 产品分类统计
- 时间段数据汇总

#### 条件格式化系统 🎯

表格插件支持强大的条件格式化功能，可以根据数据值动态设置行级和单元格级样式。

##### 支持的条件类型

| 条件值 | 条件名称 | 适用数据类型 | 示例 | 说明 |
|--------|----------|-------------|------|------|
| 1 | 大于 | 数值 | `value > 100` | 数值比较，自动类型转换 |
| 2 | 小于 | 数值 | `value < 50` | 数值比较，自动类型转换 |
| 3 | 等于 | 任意 | `status === "正常"` | 字符串严格比较 |
| 4 | 包含 | 字符串 | `name.includes("张")` | 字符串包含检查 |
| 5 | 大于等于 | 数值 | `score >= 90` | 数值比较，自动类型转换 |
| 6 | 小于等于 | 数值 | `age <= 18` | 数值比较，自动类型转换 |
| 7 | 不等于 | 任意 | `type !== "禁用"` | 字符串严格比较 |
| 8 | 不包含 | 字符串 | `!desc.includes("错误")` | 字符串不包含检查 |

##### 单条件配置示例

```javascript
column: [
  {
    label: "分数",
    prop: "score",
    condition: 5,        // 大于等于
    value: 90,           // 条件值
    cellbackground: "#27ae60",  // 单元格背景色
    cellfont: "#ffffff",        // 单元格字体色
    rowbackground: "#d5f4e6",   // 行背景色
    rowfont: "#27ae60"          // 行字体色
  }
]
```

##### 多条件配置示例

**基础多条件配置：**
```javascript
column: [
  {
    label: "状态",
    prop: "status",
    // 主条件：等于"在线"显示绿色
    condition: 3,
    value: "在线",
    cellbackground: "#27ae60",
    cellfont: "#ffffff",
    // 扩展条件：支持多个条件
    editableTabsFormJSON: [
      {
        condition: 3,     // 等于"离线"显示红色
        value: "离线",
        cellbackground: "#e74c3c",
        cellfont: "#ffffff"
      },
      {
        condition: 3,     // 等于"忙碌"显示橙色
        value: "忙碌",
        cellbackground: "#f39c12",
        cellfont: "#ffffff"
      }
    ]
  }
]
```

**🆕 相同条件类型配置：**
```javascript
column: [
  {
    label: "分数",
    prop: "score",
    // 主条件：大于0显示红色
    condition: 1,  // 大于
    value: 0,
    cellbackground: "#e74c3c",
    cellfont: "#ffffff",
    // 扩展条件：多个相同类型条件
    editableTabsFormJSON: [
      {
        condition: 1,  // 大于（相同类型）
        value: 60,     // 大于60显示橙色
        cellbackground: "#f39c12",
        cellfont: "#ffffff"
      },
      {
        condition: 1,  // 大于（相同类型）
        value: 90,     // 大于90显示绿色（优先级最高）
        cellbackground: "#27ae60",
        cellfont: "#ffffff"
      }
    ]
  }
]
```

##### 条件优先级规则 ⚡

1. **统一处理**：所有条件（主条件 + 扩展条件）都会被检查
2. **顺序优先**：按配置顺序检查，后面的条件优先级更高
3. **样式覆盖**：满足多个条件时，后面的条件样式会覆盖前面的
4. **样式独立**：不同样式属性（背景色、字体色等）可以独立设置
5. **🆕 相同条件类型**：现在支持配置多个相同类型的条件（如多个"大于"条件）

> **🔧 重要更新**：
> - v2024.1版本修复了多条件处理bug，现在所有条件都会被正确检查
> - v2024.2版本移除了条件互斥限制，支持配置多个相同类型的条件

##### 数据类型处理

- **数值比较**：自动进行类型转换，支持字符串数字与数值比较
- **字符串比较**：严格字符串比较，区分大小写
- **空值处理**：null、undefined、空字符串会被安全处理
- **错误容错**：无效配置不会导致程序崩溃，会输出警告信息

##### 最佳实践

1. **性能优化**：避免在大数据量表格中使用过多复杂条件
2. **条件设计**：确保条件互斥，避免样式冲突
3. **颜色搭配**：选择对比度适当的颜色组合，确保可读性
4. **测试验证**：使用 `demo/table-condition-test.html` 测试条件逻辑

##### 常见问题解答 ❓

**Q: 为什么条件不生效？**
A: 检查数据类型是否匹配，数值比较需要数值类型，字符串比较需要字符串类型。

**Q: 多条件时样式被覆盖怎么办？**
A: 调整条件顺序，重要的条件放在后面，或者使用不同的样式属性。

**Q: 配置了条件1和条件2，但条件2无效？**
A: 这是v2024.1之前版本的bug，已修复。现在所有条件都会被正确检查。

**Q: 可以配置多个相同类型的条件吗？**
A: 可以！v2024.2版本移除了条件互斥限制，支持配置多个相同类型的条件。

**Q: 如何处理特殊字符？**
A: 包含/不包含条件会自动转换为字符串进行匹配。

**Q: 条件判断出错怎么办？**
A: 查看浏览器控制台的错误信息，系统会输出详细的调试信息。

**Q: 如何验证多条件是否正常工作？**
A: 使用以下测试文件验证功能：
- `demo/table-multi-condition-fix-test.html` - 多条件修复测试
- `demo/table-same-condition-test.html` - 相同条件类型测试

##### 测试案例

系统提供了完整的测试案例，位于 `demo/table-condition-test.html`，包含：

- **单条件测试**：验证8种条件类型的基本功能
- **多条件测试**：验证条件优先级和样式覆盖
- **边界情况测试**：验证空值、类型转换、错误处理等

**适用场景**:
- 生产线工序展示
- 部门人员分组
- 产品分类统计
- 时间段数据汇总
- 状态监控面板
- 绩效考核表格
- 库存预警系统

### 🆕 Vue组件渲染器 (vue)

Vue组件渲染器是系统的创新功能，允许动态加载和渲染Vue单文件组件。

#### 核心原理

```javascript
// 组件内容解析
getSource(type) {
  const reg = new RegExp(`<${type}[^>]*>`);
  let content = this.content;
  let matches = content.match(reg);
  if (matches) {
    let start = content.indexOf(matches[0]) + matches[0].length;
    let end = content.lastIndexOf(`</${type}`);
    return content.slice(start, end);
  }
}

// 动态组件注册
initVue() {
  let template = this.getSource("template");
  let script = this.getSource("script");
  let styleCss = this.getSource("style");

  // 处理script部分
  if (script) {
    script = script.replace(/export default/, "return");
  }

  // 动态创建样式
  let style = document.createElement("style");
  style.innerHTML = styleCss;
  document.head.appendChild(style);

  // 编译并注册组件
  let obj = new Function(script)();
  obj.template = template;
  obj.props = { dataChart: Object };

  Vue.component(this.id, obj);
}
```

#### 使用示例

```javascript
// 配置Vue组件内容
const vueComponentConfig = {
  content: `
    <template>
      <div class="custom-component">
        <h3>{{ title }}</h3>
        <ul>
          <li v-for="item in dataChart" :key="item.id">
            {{ item.name }}: {{ item.value }}
          </li>
        </ul>
      </div>
    </template>

    <script>
    export default {
      props: ['dataChart'],
      data() {
        return {
          title: '自定义组件示例'
        }
      },
      methods: {
        handleClick(item) {
          console.log('点击了:', item);
        }
      }
    }
    </script>

    <style>
    .custom-component {
      padding: 20px;
      background: #f5f5f5;
      border-radius: 8px;
    }
    </style>
  `
}
```

### 📈 图表组件通用配置

所有ECharts图表组件都继承了通用的配置能力：

#### 数据源配置

```javascript
// 静态数据
data: [
  { name: "张三", value: 100 },
  { name: "李四", value: 200 }
]

// API数据源
dataType: 1,  // 1=API, 2=SQL, 3=WebSocket, 4=Record, 5=Custom, 6=Global
url: "https://api.example.com/data",
dataMethod: "get",
dataQuery: `(url) => ({ page: 1, size: 10 })`,
dataHeader: `(url) => ({ 'Authorization': 'Bearer token' })`,

// 数据格式化
dataFormatter: `(data, params, refs) => {
  return data.map(item => ({
    name: item.label,
    value: item.count
  }));
}`
```

#### 样式配置

```javascript
// 组件尺寸
width: 600,
height: 400,

// 主题
theme: "dark",

// 组件变换
component: {
  opacity: 1,
  scale: 1,
  perspective: 500,
  rotateX: 0,
  rotateY: 0,
  rotateZ: 0,
  animated: "fadeIn"  // 动画效果
}
```

#### 交互配置

```javascript
// 点击事件
clickFormatter: `(params, refs) => {
  console.log('图表被点击:', params);
  // 可以调用其他组件的方法
  refs.otherComponent.updateData({ filter: params.name });
}`,

// 样式格式化
stylesFormatter: `(data, params, refs) => {
  return {
    border: '2px solid #69bfe7',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
  };
}`
```

## 🎯 最佳实践

### 1. 性能优化

**大数据量处理**:
```javascript
// 表格组件
{
  count: 10,              // 限制显示行数
  scroll: true,           // 启用滚动
  scrollTime: 2000,       // 适当的滚动间隔
  enableMergeColumn: false // 大数据时关闭合并功能
}

// 图表组件
{
  dataFormatter: `(data) => {
    // 数据采样，避免渲染过多点
    return data.slice(0, 1000);
  }`
}
```

**内存管理**:
```javascript
// 组件销毁时清理资源
beforeDestroy() {
  clearInterval(this.timer);
  this.chart && this.chart.dispose();
}
```

### 2. 数据联动

**组件间通信**:
```javascript
// 主组件点击事件
clickFormatter: `(params, refs) => {
  // 更新其他组件数据
  refs.tableComponent.updateData({
    department: params.name
  });
  refs.chartComponent.updateData({
    filter: params.value
  });
}`

// 全局数据源共享
dataType: 6,  // 使用全局数据源
dataSet: 1001 // 数据集ID
```

### 3. 样式定制

**主题一致性**:
```javascript
// 定义全局颜色变量
const themeColors = {
  primary: "#69bfe7",
  success: "#67C23A",
  warning: "#E6A23C",
  danger: "#F56C6C",
  background: "#050e18"
};

// 在组件中使用
headerBackground: themeColors.background,
headerColor: themeColors.primary
```

**响应式设计**:
```javascript
// 根据屏幕尺寸调整
stylesFormatter: `(data, params, refs) => {
  const screenWidth = window.innerWidth;
  return {
    fontSize: screenWidth > 1920 ? '16px' : '14px',
    padding: screenWidth > 1920 ? '20px' : '10px'
  };
}`
```

### 4. 错误处理

**数据容错**:
```javascript
dataFormatter: `(data, params, refs) => {
  try {
    if (!Array.isArray(data)) {
      console.warn('数据格式错误，使用默认数据');
      return [];
    }
    return data.map(item => ({
      name: item.name || '未知',
      value: Number(item.value) || 0
    }));
  } catch (error) {
    console.error('数据处理错误:', error);
    return [];
  }
}`
```

**API错误处理**:
```javascript
// 在common.js中已内置错误处理
// 可以通过全局事件监听错误
this.$eventBus.$on('api-error', (error) => {
  this.$message.error('数据加载失败: ' + error.message);
});
```

## 🔧 常见问题与解决方案

### Q1: 表格滚动不流畅怎么办？
**A**:
- 调整 `scrollSpeed` (建议1-3) 和 `scrollTime` (建议1000-3000ms)
- 减少显示行数 `count`
- 关闭复杂的格式化函数
- 大数据量时关闭 `enableMergeColumn`

### Q2: 如何实现表格数据的实时更新？
**A**:
```javascript
// 方式1: WebSocket数据源
{
  dataType: 3,
  wsUrl: "ws://localhost:8080/data",
  dataQuery: `(url) => ({ channel: 'table-data' })`
}

// 方式2: 定时API轮询
{
  dataType: 1,
  url: "https://api.example.com/data",
  time: 5000  // 5秒刷新一次
}

// 方式3: 全局数据源
{
  dataType: 6,
  dataSet: 1001,
  globalDataConfig: {
    globalDataSource: [{ time: 3000 }]  // 3秒刷新
  }
}
```

### Q3: 条件格式化不生效怎么办？
**A**:
- 检查 `condition` 和 `value` 字段是否正确设置
- 确保数据类型匹配（数字比较用数字，字符串比较用字符串）
- 检查字段名 `prop` 是否与数据中的字段一致
- 多条件配置时检查是否有冲突

### Q4: 如何隐藏某一列？
**A**:
```javascript
// 方式1: 设置hide属性
{ label: "隐藏列", prop: "hidden", hide: true }

// 方式2: 设置宽度为1（视觉隐藏）
{ label: "隐藏列", prop: "hidden", width: 1 }
```

### Q5: 自定义Vue组件不显示怎么办？
**A**:
- 检查 `content` 中的Vue语法是否正确
- 确保 `<template>`、`<script>`、`<style>` 标签完整
- 检查浏览器控制台是否有JavaScript错误
- 确认 `dataChart` 数据是否正确传递

### Q6: 图表不显示或显示异常？
**A**:
- 检查容器尺寸 `width` 和 `height` 是否设置
- 确认数据格式是否符合图表要求
- 检查 `dataFormatter` 函数是否正确
- 确认ECharts主题是否加载

### Q7: 如何实现组件间的数据联动？
**A**:
```javascript
// 在点击事件中更新其他组件
clickFormatter: `(params, refs) => {
  // 获取其他组件引用
  const tableRef = refs.tableComponent;
  const chartRef = refs.chartComponent;

  // 更新数据
  tableRef.updateData({ filter: params.name });
  chartRef.updateData({ category: params.value });
}`
```

### Q8: 如何优化大屏性能？
**A**:
- 合理设置组件刷新频率，避免过于频繁的数据更新
- 使用全局数据源减少重复请求
- 大数据量时启用数据分页或虚拟滚动
- 避免在格式化函数中进行复杂计算
- 及时清理定时器和事件监听器

## 📋 配置速查表

### 表格组件快速配置

```javascript
// 基础表格
const basicTable = {
  showHeader: true,
  index: true,
  count: 5,
  border: true,
  column: [
    { label: "姓名", prop: "name", width: 120 },
    { label: "年龄", prop: "age", width: 80 }
  ]
};

// 滚动表格
const scrollTable = {
  ...basicTable,
  scroll: true,
  scrollTime: 2000,
  scrollSpeed: 2
};

// 合并列表格
const mergeTable = {
  ...basicTable,
  enableMergeColumn: true,
  column: [
    { label: "部门", prop: "dept", mergeColumn: true },
    { label: "姓名", prop: "name" }
  ]
};

// 条件格式化表格
const conditionalTable = {
  ...basicTable,
  column: [
    {
      label: "状态",
      prop: "status",
      condition: 3,  // 等于
      value: "正常",
      cellbackground: "#67C23A",
      cellfont: "#ffffff"
    }
  ]
};
```

### 图表组件快速配置

```javascript
// 柱状图
const barChart = {
  width: 600,
  height: 400,
  dataType: 1,
  url: "/api/bar-data",
  dataFormatter: `(data) => ({
    categories: data.map(item => item.name),
    series: [{ data: data.map(item => item.value) }]
  })`
};

// 饼图
const pieChart = {
  width: 400,
  height: 400,
  dataType: 1,
  url: "/api/pie-data",
  dataFormatter: `(data) => data.map(item => ({
    name: item.name,
    value: item.value
  }))`
};

// 实时折线图
const realtimeLineChart = {
  width: 800,
  height: 400,
  dataType: 3,  // WebSocket
  wsUrl: "ws://localhost:8080/realtime",
  time: 1000    // 1秒更新
};
```

### 数据源配置模板

```javascript
// API数据源
const apiDataSource = {
  dataType: 1,
  dataMethod: "post",
  url: "https://api.example.com/data",
  dataQuery: `(url) => ({
    page: 1,
    size: 20,
    filters: { status: 'active' }
  })`,
  dataHeader: `(url) => ({
    'Authorization': 'Bearer ' + localStorage.getItem('token'),
    'Content-Type': 'application/json'
  })`,
  dataFormatter: `(data, params, refs) => {
    return data.result.map(item => ({
      id: item.id,
      name: item.title,
      value: item.count
    }));
  }`
};

// WebSocket数据源
const wsDataSource = {
  dataType: 3,
  wsUrl: "ws://localhost:8080/realtime",
  dataQuery: `(url) => ({
    channel: 'dashboard',
    token: localStorage.getItem('token')
  })`,
  dataFormatter: `(data) => {
    if (data.type === 'update') {
      return data.payload;
    }
    return [];
  }`
};

// 全局数据源
const globalDataSource = {
  dataType: 6,
  dataSet: 1001,
  globalDataConfig: {
    globalDataSource: [{ time: 5000 }]  // 5秒刷新
  },
  dataFormatter: `(data) => {
    return data.filter(item => item.enabled);
  }`
};
```

## 🎨 样式定制指南

### 主题色彩配置

```javascript
// 深色主题
const darkTheme = {
  headerBackground: "#1f2937",
  headerColor: "#f9fafb",
  bodyColor: "#d1d5db",
  nthColor: "#374151",
  othColor: "#4b5563"
};

// 蓝色科技主题
const techTheme = {
  headerBackground: "#0f172a",
  headerColor: "#38bdf8",
  bodyColor: "#cbd5e1",
  nthColor: "#1e293b",
  othColor: "#334155"
};

// 绿色清新主题
const greenTheme = {
  headerBackground: "#064e3b",
  headerColor: "#6ee7b7",
  bodyColor: "#a7f3d0",
  nthColor: "#065f46",
  othColor: "#047857"
};
```

### 响应式样式

```javascript
// 响应式字体大小
stylesFormatter: `(data, params, refs) => {
  const width = window.innerWidth;
  let fontSize = 14;

  if (width >= 1920) fontSize = 18;
  else if (width >= 1440) fontSize = 16;
  else if (width >= 1024) fontSize = 14;
  else fontSize = 12;

  return { fontSize: fontSize + 'px' };
}`

// 响应式布局
stylesFormatter: `(data, params, refs) => {
  const isMobile = window.innerWidth < 768;
  return {
    padding: isMobile ? '10px' : '20px',
    fontSize: isMobile ? '12px' : '16px',
    flexDirection: isMobile ? 'column' : 'row'
  };
}`
```

### 动画效果配置

```javascript
// 组件进入动画
component: {
  animated: "fadeInUp",     // 淡入上移
  // 可选: fadeIn, fadeInDown, fadeInLeft, fadeInRight,
  //      bounceIn, zoomIn, slideInUp, etc.
}

// 自定义CSS动画
stylesFormatter: `() => ({
  animation: 'pulse 2s infinite',
  transition: 'all 0.3s ease'
})`
```

## 🚀 高级用法

### 1. 动态组件配置

```javascript
// 根据数据动态生成列配置
dataFormatter: `(data, params, refs) => {
  // 动态生成表格列
  if (data.length > 0) {
    const columns = Object.keys(data[0]).map(key => ({
      label: key.toUpperCase(),
      prop: key,
      width: 120
    }));

    // 更新组件配置
    refs.currentComponent.option.column = columns;
  }

  return data;
}`

// 根据条件切换图表类型
dataFormatter: `(data, params, refs) => {
  const chartRef = refs.chartComponent;

  if (data.length > 10) {
    // 数据多时使用折线图
    chartRef.name = 'line';
  } else {
    // 数据少时使用柱状图
    chartRef.name = 'bar';
  }

  return data;
}`
```

### 2. 复杂数据处理

```javascript
// 数据聚合和计算
dataFormatter: `(data, params, refs) => {
  // 按部门分组统计
  const grouped = data.reduce((acc, item) => {
    const dept = item.department;
    if (!acc[dept]) {
      acc[dept] = { name: dept, count: 0, total: 0 };
    }
    acc[dept].count++;
    acc[dept].total += item.value;
    return acc;
  }, {});

  // 计算平均值
  return Object.values(grouped).map(group => ({
    ...group,
    average: Math.round(group.total / group.count)
  }));
}`

// 时间序列数据处理
dataFormatter: `(data, params, refs) => {
  // 按小时聚合数据
  const hourlyData = data.reduce((acc, item) => {
    const hour = new Date(item.timestamp).getHours();
    if (!acc[hour]) acc[hour] = [];
    acc[hour].push(item.value);
    return acc;
  }, {});

  // 计算每小时平均值
  return Object.entries(hourlyData).map(([hour, values]) => ({
    time: hour + ':00',
    value: values.reduce((sum, v) => sum + v, 0) / values.length
  }));
}`
```

### 3. 高级交互功能

```javascript
// 双击编辑功能
clickFormatter: `(params, refs) => {
  if (params.clickCount === 2) {
    // 双击进入编辑模式
    const tableRef = refs.tableComponent;
    tableRef.enterEditMode(params.rowIndex, params.columnIndex);
  }
}`

// 右键菜单
clickFormatter: `(params, refs) => {
  if (params.event.button === 2) { // 右键
    // 显示上下文菜单
    const menu = [
      { label: '查看详情', action: 'view' },
      { label: '编辑', action: 'edit' },
      { label: '删除', action: 'delete' }
    ];

    refs.contextMenu.show(params.event.clientX, params.event.clientY, menu);
  }
}`

// 拖拽排序
dragFormatter: `(params, refs) => {
  const { oldIndex, newIndex } = params;
  const tableRef = refs.tableComponent;

  // 更新数据顺序
  const newData = [...tableRef.dataChart];
  const item = newData.splice(oldIndex, 1)[0];
  newData.splice(newIndex, 0, item);

  tableRef.updateData(newData);
}`
```

## 📊 项目统计

### 组件数量统计
- **图表组件**: 12个 (bar, line, pie, gauge, radar, scatter, funnel, pictorialBar, wordCloud, map, common, rectangle)
- **数据展示组件**: 5个 (table, flop, progress, text, tabs)
- **装饰组件**: 3个 (borderBox, decoration, datav)
- **媒体组件**: 4个 (img, imgBorder, video, swiper, iframe)
- **时间组件**: 2个 (time, datetime)
- **自定义组件**: 2个 (vue, clappr)
- **总计**: 30+ 个组件

### 功能特性统计
- **数据源类型**: 6种 (静态、API、SQL、WebSocket、数据集、全局)
- **条件格式化**: 8种条件判断
- **动画效果**: 20+ 种CSS动画
- **主题支持**: 多套内置主题
- **国际化**: 支持多语言
- **浏览器兼容**: 支持现代浏览器

## 📈 版本信息

### 当前版本: v2.0.0

#### 🆕 新增功能
- **表格列合并功能**: 智能合并相同值的相邻单元格
- **Vue组件渲染器**: 支持动态加载Vue单文件组件
- **多条件格式化**: 支持Tab页管理多个条件配置
- **全局数据源**: 组件间数据共享和缓存优化
- **响应式设计**: 更好的移动端和大屏适配

#### 🔧 改进优化
- **性能优化**: 优化大数据量渲染性能
- **内存管理**: 改进组件销毁时的资源清理
- **错误处理**: 增强数据容错和错误提示
- **文档完善**: 提供详细的API文档和示例

#### 🐛 问题修复
- 修复表格滚动在某些情况下的卡顿问题
- 修复条件格式化在数据更新时的显示异常
- 修复WebSocket连接断开后的重连机制
- 修复组件销毁时可能的内存泄漏

### 版本历史

#### v1.9.0 (2024-10-24)
- 新增数据模型其他动态配置参数支持
- 优化图表组件的数据处理逻辑
- 改进API错误处理机制

#### v1.8.0 (2024-09-23)
- 重构事件绑定机制
- 新增样式格式化函数支持
- 优化组件间通信方式

#### v1.7.0 (2024-07-08)
- 新增表格组件高级功能
- 完善组件配置界面
- 增加更多演示案例

## 🔮 未来规划

### 短期计划 (v2.1.0)
- [ ] **3D图表支持**: 集成Three.js实现3D可视化
- [ ] **地图增强**: 支持更多地图类型和交互
- [ ] **移动端优化**: 专门的移动端组件适配
- [ ] **主题编辑器**: 可视化主题定制工具

### 中期计划 (v2.2.0)
- [ ] **组件市场**: 第三方组件插件系统
- [ ] **可视化编辑器**: 拖拽式大屏设计器
- [ ] **数据源管理**: 统一的数据源配置中心
- [ ] **权限控制**: 细粒度的组件权限管理

### 长期计划 (v3.0.0)
- [ ] **Vue 3.x支持**: 升级到Vue 3.x版本
- [ ] **TypeScript重写**: 提供完整的类型支持
- [ ] **微前端架构**: 支持微前端应用集成
- [ ] **云端部署**: 提供SaaS版本服务

## 🤝 社区贡献

### 贡献者
感谢所有为项目做出贡献的开发者：

- **Andy Huang** - 表格组件列合并功能开发 (2025-07-08)
- **开发团队** - 核心架构设计和实现
- **测试团队** - 质量保证和用户体验优化

### 如何贡献

我们欢迎各种形式的贡献：

1. **🐛 报告Bug**: 在Issues中详细描述问题
2. **💡 功能建议**: 提出新功能或改进建议
3. **📝 文档改进**: 完善文档和示例
4. **🔧 代码贡献**: 提交Pull Request
5. **🎨 设计贡献**: 提供UI/UX设计建议

### 贡献指南

1. **Fork项目** 到你的GitHub账户
2. **创建分支** `git checkout -b feature/amazing-feature`
3. **提交更改** `git commit -m 'Add amazing feature'`
4. **推送分支** `git push origin feature/amazing-feature`
5. **创建Pull Request** 详细描述你的更改

### 代码规范

- 遵循ESLint配置的代码风格
- 组件命名使用PascalCase
- 方法命名使用camelCase
- 添加必要的注释和文档
- 确保所有测试通过

## 📞 技术支持

### 获取帮助

1. **📖 查看文档**: 首先查看本README和组件文档
2. **🔍 搜索Issues**: 查看是否有类似问题已被解决
3. **💬 提交Issue**: 详细描述问题和复现步骤
4. **📧 联系团队**: 发送邮件到技术支持邮箱

### 常用链接

- **项目主页**: [CI.Web.Plugins.Bulletin](.)
- **在线演示**: [Demo页面](demo/)
- **API文档**: [组件API文档](src/echart/packages/)
- **更新日志**: [CHANGELOG.md](CHANGELOG.md)

### 技术交流

- **技术讨论**: 欢迎在Issues中讨论技术问题
- **功能建议**: 通过Issues提交功能需求
- **Bug反馈**: 详细描述问题现象和环境信息

## 📄 许可证

本项目采用 **MIT License** 开源许可证。

```
MIT License

Copyright (c) 2024 CI.Web.Plugins.Bulletin

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🙏 致谢

特别感谢以下开源项目和技术社区的支持：

### 核心依赖
- **[Vue.js](https://vuejs.org/)** - 渐进式JavaScript框架
- **[Element UI](https://element.eleme.io/)** - 基于Vue的组件库
- **[ECharts](https://echarts.apache.org/)** - 强大的数据可视化库
- **[DataV](http://datav.jiaminghi.com/)** - 酷炫的数据可视化组件

### 工具库
- **[Monaco Editor](https://microsoft.github.io/monaco-editor/)** - 代码编辑器
- **[Axios](https://axios-http.com/)** - HTTP客户端
- **[Day.js](https://day.js.org/)** - 轻量级日期库
- **[Crypto-JS](https://cryptojs.gitbook.io/)** - 加密解密库

### 开发工具
- **[Vue CLI](https://cli.vuejs.org/)** - Vue项目脚手架
- **[Sass](https://sass-lang.com/)** - CSS预处理器
- **[ESLint](https://eslint.org/)** - 代码质量检查
- **[Webpack](https://webpack.js.org/)** - 模块打包工具

### 设计灵感
- **[Ant Design](https://ant.design/)** - 设计语言参考
- **[Material Design](https://material.io/)** - 设计规范参考
- **[Dribbble](https://dribbble.com/)** - 设计灵感来源

---

## 📋 快速导航

- [🏠 返回顶部](#ciwebpluginsbulletin---自定义vue组件系统)
- [🚀 快速开始](#-快速开始)
- [📊 表格组件](#-表格组件-table---重点功能)
- [🆕 Vue组件渲染器](#-vue组件渲染器-vue)
- [🔧 常见问题](#-常见问题与解决方案)
- [📋 配置速查表](#-配置速查表)
- [🎨 样式定制](#-样式定制指南)
- [🚀 高级用法](#-高级用法)

---

**⚠️ 重要提示**:
- 测试案例全放到 `demo/` 目录下
- 使用当前打包好的 `dist/` 文件
- 参考其它案例使用方法
- 详细文档请查看各组件的README.md文件

**🎯 项目目标**: 为企业级数据可视化提供高效、灵活、易用的Vue组件解决方案。
