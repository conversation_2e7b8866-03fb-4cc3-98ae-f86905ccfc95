<template>
      <div>
        <!-- {{ form }} -->
        <el-form-item v-for="(item,index) in form" :key="item.field"  :prop="item.field" :label="item.title">
            <template v-if="item.itemRender.name=='Input'">
                <el-input  @change="dataChangeFn()" clearable v-model="dataModelOthersCopy[item.field]" v-bind="item.itemRender.props" ></el-input>
            </template>
            <template v-else-if="item.itemRender.name=='Select'">
                <el-select clearable @change="dataChangeFn()" v-model="dataModelOthersCopy[item.field]" v-bind="item.itemRender.props">
                    <template v-if="item.itemRender.options">
                         <!-- 自定义下拉数据 -->
                        <el-option v-for="option in item.itemRender.options" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </template>
                    <template v-else>
                         <!-- 默认下拉数据 -->
                      <el-option v-for="option in defaultSelectList" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </template>
                </el-select>
            </template>
            <template v-else-if="item.itemRender.name=='ColorPicker'">
              <div class="flex items-center">
                <el-input style="width: 80%;"  @change="dataChangeFn()" clearable v-model="dataModelOthersCopy[item.field]" ></el-input>
              <el-color-picker
                 @change="dataChangeFn()"
                 v-bind="item.itemRender.props"
                v-model="dataModelOthersCopy[item.field]"
                >
            </el-color-picker>
              </div>
              
            </template>
            <template v-else-if="item.itemRender.name=='Slider'">
              <el-slider
                 @change="dataChangeFn()"
                 v-bind="item.itemRender.props"
                v-model="dataModelOthersCopy[item.field]"
                >
            </el-slider>
            </template>
            <template v-else-if="item.itemRender.name=='Switch'">
              <el-switch
                 @change="dataChangeFn()"
                 v-bind="item.itemRender.props"
                v-model="dataModelOthersCopy[item.field]"
                >
            </el-switch>
            </template>
            
            <template v-else-if="item.itemRender.name=='InputNumber'">
              <el-input-number style="width: 100%;" clearable @change="dataChangeFn()" v-model="dataModelOthersCopy[item.field]" v-bind="item.itemRender.props"></el-input-number>
            </template>
            
        </el-form-item>
      </div>
</template>
<script>
// 自定义 组件列表 组件内置控件属性 方式
export default {
  name: 'configInfoFormRender',
  props: {
    // 对象模型
    dataModelOthers: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 默认下拉模型数据列表
    defaultSelectList: {
      type: Array,
      default: () => {
        return []
      }
    },
    formList: {
      type: Object,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
     dataModelOthersCopy:{},
      form: [
            // 目前支持类型如下，其它自由扩展【注意：options为空，那么绑定的是默认的数据模型】
            // { "field": "color", "title": "颜色","itemRender": { "name": "ColorPicker", "props": { "show-alpha": true } } },
            // { "field": "dataLength", "title": "长度","itemRender": { "name": "Slider", "props": { "min": 0,"max": 100 } } },
            // { "field": "isOnOrNot", "title": "开关","itemRender": { "name": "Switch", "props": { "active-text": "开","inactive-text": "关" } } },
            // { "field": "name", "title": "名称","itemRender": { "name": "Input", "props": { "placeholder": "请输入名称" } } },
            // { "field": "model", "title": "数据模型", "itemRender":  { "name": "Select","props": { "placeholder": "请选择模型" } } },
            // { "field": "sex", "title": "性别", "itemRender":  { "name": "Select","options": [{ "value": "0", "label": "女" }, { "value": "1", "label": "男" }],"props": { "placeholder": "请选择性别" } } },
            // { "field": "age", "title": "年龄", "itemRender":  { "name": "InputNumber","props": { "placeholder": "请输入年龄"}}}    
            ]
    }
  },
  watch: {
    formList: {
      handler (val) {
        this.form = val
      },
      deep: true
    },
    dataModelOthers: {
      handler (val) {
        this.dataModelOthersCopy = val
    },
    deep: true
  },
},
  mounted(){
    this.dataModelOthersCopy = this.dataModelOthers
    this.form = this.formList
  },
  methods: {
    dataChangeFn () {
       // debugger
      this.$emit('change', this.dataModelOthers)
    }
  }
}
</script>