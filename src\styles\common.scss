* {
  margin: 0;
  padding: 0;
}

html,
body,
#appbulletin {
  height: 100%;
}

#body {
 
  overflow: hidden;
  font-family: "PingFang SC","Microsoft YaHei","Helvetica Neue",Arial,sans-serif;
  -webkit-font-smoothing: antialiased;




::-webkit-scrollbar-track-piece {
  background-color: transparent;
}
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: hsla(220, 4%, 58%, .3);
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
}
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: hsla(220, 4%, 58%, .3);
}

a {
  text-decoration: none;
}

img {
  user-select: none;
}

.img{
  margin-right: 5px;
}
.block{
  margin: 0 auto;
  width:90%;
  display: block;
}

a{
  text-decoration: none;
  font-size: 12px;
  color:#409EFF;
}


.menu__tabs{
  box-sizing: border-box;
  .el-tabs__header{
    background: #2a2a2b;
    border-radius: 5px;
  }
  .el-tabs__item{
    padding:0 5px ;
    color:#fff;
    font-size: 16px;
    text-align: left;
    box-sizing: border-box;
    i{
      margin-right: 5px;
    }
  }
  .is-active {
    color: #409EFF;
  }
  .el-tabs__nav-wrap::after,.el-tabs__active-bar{
    display: none;
  }
}
.el-message-box__wrapper {
  background-color: #000;
}
.el-select-dropdown {
  border-radius: 0;
  border:none;
  color: #bcc9d4;
  background-color: #27343e;
  color: #bcc9d4;
}
.el-select-dropdown__item{
  font-size: 12px;
}
.el-select-dropdown__item.selected,.el-select-dropdown__item.hover{
  font-weight: normal;
  background-color: rgba(0,192,222,.1);
}
.el-select-dropdown__item.hover{
  color: #fff;
}
.el-select-dropdown__item.selected{
  color: #fff;
}
.el-input-group__append, .el-input-group__prepend{
  background-color: #000;
  border-color: #fff;
  border: 0;
}
.el-color-picker--mini, .el-color-picker--mini .el-color-picker__trigger{
  width: 25px;
  height: 25px;
}
.el-collapse-item__header.is-active{
  color:#fff;
}

.el-collapse-item__wrap{
  background-color:#000;
  border-bottom: none;
}
.el-radio__input, .params .el-radio__label,.params input{
  font-size: 12px;
}
.avue-crud{
  .el-table th, .el-table tr,.el-table, .el-table__expanded-cell {
    background-color: transparent ;
    color:#fff ;
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td{
    background-color: #333;
  }
  .el-table td, .el-table th.is-leaf {
    border-color: transparent;
  }
  .hover-row td,.hover-row th{
    background-color: transparent ;
    border-bottom: none;
  }
  .el-table__fixed-right::before, .el-table__fixed::before,.el-table::before{
    display: none;
  }
}
.avue-empty__desc{
  color:#fff;
}
.el-form-item__label{
  color:#fff ;
}

.el-dialog ,.avue-group__item{
  background: #1b1e25;
}
.el-message-box{
  background: #1b1e25;
  border-color:  #1b1e25;
}
.el-message-box__title{
  color:#fff;
}
.el-dialog__title{
  color:#fff;
  font-size: 16px;
}
.el-collapse-item__arrow.is-active{
  color:#fff;
}
.el-collapse{
  border-top:none;
  border-bottom: none;
}
input{
  border-width: 2px;
  border-radius: 0;
}

.avue-crud{
  width: 90%;
  margin-top: 10px;
  &__menu{
    background-color: inherit;
  }
}
.el-radio__label{
  font-size: 14px;
  line-height: 25px;
}
.el-radio-group{
  margin-bottom: 10px;
}
.el-color-picker__trigger,.el-dialog .el-color-picker__trigger{
  border:none;
}
// .el-collapse-item__arrow{
//   position: absolute;
//   left:10px;
//   line-height: 40px;
//   color:#bcc9d4;
// }


.el-form-item{
  margin-top: 10px;
}
.el-pagination__total{
  color:#fff;
}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
  background-color:transparent;
  color: #fff;
}
.el-pagination.is-background .el-pager li:not(.disabled).active{
  background-color: transparent;
  border:1px solid #409EFF;
}
.el-form-item__label,.el-dialog .el-form-item__label{
  color:#fff;
  padding-left: 20px;
  font-size: 14px;
}
.el-form-item__content, .el-dialog  .el-form-item__content{
  padding-right: 20px;
}
.el-checkbox__inner,input,.el-slider__runway,textarea,
.el-dialog input,.el-switch__core,.el-dialog .el-slider__runway,.el-dialog textarea{
  background-color: #000 ;
  color:#fff ;
  border: 0;
  // border-color:#fff ;
}
.el-switch.is-checked .el-switch__core {
  border-color: #409EFF  ;
  background-color: #409EFF  ;
}
.el-button{
  border-radius: 5px;
}
.el-radio__input.is-checked+.el-radio__label{
  color:#2681ff;
}
.el-radio__input.is-checked .el-radio__inner{
  border-color: #2681ff;
  background: #2681ff;
}
.el-button--primary{
  background-color: transparent;
  color:#2681ff;
  border-color:#2681ff;
  &:hover,&:focus{
    color: #fff;
    background-color: #2f3f62;
    border-color: #4f9eff;
  }
}
.el-button--danger{
  background-color: transparent;
  color:#f56c6c;
  border-color:#f56c6c;
  &:hover,&:focus{
    color: #fff;
    background-color: #2f3f62;
    border-color: #f56c6c;
  }
}
.el-button--success{
  background-color: transparent;
  color:#67C23A;
  border-color:#67C23A;
  &:hover,&:focus{
    color: #fff;
    background-color: #2f3f62;
    border-color: #67C23A;
  }
}
.el-button--default{
  background-color: transparent;
  color:#859094;
  border-color:#859094;
  &:hover,&:focus{
    color: #fff;
    background-color: #2f3f62;
    border-color: #859094;
  }
}
.el-collapse-item__content{
  padding: 0;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover{
  background-color: initial;
  color:#fff;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected{
  background-color: initial;
  color:#fff;
}
.el-dialog__title{
  color:#999 ;
}
.el-collapse-item__header{
  padding-left: 20px;
  height: 40px;
  line-height: 40px;
  background: transparent;
  color:#fff;
  font-weight: 300;
  font-size: 16px;
  border-color: #282e3a;
}
 
.el-input-number__decrease,.el-input-number__increase{
  border-color:#333 ;
}
.icon-gauge{
  font-size: 12px ;
}
.el-card{
  background-color: transparent;
}
.el-form-item--mini .el-color-picker--mini, .el-form-item--mini .el-color-picker--mini .el-color-picker__trigger{
  width: 22px;
  height: 22px;
}
.avue-dialog .el-dialog__header{
  border-color: #333;
}
.avue-dialog__footer{
  background-color: transparent;
  border-color:#333;

}
.avue-tip-title {
  font-size: 14px;
  font-weight: 700;
  color: #2681ff;
  padding: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  line-height: 40px;
}
.avue-draggable__item{
  height: 100%;
}
.refer-select {
  left: 200px;
  top: 200px;
  width: 100px;
  height: 100px;
  position: absolute;
  border: 1px solid #409eff;
  background-color: #2f3f62;
  border-radius: 5px;
  z-index: 1024;
  opacity: 0.4;
}
.editor-fullscreen{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1003;
    padding: 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #333;
}
}
