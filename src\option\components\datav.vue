<!-- 通用形配置 -->
<template>
  <div>
    <el-form-item label1="在线文档" :label="`${$t('page.build.DOCOnLine')}`">
      <a href="http://datav.jiaminghi.com/guide/"
         target="_blank">
         <!-- 点击查看  -->
         {{ $t('page.build.ClickToView') }}
        </a>
    </el-form-item>
    <el-form-item label1="模块名称" :label="`${$t('page.build.ModuleName')}`">
      <avue-input v-model="main.activeOption.is"></avue-input>
    </el-form-item>
    <el-form-item label1="配置列表" :label="`${$t('page.build.ConfigurationList')}`">
      <el-button size="small"
                 type="primary"
                 @click="openCode">
                 <!-- 编辑 -->
                 {{ $t('page.build.Edit') }}
                </el-button>
    </el-form-item>
    <codeedit @submit="codeClose"
              title="配置列表" :title="`${$t('page.build.ConfigurationList')}`"
              v-model="code.obj"
              v-if="code.box"
              :type="code.type"
              :visible.sync="code.box"></codeedit>
  </div>
</template>

<script>
import codeedit from '../../page/group/code';
export default {
  name: 'datav',
  inject: ["main"],
  data () {
    return {
      code: {
        box: false,
        obj: {},
      }
    }
  },
  components: {
    codeedit
  },
  methods: {
    codeClose (value) {
      this.main.activeObj[this.code.type] = value;
    },
    openCode () {
      this.code.type = 'echartFormatter';
      this.code.obj = this.main.activeObj[this.code.type];
      this.code.box = true;
    },
  }
}
</script>

<style>
</style>